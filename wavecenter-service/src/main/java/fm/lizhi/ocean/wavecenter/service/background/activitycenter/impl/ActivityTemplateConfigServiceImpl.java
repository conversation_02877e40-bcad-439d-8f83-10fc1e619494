package fm.lizhi.ocean.wavecenter.service.background.activitycenter.impl;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.activitycenter.bean.ActivityToolsInfoBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityBaseConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityTemplatePageBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.request.*;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseActivityConfigBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseCreateActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplate;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.response.ResponseGetActivityTemplateShelfStatus;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.service.ActivityTemplateConfigService;
import fm.lizhi.ocean.wavecenter.api.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityBaseEnumConfigManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityTemplateManager;
import fm.lizhi.ocean.wavecenter.service.background.activitycenter.manager.ActivityToolsConfigManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * 活动模板配置服务实现
 */
@ServiceProvider
@Slf4j
public class ActivityTemplateConfigServiceImpl implements ActivityTemplateConfigService {

    @Autowired
    private ActivityTemplateManager activityTemplateManager;
    @Autowired
    private ActivityToolsConfigManager activityToolsConfigManager;

    @Override
    public Result<ResponseCreateActivityTemplate> createTemplate(RequestCreateActivityTemplate req) {
        try {
            if (activityToolsConfigManager.isActivityToolsIllegal(req.getAppId(), req.getActivityTools())) {
                LogContext.addResLog("`appId={}`activityTools={}", req.getAppId(), req.getActivityTools());
                return RpcResult.fail(CommonService.PARAM_ERROR, "玩法工具类型不合法");
            }
            Result<Long> result = activityTemplateManager.createTemplate(req);
            if (RpcResult.isSuccess(result)) {
                LogContext.addResLog("`target={}", result.target());
                Long templateId = result.target();
                ResponseCreateActivityTemplate response = new ResponseCreateActivityTemplate();
                response.setId(templateId);
                return RpcResult.success(response);
            }
            return RpcResult.fail(CREATE_TEMPLATE_FAIL, result.getMessage());
        } catch (RuntimeException e) {
            log.error("createTemplate error req={}", req, e);
            return RpcResult.fail(CREATE_TEMPLATE_FAIL, e.getMessage());
        }
    }

    @Override
    public Result<Void> updateTemplate(RequestUpdateActivityTemplate req) {
        try {
            if (activityToolsConfigManager.isActivityToolsIllegal(req.getAppId(), req.getActivityTools())) {
                LogContext.addResLog("`appId={}`activityTools={}", req.getAppId(), req.getActivityTools());
                return RpcResult.fail(CommonService.PARAM_ERROR, "玩法工具类型不合法");
            }
            return activityTemplateManager.updateTemplate(req);
        } catch (RuntimeException e) {
            log.error("updateTemplate error req={}", req, e);
            return RpcResult.fail(UPDATE_TEMPLATE_FAIL, e.getMessage());
        }
    }

    @Override
    public Result<Void> deleteTemplate(RequestDeleteActivityTemplate req) {
        try {
            LogContext.addReqLog("req={}", req);
            return activityTemplateManager.deleteTemplate(req);
        } catch (RuntimeException e) {
            log.error("deleteTemplate error req={}", req, e);
            return RpcResult.fail(DELETE_TEMPLATE_FAIL, e.getMessage());
        }
    }

    @Override
    public Result<Void> updateShelfStatus(RequestUpdateActivityTemplateShelfStatus req) {
        try {
            return activityTemplateManager.updateShelfStatus(req);
        } catch (RuntimeException e) {
            log.error("updateShelfStatus error req={}", req, e);
            return RpcResult.fail(UPDATE_SHELF_STATUS_FAIL, e.getMessage());
        }
    }

    @Override
    public Result<ResponseGetActivityTemplateShelfStatus> getShelfStatus(long id) {
        try {
            LogContext.addReqLog("id={}", id);
            return activityTemplateManager.getShelfStatus(id);
        } catch (RuntimeException e) {
            log.error("getShelfStatus error id={}", id, e);
            return RpcResult.fail(GET_SHELF_STATUS_FAIL, e.getMessage());
        }
    }

    @Override
    public Result<PageBean<ActivityTemplatePageBean>> pageTemplate(RequestPageActivityTemplate req) {
        try {
            return activityTemplateManager.pageTemplate(req);
        } catch (RuntimeException e) {
            log.error("pageTemplate error req={}", req, e);
            return RpcResult.fail(PAGE_TEMPLATE_FAIL, e.getMessage());
        }
    }

    @Override
    public Result<ResponseGetActivityTemplate> getTemplate(long id) {
        try {
            LogContext.addReqLog("id={}", id);
            return activityTemplateManager.getTemplate(id);
        } catch (RuntimeException e) {
            log.error("getTemplate error id={}", id, e);
            return RpcResult.fail(GET_TEMPLATE_FAIL, e.getMessage());
        }
    }
}

