package fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ActivityReportDataSummaryExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_report_data_summary
     *
     * @mbg.generated Tue Apr 29 17:49:36 CST 2025
     */
    public ActivityReportDataSummaryExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_report_data_summary
     *
     * @mbg.generated Tue Apr 29 17:49:36 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_report_data_summary
     *
     * @mbg.generated Tue Apr 29 17:49:36 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_report_data_summary
     *
     * @mbg.generated Tue Apr 29 17:49:36 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_report_data_summary
     *
     * @mbg.generated Tue Apr 29 17:49:36 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_report_data_summary
     *
     * @mbg.generated Tue Apr 29 17:49:36 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_report_data_summary
     *
     * @mbg.generated Tue Apr 29 17:49:36 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_report_data_summary
     *
     * @mbg.generated Tue Apr 29 17:49:36 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_report_data_summary
     *
     * @mbg.generated Tue Apr 29 17:49:36 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_report_data_summary
     *
     * @mbg.generated Tue Apr 29 17:49:36 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_report_data_summary
     *
     * @mbg.generated Tue Apr 29 17:49:36 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return ActivityReportDataSummary.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table activity_report_data_summary
     *
     * @mbg.generated Tue Apr 29 17:49:36 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andActivityIdIsNull() {
            addCriterion("activity_id is null");
            return (Criteria) this;
        }

        public Criteria andActivityIdIsNotNull() {
            addCriterion("activity_id is not null");
            return (Criteria) this;
        }

        public Criteria andActivityIdEqualTo(Long value) {
            addCriterion("activity_id =", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotEqualTo(Long value) {
            addCriterion("activity_id <>", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThan(Long value) {
            addCriterion("activity_id >", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThanOrEqualTo(Long value) {
            addCriterion("activity_id >=", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThan(Long value) {
            addCriterion("activity_id <", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThanOrEqualTo(Long value) {
            addCriterion("activity_id <=", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdIn(List<Long> values) {
            addCriterion("activity_id in", values, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotIn(List<Long> values) {
            addCriterion("activity_id not in", values, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdBetween(Long value1, Long value2) {
            addCriterion("activity_id between", value1, value2, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotBetween(Long value1, Long value2) {
            addCriterion("activity_id not between", value1, value2, "activityId");
            return (Criteria) this;
        }

        public Criteria andNjIdIsNull() {
            addCriterion("nj_id is null");
            return (Criteria) this;
        }

        public Criteria andNjIdIsNotNull() {
            addCriterion("nj_id is not null");
            return (Criteria) this;
        }

        public Criteria andNjIdEqualTo(Long value) {
            addCriterion("nj_id =", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotEqualTo(Long value) {
            addCriterion("nj_id <>", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdGreaterThan(Long value) {
            addCriterion("nj_id >", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdGreaterThanOrEqualTo(Long value) {
            addCriterion("nj_id >=", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdLessThan(Long value) {
            addCriterion("nj_id <", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdLessThanOrEqualTo(Long value) {
            addCriterion("nj_id <=", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdIn(List<Long> values) {
            addCriterion("nj_id in", values, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotIn(List<Long> values) {
            addCriterion("nj_id not in", values, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdBetween(Long value1, Long value2) {
            addCriterion("nj_id between", value1, value2, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotBetween(Long value1, Long value2) {
            addCriterion("nj_id not between", value1, value2, "njId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNull() {
            addCriterion("family_id is null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNotNull() {
            addCriterion("family_id is not null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdEqualTo(Long value) {
            addCriterion("family_id =", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotEqualTo(Long value) {
            addCriterion("family_id <>", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThan(Long value) {
            addCriterion("family_id >", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("family_id >=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThan(Long value) {
            addCriterion("family_id <", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("family_id <=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIn(List<Long> values) {
            addCriterion("family_id in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotIn(List<Long> values) {
            addCriterion("family_id not in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdBetween(Long value1, Long value2) {
            addCriterion("family_id between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("family_id not between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Date value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Date value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Date value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Date value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Date> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Date> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Date value1, Date value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("end_time is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(Date value) {
            addCriterion("end_time =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(Date value) {
            addCriterion("end_time <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(Date value) {
            addCriterion("end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("end_time >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(Date value) {
            addCriterion("end_time <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("end_time <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<Date> values) {
            addCriterion("end_time in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<Date> values) {
            addCriterion("end_time not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Date value1, Date value2) {
            addCriterion("end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("end_time not between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntIsNull() {
            addCriterion("enter_room_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntIsNotNull() {
            addCriterion("enter_room_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntEqualTo(Long value) {
            addCriterion("enter_room_user_cnt =", value, "enterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntNotEqualTo(Long value) {
            addCriterion("enter_room_user_cnt <>", value, "enterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntGreaterThan(Long value) {
            addCriterion("enter_room_user_cnt >", value, "enterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntGreaterThanOrEqualTo(Long value) {
            addCriterion("enter_room_user_cnt >=", value, "enterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntLessThan(Long value) {
            addCriterion("enter_room_user_cnt <", value, "enterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntLessThanOrEqualTo(Long value) {
            addCriterion("enter_room_user_cnt <=", value, "enterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntIn(List<Long> values) {
            addCriterion("enter_room_user_cnt in", values, "enterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntNotIn(List<Long> values) {
            addCriterion("enter_room_user_cnt not in", values, "enterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntBetween(Long value1, Long value2) {
            addCriterion("enter_room_user_cnt between", value1, value2, "enterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomUserCntNotBetween(Long value1, Long value2) {
            addCriterion("enter_room_user_cnt not between", value1, value2, "enterRoomUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomNewUserCntIsNull() {
            addCriterion("enter_room_new_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andEnterRoomNewUserCntIsNotNull() {
            addCriterion("enter_room_new_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andEnterRoomNewUserCntEqualTo(Long value) {
            addCriterion("enter_room_new_user_cnt =", value, "enterRoomNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomNewUserCntNotEqualTo(Long value) {
            addCriterion("enter_room_new_user_cnt <>", value, "enterRoomNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomNewUserCntGreaterThan(Long value) {
            addCriterion("enter_room_new_user_cnt >", value, "enterRoomNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomNewUserCntGreaterThanOrEqualTo(Long value) {
            addCriterion("enter_room_new_user_cnt >=", value, "enterRoomNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomNewUserCntLessThan(Long value) {
            addCriterion("enter_room_new_user_cnt <", value, "enterRoomNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomNewUserCntLessThanOrEqualTo(Long value) {
            addCriterion("enter_room_new_user_cnt <=", value, "enterRoomNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomNewUserCntIn(List<Long> values) {
            addCriterion("enter_room_new_user_cnt in", values, "enterRoomNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomNewUserCntNotIn(List<Long> values) {
            addCriterion("enter_room_new_user_cnt not in", values, "enterRoomNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomNewUserCntBetween(Long value1, Long value2) {
            addCriterion("enter_room_new_user_cnt between", value1, value2, "enterRoomNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andEnterRoomNewUserCntNotBetween(Long value1, Long value2) {
            addCriterion("enter_room_new_user_cnt not between", value1, value2, "enterRoomNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinIsNull() {
            addCriterion("user_full_one_min is null");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinIsNotNull() {
            addCriterion("user_full_one_min is not null");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinEqualTo(Long value) {
            addCriterion("user_full_one_min =", value, "userFullOneMin");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinNotEqualTo(Long value) {
            addCriterion("user_full_one_min <>", value, "userFullOneMin");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinGreaterThan(Long value) {
            addCriterion("user_full_one_min >", value, "userFullOneMin");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinGreaterThanOrEqualTo(Long value) {
            addCriterion("user_full_one_min >=", value, "userFullOneMin");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinLessThan(Long value) {
            addCriterion("user_full_one_min <", value, "userFullOneMin");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinLessThanOrEqualTo(Long value) {
            addCriterion("user_full_one_min <=", value, "userFullOneMin");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinIn(List<Long> values) {
            addCriterion("user_full_one_min in", values, "userFullOneMin");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinNotIn(List<Long> values) {
            addCriterion("user_full_one_min not in", values, "userFullOneMin");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinBetween(Long value1, Long value2) {
            addCriterion("user_full_one_min between", value1, value2, "userFullOneMin");
            return (Criteria) this;
        }

        public Criteria andUserFullOneMinNotBetween(Long value1, Long value2) {
            addCriterion("user_full_one_min not between", value1, value2, "userFullOneMin");
            return (Criteria) this;
        }

        public Criteria andNewUserFullOneMinIsNull() {
            addCriterion("new_user_full_one_min is null");
            return (Criteria) this;
        }

        public Criteria andNewUserFullOneMinIsNotNull() {
            addCriterion("new_user_full_one_min is not null");
            return (Criteria) this;
        }

        public Criteria andNewUserFullOneMinEqualTo(Long value) {
            addCriterion("new_user_full_one_min =", value, "newUserFullOneMin");
            return (Criteria) this;
        }

        public Criteria andNewUserFullOneMinNotEqualTo(Long value) {
            addCriterion("new_user_full_one_min <>", value, "newUserFullOneMin");
            return (Criteria) this;
        }

        public Criteria andNewUserFullOneMinGreaterThan(Long value) {
            addCriterion("new_user_full_one_min >", value, "newUserFullOneMin");
            return (Criteria) this;
        }

        public Criteria andNewUserFullOneMinGreaterThanOrEqualTo(Long value) {
            addCriterion("new_user_full_one_min >=", value, "newUserFullOneMin");
            return (Criteria) this;
        }

        public Criteria andNewUserFullOneMinLessThan(Long value) {
            addCriterion("new_user_full_one_min <", value, "newUserFullOneMin");
            return (Criteria) this;
        }

        public Criteria andNewUserFullOneMinLessThanOrEqualTo(Long value) {
            addCriterion("new_user_full_one_min <=", value, "newUserFullOneMin");
            return (Criteria) this;
        }

        public Criteria andNewUserFullOneMinIn(List<Long> values) {
            addCriterion("new_user_full_one_min in", values, "newUserFullOneMin");
            return (Criteria) this;
        }

        public Criteria andNewUserFullOneMinNotIn(List<Long> values) {
            addCriterion("new_user_full_one_min not in", values, "newUserFullOneMin");
            return (Criteria) this;
        }

        public Criteria andNewUserFullOneMinBetween(Long value1, Long value2) {
            addCriterion("new_user_full_one_min between", value1, value2, "newUserFullOneMin");
            return (Criteria) this;
        }

        public Criteria andNewUserFullOneMinNotBetween(Long value1, Long value2) {
            addCriterion("new_user_full_one_min not between", value1, value2, "newUserFullOneMin");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntIsNull() {
            addCriterion("gift_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntIsNotNull() {
            addCriterion("gift_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntEqualTo(Long value) {
            addCriterion("gift_user_cnt =", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntNotEqualTo(Long value) {
            addCriterion("gift_user_cnt <>", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntGreaterThan(Long value) {
            addCriterion("gift_user_cnt >", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntGreaterThanOrEqualTo(Long value) {
            addCriterion("gift_user_cnt >=", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntLessThan(Long value) {
            addCriterion("gift_user_cnt <", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntLessThanOrEqualTo(Long value) {
            addCriterion("gift_user_cnt <=", value, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntIn(List<Long> values) {
            addCriterion("gift_user_cnt in", values, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntNotIn(List<Long> values) {
            addCriterion("gift_user_cnt not in", values, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntBetween(Long value1, Long value2) {
            addCriterion("gift_user_cnt between", value1, value2, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftUserCntNotBetween(Long value1, Long value2) {
            addCriterion("gift_user_cnt not between", value1, value2, "giftUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntIsNull() {
            addCriterion("gift_new_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntIsNotNull() {
            addCriterion("gift_new_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntEqualTo(Long value) {
            addCriterion("gift_new_user_cnt =", value, "giftNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntNotEqualTo(Long value) {
            addCriterion("gift_new_user_cnt <>", value, "giftNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntGreaterThan(Long value) {
            addCriterion("gift_new_user_cnt >", value, "giftNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntGreaterThanOrEqualTo(Long value) {
            addCriterion("gift_new_user_cnt >=", value, "giftNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntLessThan(Long value) {
            addCriterion("gift_new_user_cnt <", value, "giftNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntLessThanOrEqualTo(Long value) {
            addCriterion("gift_new_user_cnt <=", value, "giftNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntIn(List<Long> values) {
            addCriterion("gift_new_user_cnt in", values, "giftNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntNotIn(List<Long> values) {
            addCriterion("gift_new_user_cnt not in", values, "giftNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntBetween(Long value1, Long value2) {
            addCriterion("gift_new_user_cnt between", value1, value2, "giftNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andGiftNewUserCntNotBetween(Long value1, Long value2) {
            addCriterion("gift_new_user_cnt not between", value1, value2, "giftNewUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntIsNull() {
            addCriterion("new_fans_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntIsNotNull() {
            addCriterion("new_fans_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntEqualTo(Long value) {
            addCriterion("new_fans_user_cnt =", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntNotEqualTo(Long value) {
            addCriterion("new_fans_user_cnt <>", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntGreaterThan(Long value) {
            addCriterion("new_fans_user_cnt >", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntGreaterThanOrEqualTo(Long value) {
            addCriterion("new_fans_user_cnt >=", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntLessThan(Long value) {
            addCriterion("new_fans_user_cnt <", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntLessThanOrEqualTo(Long value) {
            addCriterion("new_fans_user_cnt <=", value, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntIn(List<Long> values) {
            addCriterion("new_fans_user_cnt in", values, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntNotIn(List<Long> values) {
            addCriterion("new_fans_user_cnt not in", values, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntBetween(Long value1, Long value2) {
            addCriterion("new_fans_user_cnt between", value1, value2, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewFansUserCntNotBetween(Long value1, Long value2) {
            addCriterion("new_fans_user_cnt not between", value1, value2, "newFansUserCnt");
            return (Criteria) this;
        }

        public Criteria andAllIncomeIsNull() {
            addCriterion("all_income is null");
            return (Criteria) this;
        }

        public Criteria andAllIncomeIsNotNull() {
            addCriterion("all_income is not null");
            return (Criteria) this;
        }

        public Criteria andAllIncomeEqualTo(Long value) {
            addCriterion("all_income =", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeNotEqualTo(Long value) {
            addCriterion("all_income <>", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeGreaterThan(Long value) {
            addCriterion("all_income >", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeGreaterThanOrEqualTo(Long value) {
            addCriterion("all_income >=", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeLessThan(Long value) {
            addCriterion("all_income <", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeLessThanOrEqualTo(Long value) {
            addCriterion("all_income <=", value, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeIn(List<Long> values) {
            addCriterion("all_income in", values, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeNotIn(List<Long> values) {
            addCriterion("all_income not in", values, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeBetween(Long value1, Long value2) {
            addCriterion("all_income between", value1, value2, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllIncomeNotBetween(Long value1, Long value2) {
            addCriterion("all_income not between", value1, value2, "allIncome");
            return (Criteria) this;
        }

        public Criteria andAllCharmIsNull() {
            addCriterion("all_charm is null");
            return (Criteria) this;
        }

        public Criteria andAllCharmIsNotNull() {
            addCriterion("all_charm is not null");
            return (Criteria) this;
        }

        public Criteria andAllCharmEqualTo(Long value) {
            addCriterion("all_charm =", value, "allCharm");
            return (Criteria) this;
        }

        public Criteria andAllCharmNotEqualTo(Long value) {
            addCriterion("all_charm <>", value, "allCharm");
            return (Criteria) this;
        }

        public Criteria andAllCharmGreaterThan(Long value) {
            addCriterion("all_charm >", value, "allCharm");
            return (Criteria) this;
        }

        public Criteria andAllCharmGreaterThanOrEqualTo(Long value) {
            addCriterion("all_charm >=", value, "allCharm");
            return (Criteria) this;
        }

        public Criteria andAllCharmLessThan(Long value) {
            addCriterion("all_charm <", value, "allCharm");
            return (Criteria) this;
        }

        public Criteria andAllCharmLessThanOrEqualTo(Long value) {
            addCriterion("all_charm <=", value, "allCharm");
            return (Criteria) this;
        }

        public Criteria andAllCharmIn(List<Long> values) {
            addCriterion("all_charm in", values, "allCharm");
            return (Criteria) this;
        }

        public Criteria andAllCharmNotIn(List<Long> values) {
            addCriterion("all_charm not in", values, "allCharm");
            return (Criteria) this;
        }

        public Criteria andAllCharmBetween(Long value1, Long value2) {
            addCriterion("all_charm between", value1, value2, "allCharm");
            return (Criteria) this;
        }

        public Criteria andAllCharmNotBetween(Long value1, Long value2) {
            addCriterion("all_charm not between", value1, value2, "allCharm");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andEnterRoomStayRateIsNull() {
            addCriterion("enter_room_stay_rate is null");
            return (Criteria) this;
        }

        public Criteria andEnterRoomStayRateIsNotNull() {
            addCriterion("enter_room_stay_rate is not null");
            return (Criteria) this;
        }

        public Criteria andEnterRoomStayRateEqualTo(BigDecimal value) {
            addCriterion("enter_room_stay_rate =", value, "enterRoomStayRate");
            return (Criteria) this;
        }

        public Criteria andEnterRoomStayRateNotEqualTo(BigDecimal value) {
            addCriterion("enter_room_stay_rate <>", value, "enterRoomStayRate");
            return (Criteria) this;
        }

        public Criteria andEnterRoomStayRateGreaterThan(BigDecimal value) {
            addCriterion("enter_room_stay_rate >", value, "enterRoomStayRate");
            return (Criteria) this;
        }

        public Criteria andEnterRoomStayRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("enter_room_stay_rate >=", value, "enterRoomStayRate");
            return (Criteria) this;
        }

        public Criteria andEnterRoomStayRateLessThan(BigDecimal value) {
            addCriterion("enter_room_stay_rate <", value, "enterRoomStayRate");
            return (Criteria) this;
        }

        public Criteria andEnterRoomStayRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("enter_room_stay_rate <=", value, "enterRoomStayRate");
            return (Criteria) this;
        }

        public Criteria andEnterRoomStayRateIn(List<BigDecimal> values) {
            addCriterion("enter_room_stay_rate in", values, "enterRoomStayRate");
            return (Criteria) this;
        }

        public Criteria andEnterRoomStayRateNotIn(List<BigDecimal> values) {
            addCriterion("enter_room_stay_rate not in", values, "enterRoomStayRate");
            return (Criteria) this;
        }

        public Criteria andEnterRoomStayRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("enter_room_stay_rate between", value1, value2, "enterRoomStayRate");
            return (Criteria) this;
        }

        public Criteria andEnterRoomStayRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("enter_room_stay_rate not between", value1, value2, "enterRoomStayRate");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntIsNull() {
            addCriterion("comment_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntIsNotNull() {
            addCriterion("comment_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntEqualTo(Long value) {
            addCriterion("comment_user_cnt =", value, "commentUserCnt");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntNotEqualTo(Long value) {
            addCriterion("comment_user_cnt <>", value, "commentUserCnt");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntGreaterThan(Long value) {
            addCriterion("comment_user_cnt >", value, "commentUserCnt");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntGreaterThanOrEqualTo(Long value) {
            addCriterion("comment_user_cnt >=", value, "commentUserCnt");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntLessThan(Long value) {
            addCriterion("comment_user_cnt <", value, "commentUserCnt");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntLessThanOrEqualTo(Long value) {
            addCriterion("comment_user_cnt <=", value, "commentUserCnt");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntIn(List<Long> values) {
            addCriterion("comment_user_cnt in", values, "commentUserCnt");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntNotIn(List<Long> values) {
            addCriterion("comment_user_cnt not in", values, "commentUserCnt");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntBetween(Long value1, Long value2) {
            addCriterion("comment_user_cnt between", value1, value2, "commentUserCnt");
            return (Criteria) this;
        }

        public Criteria andCommentUserCntNotBetween(Long value1, Long value2) {
            addCriterion("comment_user_cnt not between", value1, value2, "commentUserCnt");
            return (Criteria) this;
        }

        public Criteria andStaySpeakRateIsNull() {
            addCriterion("stay_speak_rate is null");
            return (Criteria) this;
        }

        public Criteria andStaySpeakRateIsNotNull() {
            addCriterion("stay_speak_rate is not null");
            return (Criteria) this;
        }

        public Criteria andStaySpeakRateEqualTo(BigDecimal value) {
            addCriterion("stay_speak_rate =", value, "staySpeakRate");
            return (Criteria) this;
        }

        public Criteria andStaySpeakRateNotEqualTo(BigDecimal value) {
            addCriterion("stay_speak_rate <>", value, "staySpeakRate");
            return (Criteria) this;
        }

        public Criteria andStaySpeakRateGreaterThan(BigDecimal value) {
            addCriterion("stay_speak_rate >", value, "staySpeakRate");
            return (Criteria) this;
        }

        public Criteria andStaySpeakRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("stay_speak_rate >=", value, "staySpeakRate");
            return (Criteria) this;
        }

        public Criteria andStaySpeakRateLessThan(BigDecimal value) {
            addCriterion("stay_speak_rate <", value, "staySpeakRate");
            return (Criteria) this;
        }

        public Criteria andStaySpeakRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("stay_speak_rate <=", value, "staySpeakRate");
            return (Criteria) this;
        }

        public Criteria andStaySpeakRateIn(List<BigDecimal> values) {
            addCriterion("stay_speak_rate in", values, "staySpeakRate");
            return (Criteria) this;
        }

        public Criteria andStaySpeakRateNotIn(List<BigDecimal> values) {
            addCriterion("stay_speak_rate not in", values, "staySpeakRate");
            return (Criteria) this;
        }

        public Criteria andStaySpeakRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stay_speak_rate between", value1, value2, "staySpeakRate");
            return (Criteria) this;
        }

        public Criteria andStaySpeakRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stay_speak_rate not between", value1, value2, "staySpeakRate");
            return (Criteria) this;
        }

        public Criteria andStayPayRateIsNull() {
            addCriterion("stay_pay_rate is null");
            return (Criteria) this;
        }

        public Criteria andStayPayRateIsNotNull() {
            addCriterion("stay_pay_rate is not null");
            return (Criteria) this;
        }

        public Criteria andStayPayRateEqualTo(BigDecimal value) {
            addCriterion("stay_pay_rate =", value, "stayPayRate");
            return (Criteria) this;
        }

        public Criteria andStayPayRateNotEqualTo(BigDecimal value) {
            addCriterion("stay_pay_rate <>", value, "stayPayRate");
            return (Criteria) this;
        }

        public Criteria andStayPayRateGreaterThan(BigDecimal value) {
            addCriterion("stay_pay_rate >", value, "stayPayRate");
            return (Criteria) this;
        }

        public Criteria andStayPayRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("stay_pay_rate >=", value, "stayPayRate");
            return (Criteria) this;
        }

        public Criteria andStayPayRateLessThan(BigDecimal value) {
            addCriterion("stay_pay_rate <", value, "stayPayRate");
            return (Criteria) this;
        }

        public Criteria andStayPayRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("stay_pay_rate <=", value, "stayPayRate");
            return (Criteria) this;
        }

        public Criteria andStayPayRateIn(List<BigDecimal> values) {
            addCriterion("stay_pay_rate in", values, "stayPayRate");
            return (Criteria) this;
        }

        public Criteria andStayPayRateNotIn(List<BigDecimal> values) {
            addCriterion("stay_pay_rate not in", values, "stayPayRate");
            return (Criteria) this;
        }

        public Criteria andStayPayRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stay_pay_rate between", value1, value2, "stayPayRate");
            return (Criteria) this;
        }

        public Criteria andStayPayRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("stay_pay_rate not between", value1, value2, "stayPayRate");
            return (Criteria) this;
        }

        public Criteria andNewUserEnterRoomStayRateIsNull() {
            addCriterion("new_user_enter_room_stay_rate is null");
            return (Criteria) this;
        }

        public Criteria andNewUserEnterRoomStayRateIsNotNull() {
            addCriterion("new_user_enter_room_stay_rate is not null");
            return (Criteria) this;
        }

        public Criteria andNewUserEnterRoomStayRateEqualTo(BigDecimal value) {
            addCriterion("new_user_enter_room_stay_rate =", value, "newUserEnterRoomStayRate");
            return (Criteria) this;
        }

        public Criteria andNewUserEnterRoomStayRateNotEqualTo(BigDecimal value) {
            addCriterion("new_user_enter_room_stay_rate <>", value, "newUserEnterRoomStayRate");
            return (Criteria) this;
        }

        public Criteria andNewUserEnterRoomStayRateGreaterThan(BigDecimal value) {
            addCriterion("new_user_enter_room_stay_rate >", value, "newUserEnterRoomStayRate");
            return (Criteria) this;
        }

        public Criteria andNewUserEnterRoomStayRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("new_user_enter_room_stay_rate >=", value, "newUserEnterRoomStayRate");
            return (Criteria) this;
        }

        public Criteria andNewUserEnterRoomStayRateLessThan(BigDecimal value) {
            addCriterion("new_user_enter_room_stay_rate <", value, "newUserEnterRoomStayRate");
            return (Criteria) this;
        }

        public Criteria andNewUserEnterRoomStayRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("new_user_enter_room_stay_rate <=", value, "newUserEnterRoomStayRate");
            return (Criteria) this;
        }

        public Criteria andNewUserEnterRoomStayRateIn(List<BigDecimal> values) {
            addCriterion("new_user_enter_room_stay_rate in", values, "newUserEnterRoomStayRate");
            return (Criteria) this;
        }

        public Criteria andNewUserEnterRoomStayRateNotIn(List<BigDecimal> values) {
            addCriterion("new_user_enter_room_stay_rate not in", values, "newUserEnterRoomStayRate");
            return (Criteria) this;
        }

        public Criteria andNewUserEnterRoomStayRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("new_user_enter_room_stay_rate between", value1, value2, "newUserEnterRoomStayRate");
            return (Criteria) this;
        }

        public Criteria andNewUserEnterRoomStayRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("new_user_enter_room_stay_rate not between", value1, value2, "newUserEnterRoomStayRate");
            return (Criteria) this;
        }

        public Criteria andNewUserCommentUserCntIsNull() {
            addCriterion("new_user_comment_user_cnt is null");
            return (Criteria) this;
        }

        public Criteria andNewUserCommentUserCntIsNotNull() {
            addCriterion("new_user_comment_user_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andNewUserCommentUserCntEqualTo(Long value) {
            addCriterion("new_user_comment_user_cnt =", value, "newUserCommentUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewUserCommentUserCntNotEqualTo(Long value) {
            addCriterion("new_user_comment_user_cnt <>", value, "newUserCommentUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewUserCommentUserCntGreaterThan(Long value) {
            addCriterion("new_user_comment_user_cnt >", value, "newUserCommentUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewUserCommentUserCntGreaterThanOrEqualTo(Long value) {
            addCriterion("new_user_comment_user_cnt >=", value, "newUserCommentUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewUserCommentUserCntLessThan(Long value) {
            addCriterion("new_user_comment_user_cnt <", value, "newUserCommentUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewUserCommentUserCntLessThanOrEqualTo(Long value) {
            addCriterion("new_user_comment_user_cnt <=", value, "newUserCommentUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewUserCommentUserCntIn(List<Long> values) {
            addCriterion("new_user_comment_user_cnt in", values, "newUserCommentUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewUserCommentUserCntNotIn(List<Long> values) {
            addCriterion("new_user_comment_user_cnt not in", values, "newUserCommentUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewUserCommentUserCntBetween(Long value1, Long value2) {
            addCriterion("new_user_comment_user_cnt between", value1, value2, "newUserCommentUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewUserCommentUserCntNotBetween(Long value1, Long value2) {
            addCriterion("new_user_comment_user_cnt not between", value1, value2, "newUserCommentUserCnt");
            return (Criteria) this;
        }

        public Criteria andNewUserStaySpeakRateIsNull() {
            addCriterion("new_user_stay_speak_rate is null");
            return (Criteria) this;
        }

        public Criteria andNewUserStaySpeakRateIsNotNull() {
            addCriterion("new_user_stay_speak_rate is not null");
            return (Criteria) this;
        }

        public Criteria andNewUserStaySpeakRateEqualTo(BigDecimal value) {
            addCriterion("new_user_stay_speak_rate =", value, "newUserStaySpeakRate");
            return (Criteria) this;
        }

        public Criteria andNewUserStaySpeakRateNotEqualTo(BigDecimal value) {
            addCriterion("new_user_stay_speak_rate <>", value, "newUserStaySpeakRate");
            return (Criteria) this;
        }

        public Criteria andNewUserStaySpeakRateGreaterThan(BigDecimal value) {
            addCriterion("new_user_stay_speak_rate >", value, "newUserStaySpeakRate");
            return (Criteria) this;
        }

        public Criteria andNewUserStaySpeakRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("new_user_stay_speak_rate >=", value, "newUserStaySpeakRate");
            return (Criteria) this;
        }

        public Criteria andNewUserStaySpeakRateLessThan(BigDecimal value) {
            addCriterion("new_user_stay_speak_rate <", value, "newUserStaySpeakRate");
            return (Criteria) this;
        }

        public Criteria andNewUserStaySpeakRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("new_user_stay_speak_rate <=", value, "newUserStaySpeakRate");
            return (Criteria) this;
        }

        public Criteria andNewUserStaySpeakRateIn(List<BigDecimal> values) {
            addCriterion("new_user_stay_speak_rate in", values, "newUserStaySpeakRate");
            return (Criteria) this;
        }

        public Criteria andNewUserStaySpeakRateNotIn(List<BigDecimal> values) {
            addCriterion("new_user_stay_speak_rate not in", values, "newUserStaySpeakRate");
            return (Criteria) this;
        }

        public Criteria andNewUserStaySpeakRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("new_user_stay_speak_rate between", value1, value2, "newUserStaySpeakRate");
            return (Criteria) this;
        }

        public Criteria andNewUserStaySpeakRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("new_user_stay_speak_rate not between", value1, value2, "newUserStaySpeakRate");
            return (Criteria) this;
        }

        public Criteria andNewUserStayPayRateIsNull() {
            addCriterion("new_user_stay_pay_rate is null");
            return (Criteria) this;
        }

        public Criteria andNewUserStayPayRateIsNotNull() {
            addCriterion("new_user_stay_pay_rate is not null");
            return (Criteria) this;
        }

        public Criteria andNewUserStayPayRateEqualTo(BigDecimal value) {
            addCriterion("new_user_stay_pay_rate =", value, "newUserStayPayRate");
            return (Criteria) this;
        }

        public Criteria andNewUserStayPayRateNotEqualTo(BigDecimal value) {
            addCriterion("new_user_stay_pay_rate <>", value, "newUserStayPayRate");
            return (Criteria) this;
        }

        public Criteria andNewUserStayPayRateGreaterThan(BigDecimal value) {
            addCriterion("new_user_stay_pay_rate >", value, "newUserStayPayRate");
            return (Criteria) this;
        }

        public Criteria andNewUserStayPayRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("new_user_stay_pay_rate >=", value, "newUserStayPayRate");
            return (Criteria) this;
        }

        public Criteria andNewUserStayPayRateLessThan(BigDecimal value) {
            addCriterion("new_user_stay_pay_rate <", value, "newUserStayPayRate");
            return (Criteria) this;
        }

        public Criteria andNewUserStayPayRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("new_user_stay_pay_rate <=", value, "newUserStayPayRate");
            return (Criteria) this;
        }

        public Criteria andNewUserStayPayRateIn(List<BigDecimal> values) {
            addCriterion("new_user_stay_pay_rate in", values, "newUserStayPayRate");
            return (Criteria) this;
        }

        public Criteria andNewUserStayPayRateNotIn(List<BigDecimal> values) {
            addCriterion("new_user_stay_pay_rate not in", values, "newUserStayPayRate");
            return (Criteria) this;
        }

        public Criteria andNewUserStayPayRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("new_user_stay_pay_rate between", value1, value2, "newUserStayPayRate");
            return (Criteria) this;
        }

        public Criteria andNewUserStayPayRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("new_user_stay_pay_rate not between", value1, value2, "newUserStayPayRate");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table activity_report_data_summary
     *
     * @mbg.generated do_not_delete_during_merge Tue Apr 29 17:49:36 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table activity_report_data_summary
     *
     * @mbg.generated Tue Apr 29 17:49:36 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}