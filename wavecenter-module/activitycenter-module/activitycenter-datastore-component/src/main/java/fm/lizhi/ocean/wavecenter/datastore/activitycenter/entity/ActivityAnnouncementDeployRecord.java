package fm.lizhi.ocean.wavecenter.datastore.activitycenter.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 *
 * @date 2024-10-19 04:17:17
 */
@Table(name = "`activity_announcement_deploy_record`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityAnnouncementDeployRecord {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 活动ID
     */
    @Column(name= "`activity_id`")
    private Long activityId;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 厅主ID，冗余，部分业务会使用到
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 原始房间公告
     */
    @Column(name= "`original_announcement`")
    private String originalAnnouncement;

    /**
     * 原始房间公告url
     */
    @Column(name= "`original_announcement_img_url`")
    private String originalAnnouncementImgUrl;

    /**
     * 要配置的房间公告
     */
    @Column(name= "`announcement`")
    private String announcement;

    /**
     * 要配置的房间公告图片
     */
    @Column(name= "`announcement_img_url`")
    private String announcementImgUrl;

    /**
     * 重试次数
     */
    @Column(name= "`try_count`")
    private Integer tryCount;

    /**
     * 状态，0：待设置，1：设置失败，2：设置成功，3：恢复失败，4：恢复成功
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 活动开始时间
     */
    @Column(name= "`start_time`")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @Column(name= "`end_time`")
    private Date endTime;

    /**
     * 可选值  TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", activityId=").append(activityId);
        sb.append(", appId=").append(appId);
        sb.append(", njId=").append(njId);
        sb.append(", originalAnnouncement=").append(originalAnnouncement);
        sb.append(", originalAnnouncementImgUrl=").append(originalAnnouncementImgUrl);
        sb.append(", announcement=").append(announcement);
        sb.append(", announcementImgUrl=").append(announcementImgUrl);
        sb.append(", tryCount=").append(tryCount);
        sb.append(", status=").append(status);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }
}