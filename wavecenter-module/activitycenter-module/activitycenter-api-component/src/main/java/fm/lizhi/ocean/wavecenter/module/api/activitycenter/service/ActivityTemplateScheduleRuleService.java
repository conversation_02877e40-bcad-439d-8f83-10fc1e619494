package fm.lizhi.ocean.wavecenter.module.api.activitycenter.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.module.api.activitycenter.response.ResponseActivityTemplateScheduleRule;

import java.util.Optional;

/**
 * 活动目标日程规则
 * <AUTHOR>
 */
public interface ActivityTemplateScheduleRuleService {


    /**
     * 根据活动模板ID获取活动模板日程规则
     * @param templateId
     * @return
     */
    Result<ResponseActivityTemplateScheduleRule> getScheduleByTemplateId(int appId, Long templateId);


    /**
     * 校验活动模板日程规则
     * @param templateId
     * @return
     */
    Result<Boolean> checkTemplateScheduleRule(int appId, Long templateId, Long startTime, Long endTime);
}
