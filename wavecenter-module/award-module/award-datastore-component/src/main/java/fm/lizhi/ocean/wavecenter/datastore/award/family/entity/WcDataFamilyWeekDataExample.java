package fm.lizhi.ocean.wavecenter.datastore.award.family.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WcDataFamilyWeekDataExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week_data
     *
     * @mbg.generated Wed Mar 26 16:46:07 CST 2025
     */
    public WcDataFamilyWeekDataExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week_data
     *
     * @mbg.generated Wed Mar 26 16:46:07 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week_data
     *
     * @mbg.generated Wed Mar 26 16:46:07 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week_data
     *
     * @mbg.generated Wed Mar 26 16:46:07 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week_data
     *
     * @mbg.generated Wed Mar 26 16:46:07 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week_data
     *
     * @mbg.generated Wed Mar 26 16:46:07 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week_data
     *
     * @mbg.generated Wed Mar 26 16:46:07 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week_data
     *
     * @mbg.generated Wed Mar 26 16:46:07 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week_data
     *
     * @mbg.generated Wed Mar 26 16:46:07 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week_data
     *
     * @mbg.generated Wed Mar 26 16:46:07 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table wavecenter_data_family_week_data
     *
     * @mbg.generated Wed Mar 26 16:46:07 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return WcDataFamilyWeekData.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_data_family_week_data
     *
     * @mbg.generated Wed Mar 26 16:46:07 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNull() {
            addCriterion("family_id is null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNotNull() {
            addCriterion("family_id is not null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdEqualTo(Long value) {
            addCriterion("family_id =", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotEqualTo(Long value) {
            addCriterion("family_id <>", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThan(Long value) {
            addCriterion("family_id >", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("family_id >=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThan(Long value) {
            addCriterion("family_id <", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("family_id <=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIn(List<Long> values) {
            addCriterion("family_id in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotIn(List<Long> values) {
            addCriterion("family_id not in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdBetween(Long value1, Long value2) {
            addCriterion("family_id between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("family_id not between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Date value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Date value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Date value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Date value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Date> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Date> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Date value1, Date value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("end_time is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(Date value) {
            addCriterion("end_time =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(Date value) {
            addCriterion("end_time <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(Date value) {
            addCriterion("end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("end_time >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(Date value) {
            addCriterion("end_time <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("end_time <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<Date> values) {
            addCriterion("end_time in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<Date> values) {
            addCriterion("end_time not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Date value1, Date value2) {
            addCriterion("end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("end_time not between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEffectiveRoomNumberIsNull() {
            addCriterion("effective_room_number is null");
            return (Criteria) this;
        }

        public Criteria andEffectiveRoomNumberIsNotNull() {
            addCriterion("effective_room_number is not null");
            return (Criteria) this;
        }

        public Criteria andEffectiveRoomNumberEqualTo(Integer value) {
            addCriterion("effective_room_number =", value, "effectiveRoomNumber");
            return (Criteria) this;
        }

        public Criteria andEffectiveRoomNumberNotEqualTo(Integer value) {
            addCriterion("effective_room_number <>", value, "effectiveRoomNumber");
            return (Criteria) this;
        }

        public Criteria andEffectiveRoomNumberGreaterThan(Integer value) {
            addCriterion("effective_room_number >", value, "effectiveRoomNumber");
            return (Criteria) this;
        }

        public Criteria andEffectiveRoomNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("effective_room_number >=", value, "effectiveRoomNumber");
            return (Criteria) this;
        }

        public Criteria andEffectiveRoomNumberLessThan(Integer value) {
            addCriterion("effective_room_number <", value, "effectiveRoomNumber");
            return (Criteria) this;
        }

        public Criteria andEffectiveRoomNumberLessThanOrEqualTo(Integer value) {
            addCriterion("effective_room_number <=", value, "effectiveRoomNumber");
            return (Criteria) this;
        }

        public Criteria andEffectiveRoomNumberIn(List<Integer> values) {
            addCriterion("effective_room_number in", values, "effectiveRoomNumber");
            return (Criteria) this;
        }

        public Criteria andEffectiveRoomNumberNotIn(List<Integer> values) {
            addCriterion("effective_room_number not in", values, "effectiveRoomNumber");
            return (Criteria) this;
        }

        public Criteria andEffectiveRoomNumberBetween(Integer value1, Integer value2) {
            addCriterion("effective_room_number between", value1, value2, "effectiveRoomNumber");
            return (Criteria) this;
        }

        public Criteria andEffectiveRoomNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("effective_room_number not between", value1, value2, "effectiveRoomNumber");
            return (Criteria) this;
        }

        public Criteria andFlowWeekOnWeekIsNull() {
            addCriterion("flow_week_on_week is null");
            return (Criteria) this;
        }

        public Criteria andFlowWeekOnWeekIsNotNull() {
            addCriterion("flow_week_on_week is not null");
            return (Criteria) this;
        }

        public Criteria andFlowWeekOnWeekEqualTo(BigDecimal value) {
            addCriterion("flow_week_on_week =", value, "flowWeekOnWeek");
            return (Criteria) this;
        }

        public Criteria andFlowWeekOnWeekNotEqualTo(BigDecimal value) {
            addCriterion("flow_week_on_week <>", value, "flowWeekOnWeek");
            return (Criteria) this;
        }

        public Criteria andFlowWeekOnWeekGreaterThan(BigDecimal value) {
            addCriterion("flow_week_on_week >", value, "flowWeekOnWeek");
            return (Criteria) this;
        }

        public Criteria andFlowWeekOnWeekGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("flow_week_on_week >=", value, "flowWeekOnWeek");
            return (Criteria) this;
        }

        public Criteria andFlowWeekOnWeekLessThan(BigDecimal value) {
            addCriterion("flow_week_on_week <", value, "flowWeekOnWeek");
            return (Criteria) this;
        }

        public Criteria andFlowWeekOnWeekLessThanOrEqualTo(BigDecimal value) {
            addCriterion("flow_week_on_week <=", value, "flowWeekOnWeek");
            return (Criteria) this;
        }

        public Criteria andFlowWeekOnWeekIn(List<BigDecimal> values) {
            addCriterion("flow_week_on_week in", values, "flowWeekOnWeek");
            return (Criteria) this;
        }

        public Criteria andFlowWeekOnWeekNotIn(List<BigDecimal> values) {
            addCriterion("flow_week_on_week not in", values, "flowWeekOnWeek");
            return (Criteria) this;
        }

        public Criteria andFlowWeekOnWeekBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("flow_week_on_week between", value1, value2, "flowWeekOnWeek");
            return (Criteria) this;
        }

        public Criteria andFlowWeekOnWeekNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("flow_week_on_week not between", value1, value2, "flowWeekOnWeek");
            return (Criteria) this;
        }

        public Criteria andRetainedNewRoomNumberIsNull() {
            addCriterion("retained_new_room_number is null");
            return (Criteria) this;
        }

        public Criteria andRetainedNewRoomNumberIsNotNull() {
            addCriterion("retained_new_room_number is not null");
            return (Criteria) this;
        }

        public Criteria andRetainedNewRoomNumberEqualTo(Integer value) {
            addCriterion("retained_new_room_number =", value, "retainedNewRoomNumber");
            return (Criteria) this;
        }

        public Criteria andRetainedNewRoomNumberNotEqualTo(Integer value) {
            addCriterion("retained_new_room_number <>", value, "retainedNewRoomNumber");
            return (Criteria) this;
        }

        public Criteria andRetainedNewRoomNumberGreaterThan(Integer value) {
            addCriterion("retained_new_room_number >", value, "retainedNewRoomNumber");
            return (Criteria) this;
        }

        public Criteria andRetainedNewRoomNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("retained_new_room_number >=", value, "retainedNewRoomNumber");
            return (Criteria) this;
        }

        public Criteria andRetainedNewRoomNumberLessThan(Integer value) {
            addCriterion("retained_new_room_number <", value, "retainedNewRoomNumber");
            return (Criteria) this;
        }

        public Criteria andRetainedNewRoomNumberLessThanOrEqualTo(Integer value) {
            addCriterion("retained_new_room_number <=", value, "retainedNewRoomNumber");
            return (Criteria) this;
        }

        public Criteria andRetainedNewRoomNumberIn(List<Integer> values) {
            addCriterion("retained_new_room_number in", values, "retainedNewRoomNumber");
            return (Criteria) this;
        }

        public Criteria andRetainedNewRoomNumberNotIn(List<Integer> values) {
            addCriterion("retained_new_room_number not in", values, "retainedNewRoomNumber");
            return (Criteria) this;
        }

        public Criteria andRetainedNewRoomNumberBetween(Integer value1, Integer value2) {
            addCriterion("retained_new_room_number between", value1, value2, "retainedNewRoomNumber");
            return (Criteria) this;
        }

        public Criteria andRetainedNewRoomNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("retained_new_room_number not between", value1, value2, "retainedNewRoomNumber");
            return (Criteria) this;
        }

        public Criteria andLostRoomNumberIsNull() {
            addCriterion("lost_room_number is null");
            return (Criteria) this;
        }

        public Criteria andLostRoomNumberIsNotNull() {
            addCriterion("lost_room_number is not null");
            return (Criteria) this;
        }

        public Criteria andLostRoomNumberEqualTo(Integer value) {
            addCriterion("lost_room_number =", value, "lostRoomNumber");
            return (Criteria) this;
        }

        public Criteria andLostRoomNumberNotEqualTo(Integer value) {
            addCriterion("lost_room_number <>", value, "lostRoomNumber");
            return (Criteria) this;
        }

        public Criteria andLostRoomNumberGreaterThan(Integer value) {
            addCriterion("lost_room_number >", value, "lostRoomNumber");
            return (Criteria) this;
        }

        public Criteria andLostRoomNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("lost_room_number >=", value, "lostRoomNumber");
            return (Criteria) this;
        }

        public Criteria andLostRoomNumberLessThan(Integer value) {
            addCriterion("lost_room_number <", value, "lostRoomNumber");
            return (Criteria) this;
        }

        public Criteria andLostRoomNumberLessThanOrEqualTo(Integer value) {
            addCriterion("lost_room_number <=", value, "lostRoomNumber");
            return (Criteria) this;
        }

        public Criteria andLostRoomNumberIn(List<Integer> values) {
            addCriterion("lost_room_number in", values, "lostRoomNumber");
            return (Criteria) this;
        }

        public Criteria andLostRoomNumberNotIn(List<Integer> values) {
            addCriterion("lost_room_number not in", values, "lostRoomNumber");
            return (Criteria) this;
        }

        public Criteria andLostRoomNumberBetween(Integer value1, Integer value2) {
            addCriterion("lost_room_number between", value1, value2, "lostRoomNumber");
            return (Criteria) this;
        }

        public Criteria andLostRoomNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("lost_room_number not between", value1, value2, "lostRoomNumber");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_data_family_week_data
     *
     * @mbg.generated do_not_delete_during_merge Wed Mar 26 16:46:07 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table wavecenter_data_family_week_data
     *
     * @mbg.generated Wed Mar 26 16:46:07 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}