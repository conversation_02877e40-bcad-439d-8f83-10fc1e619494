package fm.lizhi.ocean.wavecenter.datastore.award.family.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 公会奖励发放执行
 *
 * @date 2025-03-25 04:09:01
 */
@Table(name = "`wavecenter_family_award_deliver_execution`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcFamilyAwardDeliverExecution {
    /**
     * id
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 服务部署环境, TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 应用id
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 公会奖励发放记录id
     */
    @Column(name= "`record_id`")
    private Long recordId;

    /**
     * 资源发放类型, 1-推荐卡, 2-座驾, 3-勋章, 4-短号, 5-新厅名额
     */
    @Column(name= "`resource_deliver_type`")
    private Integer resourceDeliverType;

    /**
     * 资源数量
     */
    @Column(name= "`resource_number`")
    private Integer resourceNumber;

    /**
     * 资源有效期, 默认单位为天
     */
    @Column(name= "`resource_valid_period`")
    private Integer resourceValidPeriod;

    /**
     * 资源id, 如果资源类型需要具体的业务资源记录, 则是业务资源id
     */
    @Column(name= "`resource_id`")
    private Long resourceId;

    /**
     * 资源名称, 冗余存储具体的业务资源名称
     */
    @Column(name= "`resource_name`")
    private String resourceName;

    /**
     * 资源图片路径, 冗余存储具体的业务资源图片
     */
    @Column(name= "`resource_image_path`")
    private String resourceImagePath;

    /**
     * 状态, 1-发放中, 2-发放成功, 3-发放失败, 4-已回收
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 错误码, 用于记录失败信息, 成功时值为0
     */
    @Column(name= "`error_code`")
    private Integer errorCode;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 错误文本, 用于记录失败信息, 成功时值为空白
     */
    @Column(name= "`error_text`")
    private String errorText;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", appId=").append(appId);
        sb.append(", recordId=").append(recordId);
        sb.append(", resourceDeliverType=").append(resourceDeliverType);
        sb.append(", resourceNumber=").append(resourceNumber);
        sb.append(", resourceValidPeriod=").append(resourceValidPeriod);
        sb.append(", resourceId=").append(resourceId);
        sb.append(", resourceName=").append(resourceName);
        sb.append(", resourceImagePath=").append(resourceImagePath);
        sb.append(", status=").append(status);
        sb.append(", errorCode=").append(errorCode);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", errorText=").append(errorText);
        sb.append("]");
        return sb.toString();
    }
}