package fm.lizhi.ocean.wavecenter.datastore.award.singer.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 歌手装扮规则配置
 *
 * @date 2025-07-24 02:47:12
 */
@Table(name = "`singer_decorate_rule`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SingerDecorateRule {
    /**
     * ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 歌手认证等级
     */
    @Column(name= "`singer_type`")
    private Integer singerType;

    /**
     * 曲风
     */
    @Column(name= "`song_style`")
    private String songStyle;

    /**
     * 装扮类型
     */
    @Column(name= "`decorate_type`")
    private Integer decorateType;

    /**
     * 装扮ID
     */
    @Column(name= "`decorate_id`")
    private Long decorateId;

    /**
     * 环境：TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 是否启用 0:停用，1: 启用
     */
    @Column(name= "`enabled`")
    private Boolean enabled;

    /**
     * 是否删除 0未删除 1已删除
     */
    @Column(name= "`deleted`")
    private Boolean deleted;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 规则合并条件索引,结构为条件类型_条件值(BOOLEAN/曲风范围_曲风列表)
     */
    @Column(name= "`combine_condition_index`")
    private String combineConditionIndex;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", singerType=").append(singerType);
        sb.append(", songStyle=").append(songStyle);
        sb.append(", decorateType=").append(decorateType);
        sb.append(", decorateId=").append(decorateId);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", enabled=").append(enabled);
        sb.append(", deleted=").append(deleted);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", operator=").append(operator);
        sb.append(", combineConditionIndex=").append(combineConditionIndex);
        sb.append("]");
        return sb.toString();
    }
}