package fm.lizhi.ocean.wavecenter.datastore.award.singer.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SingerDecorateRuleExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_decorate_rule
     *
     * @mbg.generated Thu Jul 24 14:47:12 CST 2025
     */
    public SingerDecorateRuleExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_decorate_rule
     *
     * @mbg.generated Thu Jul 24 14:47:12 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_decorate_rule
     *
     * @mbg.generated Thu Jul 24 14:47:12 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_decorate_rule
     *
     * @mbg.generated Thu Jul 24 14:47:12 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_decorate_rule
     *
     * @mbg.generated Thu Jul 24 14:47:12 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_decorate_rule
     *
     * @mbg.generated Thu Jul 24 14:47:12 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_decorate_rule
     *
     * @mbg.generated Thu Jul 24 14:47:12 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_decorate_rule
     *
     * @mbg.generated Thu Jul 24 14:47:12 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_decorate_rule
     *
     * @mbg.generated Thu Jul 24 14:47:12 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_decorate_rule
     *
     * @mbg.generated Thu Jul 24 14:47:12 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_decorate_rule
     *
     * @mbg.generated Thu Jul 24 14:47:12 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return fm.lizhi.ocean.wavecenter.datastore.award.singer.entity.SingerDecorateRule.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table singer_decorate_rule
     *
     * @mbg.generated Thu Jul 24 14:47:12 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andSingerTypeIsNull() {
            addCriterion("singer_type is null");
            return (Criteria) this;
        }

        public Criteria andSingerTypeIsNotNull() {
            addCriterion("singer_type is not null");
            return (Criteria) this;
        }

        public Criteria andSingerTypeEqualTo(Integer value) {
            addCriterion("singer_type =", value, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeNotEqualTo(Integer value) {
            addCriterion("singer_type <>", value, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeGreaterThan(Integer value) {
            addCriterion("singer_type >", value, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("singer_type >=", value, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeLessThan(Integer value) {
            addCriterion("singer_type <", value, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeLessThanOrEqualTo(Integer value) {
            addCriterion("singer_type <=", value, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeIn(List<Integer> values) {
            addCriterion("singer_type in", values, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeNotIn(List<Integer> values) {
            addCriterion("singer_type not in", values, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeBetween(Integer value1, Integer value2) {
            addCriterion("singer_type between", value1, value2, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("singer_type not between", value1, value2, "singerType");
            return (Criteria) this;
        }

        public Criteria andSongStyleIsNull() {
            addCriterion("song_style is null");
            return (Criteria) this;
        }

        public Criteria andSongStyleIsNotNull() {
            addCriterion("song_style is not null");
            return (Criteria) this;
        }

        public Criteria andSongStyleEqualTo(String value) {
            addCriterion("song_style =", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleNotEqualTo(String value) {
            addCriterion("song_style <>", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleGreaterThan(String value) {
            addCriterion("song_style >", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleGreaterThanOrEqualTo(String value) {
            addCriterion("song_style >=", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleLessThan(String value) {
            addCriterion("song_style <", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleLessThanOrEqualTo(String value) {
            addCriterion("song_style <=", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleLike(String value) {
            addCriterion("song_style like", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleNotLike(String value) {
            addCriterion("song_style not like", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleIn(List<String> values) {
            addCriterion("song_style in", values, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleNotIn(List<String> values) {
            addCriterion("song_style not in", values, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleBetween(String value1, String value2) {
            addCriterion("song_style between", value1, value2, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleNotBetween(String value1, String value2) {
            addCriterion("song_style not between", value1, value2, "songStyle");
            return (Criteria) this;
        }

        public Criteria andDecorateTypeIsNull() {
            addCriterion("decorate_type is null");
            return (Criteria) this;
        }

        public Criteria andDecorateTypeIsNotNull() {
            addCriterion("decorate_type is not null");
            return (Criteria) this;
        }

        public Criteria andDecorateTypeEqualTo(Integer value) {
            addCriterion("decorate_type =", value, "decorateType");
            return (Criteria) this;
        }

        public Criteria andDecorateTypeNotEqualTo(Integer value) {
            addCriterion("decorate_type <>", value, "decorateType");
            return (Criteria) this;
        }

        public Criteria andDecorateTypeGreaterThan(Integer value) {
            addCriterion("decorate_type >", value, "decorateType");
            return (Criteria) this;
        }

        public Criteria andDecorateTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("decorate_type >=", value, "decorateType");
            return (Criteria) this;
        }

        public Criteria andDecorateTypeLessThan(Integer value) {
            addCriterion("decorate_type <", value, "decorateType");
            return (Criteria) this;
        }

        public Criteria andDecorateTypeLessThanOrEqualTo(Integer value) {
            addCriterion("decorate_type <=", value, "decorateType");
            return (Criteria) this;
        }

        public Criteria andDecorateTypeIn(List<Integer> values) {
            addCriterion("decorate_type in", values, "decorateType");
            return (Criteria) this;
        }

        public Criteria andDecorateTypeNotIn(List<Integer> values) {
            addCriterion("decorate_type not in", values, "decorateType");
            return (Criteria) this;
        }

        public Criteria andDecorateTypeBetween(Integer value1, Integer value2) {
            addCriterion("decorate_type between", value1, value2, "decorateType");
            return (Criteria) this;
        }

        public Criteria andDecorateTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("decorate_type not between", value1, value2, "decorateType");
            return (Criteria) this;
        }

        public Criteria andDecorateIdIsNull() {
            addCriterion("decorate_id is null");
            return (Criteria) this;
        }

        public Criteria andDecorateIdIsNotNull() {
            addCriterion("decorate_id is not null");
            return (Criteria) this;
        }

        public Criteria andDecorateIdEqualTo(Long value) {
            addCriterion("decorate_id =", value, "decorateId");
            return (Criteria) this;
        }

        public Criteria andDecorateIdNotEqualTo(Long value) {
            addCriterion("decorate_id <>", value, "decorateId");
            return (Criteria) this;
        }

        public Criteria andDecorateIdGreaterThan(Long value) {
            addCriterion("decorate_id >", value, "decorateId");
            return (Criteria) this;
        }

        public Criteria andDecorateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("decorate_id >=", value, "decorateId");
            return (Criteria) this;
        }

        public Criteria andDecorateIdLessThan(Long value) {
            addCriterion("decorate_id <", value, "decorateId");
            return (Criteria) this;
        }

        public Criteria andDecorateIdLessThanOrEqualTo(Long value) {
            addCriterion("decorate_id <=", value, "decorateId");
            return (Criteria) this;
        }

        public Criteria andDecorateIdIn(List<Long> values) {
            addCriterion("decorate_id in", values, "decorateId");
            return (Criteria) this;
        }

        public Criteria andDecorateIdNotIn(List<Long> values) {
            addCriterion("decorate_id not in", values, "decorateId");
            return (Criteria) this;
        }

        public Criteria andDecorateIdBetween(Long value1, Long value2) {
            addCriterion("decorate_id between", value1, value2, "decorateId");
            return (Criteria) this;
        }

        public Criteria andDecorateIdNotBetween(Long value1, Long value2) {
            addCriterion("decorate_id not between", value1, value2, "decorateId");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNull() {
            addCriterion("deploy_env is null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNotNull() {
            addCriterion("deploy_env is not null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvEqualTo(String value) {
            addCriterion("deploy_env =", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotEqualTo(String value) {
            addCriterion("deploy_env <>", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThan(String value) {
            addCriterion("deploy_env >", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThanOrEqualTo(String value) {
            addCriterion("deploy_env >=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThan(String value) {
            addCriterion("deploy_env <", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThanOrEqualTo(String value) {
            addCriterion("deploy_env <=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLike(String value) {
            addCriterion("deploy_env like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotLike(String value) {
            addCriterion("deploy_env not like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIn(List<String> values) {
            addCriterion("deploy_env in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotIn(List<String> values) {
            addCriterion("deploy_env not in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvBetween(String value1, String value2) {
            addCriterion("deploy_env between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotBetween(String value1, String value2) {
            addCriterion("deploy_env not between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andEnabledIsNull() {
            addCriterion("enabled is null");
            return (Criteria) this;
        }

        public Criteria andEnabledIsNotNull() {
            addCriterion("enabled is not null");
            return (Criteria) this;
        }

        public Criteria andEnabledEqualTo(Boolean value) {
            addCriterion("enabled =", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledNotEqualTo(Boolean value) {
            addCriterion("enabled <>", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledGreaterThan(Boolean value) {
            addCriterion("enabled >", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledGreaterThanOrEqualTo(Boolean value) {
            addCriterion("enabled >=", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledLessThan(Boolean value) {
            addCriterion("enabled <", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledLessThanOrEqualTo(Boolean value) {
            addCriterion("enabled <=", value, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledIn(List<Boolean> values) {
            addCriterion("enabled in", values, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledNotIn(List<Boolean> values) {
            addCriterion("enabled not in", values, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledBetween(Boolean value1, Boolean value2) {
            addCriterion("enabled between", value1, value2, "enabled");
            return (Criteria) this;
        }

        public Criteria andEnabledNotBetween(Boolean value1, Boolean value2) {
            addCriterion("enabled not between", value1, value2, "enabled");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Boolean value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Boolean value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Boolean value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Boolean value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Boolean> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Boolean> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNull() {
            addCriterion("operator is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNotNull() {
            addCriterion("operator is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorEqualTo(String value) {
            addCriterion("operator =", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotEqualTo(String value) {
            addCriterion("operator <>", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThan(String value) {
            addCriterion("operator >", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThanOrEqualTo(String value) {
            addCriterion("operator >=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThan(String value) {
            addCriterion("operator <", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThanOrEqualTo(String value) {
            addCriterion("operator <=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLike(String value) {
            addCriterion("operator like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotLike(String value) {
            addCriterion("operator not like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorIn(List<String> values) {
            addCriterion("operator in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotIn(List<String> values) {
            addCriterion("operator not in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorBetween(String value1, String value2) {
            addCriterion("operator between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotBetween(String value1, String value2) {
            addCriterion("operator not between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andCombineConditionIndexIsNull() {
            addCriterion("combine_condition_index is null");
            return (Criteria) this;
        }

        public Criteria andCombineConditionIndexIsNotNull() {
            addCriterion("combine_condition_index is not null");
            return (Criteria) this;
        }

        public Criteria andCombineConditionIndexEqualTo(String value) {
            addCriterion("combine_condition_index =", value, "combineConditionIndex");
            return (Criteria) this;
        }

        public Criteria andCombineConditionIndexNotEqualTo(String value) {
            addCriterion("combine_condition_index <>", value, "combineConditionIndex");
            return (Criteria) this;
        }

        public Criteria andCombineConditionIndexGreaterThan(String value) {
            addCriterion("combine_condition_index >", value, "combineConditionIndex");
            return (Criteria) this;
        }

        public Criteria andCombineConditionIndexGreaterThanOrEqualTo(String value) {
            addCriterion("combine_condition_index >=", value, "combineConditionIndex");
            return (Criteria) this;
        }

        public Criteria andCombineConditionIndexLessThan(String value) {
            addCriterion("combine_condition_index <", value, "combineConditionIndex");
            return (Criteria) this;
        }

        public Criteria andCombineConditionIndexLessThanOrEqualTo(String value) {
            addCriterion("combine_condition_index <=", value, "combineConditionIndex");
            return (Criteria) this;
        }

        public Criteria andCombineConditionIndexLike(String value) {
            addCriterion("combine_condition_index like", value, "combineConditionIndex");
            return (Criteria) this;
        }

        public Criteria andCombineConditionIndexNotLike(String value) {
            addCriterion("combine_condition_index not like", value, "combineConditionIndex");
            return (Criteria) this;
        }

        public Criteria andCombineConditionIndexIn(List<String> values) {
            addCriterion("combine_condition_index in", values, "combineConditionIndex");
            return (Criteria) this;
        }

        public Criteria andCombineConditionIndexNotIn(List<String> values) {
            addCriterion("combine_condition_index not in", values, "combineConditionIndex");
            return (Criteria) this;
        }

        public Criteria andCombineConditionIndexBetween(String value1, String value2) {
            addCriterion("combine_condition_index between", value1, value2, "combineConditionIndex");
            return (Criteria) this;
        }

        public Criteria andCombineConditionIndexNotBetween(String value1, String value2) {
            addCriterion("combine_condition_index not between", value1, value2, "combineConditionIndex");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table singer_decorate_rule
     *
     * @mbg.generated do_not_delete_during_merge Thu Jul 24 14:47:12 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table singer_decorate_rule
     *
     * @mbg.generated Thu Jul 24 14:47:12 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}