package fm.lizhi.ocean.wavecenter.datastore.award.singer.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 歌手装扮流水
 *
 * @date 2025-05-06 04:40:07
 */
@Table(name = "`singer_decorate_flow`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SingerDecorateFlow {
    /**
     * ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 用户ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 批次ID
     */
    @Column(name= "`transaction_id`")
    private Long transactionId;

    /**
     * 装扮类型
     */
    @Column(name= "`decorate_type`")
    private Integer decorateType;

    /**
     * 装扮ID
     */
    @Column(name= "`decorate_id`")
    private Long decorateId;

    /**
     * 环境：TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 发放时长，单位秒
     */
    @Column(name= "`duration`")
    private Long duration;

    /**
     * 歌手认证等级
     */
    @Column(name= "`singer_type`")
    private Integer singerType;

    /**
     * 过期时间
     */
    @Column(name= "`expire_time`")
    private Date expireTime;

    /**
     * 发放的规则 ID
     */
    @Column(name= "`rule_id`")
    private Long ruleId;

    /**
     * 状态 1: 未处理 2: 成功 3: 失败
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 操作类型 1: 发放 2: 回收
     */
    @Column(name= "`operate_type`")
    private Integer operateType;

    /**
     * 是否被回收，只有operate_type=发放时生效, 0 未回收,1回收
     */
    @Column(name= "`recycled`")
    private Boolean recycled;

    /**
     * 重试次数
     */
    @Column(name= "`retry_count`")
    private Integer retryCount;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 操作时间
     */
    @Column(name= "`operate_time`")
    private Date operateTime;

    /**
     * 操作原因
     */
    @Column(name= "`reason`")
    private String reason;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", appId=").append(appId);
        sb.append(", transactionId=").append(transactionId);
        sb.append(", decorateType=").append(decorateType);
        sb.append(", decorateId=").append(decorateId);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", duration=").append(duration);
        sb.append(", singerType=").append(singerType);
        sb.append(", expireTime=").append(expireTime);
        sb.append(", ruleId=").append(ruleId);
        sb.append(", status=").append(status);
        sb.append(", operateType=").append(operateType);
        sb.append(", recycled=").append(recycled);
        sb.append(", retryCount=").append(retryCount);
        sb.append(", operator=").append(operator);
        sb.append(", operateTime=").append(operateTime);
        sb.append(", reason=").append(reason);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}