package fm.lizhi.ocean.wavecenter.datastore.award.family.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 公会等级奖励条目
 *
 * @date 2025-03-20 11:32:45
 */
@Table(name = "`wavecenter_family_level_award_item`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcFamilyLevelAwardItem {
    /**
     * id
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 服务部署环境, TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 应用id
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 公会等级奖励规则id
     */
    @Column(name= "`rule_id`")
    private Long ruleId;

    /**
     * 资源类型, 类型不同读取的参数也不同, 1-推荐卡, 2-座驾, 3-勋章, 4-短号
     */
    @Column(name= "`resource_type`")
    private Integer resourceType;

    /**
     * 资源数量
     */
    @Column(name= "`resource_number`")
    private Integer resourceNumber;

    /**
     * 资源有效期, 默认单位为天
     */
    @Column(name= "`resource_valid_period`")
    private Integer resourceValidPeriod;

    /**
     * 资源id, 如果资源类型需要具体的业务资源记录, 则是业务资源id
     */
    @Column(name= "`resource_id`")
    private Long resourceId;

    /**
     * 是否已删除, 0-否, 1-是
     */
    @Column(name= "`deleted`")
    private Boolean deleted;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 创建者
     */
    @Column(name= "`creator`")
    private String creator;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 修改者
     */
    @Column(name= "`modifier`")
    private String modifier;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", appId=").append(appId);
        sb.append(", ruleId=").append(ruleId);
        sb.append(", resourceType=").append(resourceType);
        sb.append(", resourceNumber=").append(resourceNumber);
        sb.append(", resourceValidPeriod=").append(resourceValidPeriod);
        sb.append(", resourceId=").append(resourceId);
        sb.append(", deleted=").append(deleted);
        sb.append(", createTime=").append(createTime);
        sb.append(", creator=").append(creator);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", modifier=").append(modifier);
        sb.append("]");
        return sb.toString();
    }
}