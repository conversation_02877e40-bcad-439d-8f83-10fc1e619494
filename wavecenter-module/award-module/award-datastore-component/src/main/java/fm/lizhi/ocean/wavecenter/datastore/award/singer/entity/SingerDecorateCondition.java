package fm.lizhi.ocean.wavecenter.datastore.award.singer.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 歌手装扮-审核条件表
 *
 * @date 2025-07-24 02:47:12
 */
@Table(name = "`singer_decorate_condition`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SingerDecorateCondition {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 装扮规则ID
     */
    @Column(name= "`decorate_rule_id`")
    private Long decorateRuleId;

    /**
     * 1:曲风，2：原创
     */
    @Column(name= "`condition_type`")
    private Integer conditionType;

    /**
     * 1:固定曲风，2：任一曲风，3：全能曲风
     */
    @Column(name= "`song_style_type`")
    private Integer songStyleType;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 条件json
     */
    @Column(name= "`condition_json`")
    private String conditionJson;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", decorateRuleId=").append(decorateRuleId);
        sb.append(", conditionType=").append(conditionType);
        sb.append(", songStyleType=").append(songStyleType);
        sb.append(", appId=").append(appId);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", conditionJson=").append(conditionJson);
        sb.append("]");
        return sb.toString();
    }
}