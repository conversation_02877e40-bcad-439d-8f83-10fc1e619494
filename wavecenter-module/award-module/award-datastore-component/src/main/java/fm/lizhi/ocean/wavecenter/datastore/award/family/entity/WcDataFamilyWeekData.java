package fm.lizhi.ocean.wavecenter.datastore.award.family.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 公会周运营数据
 *
 * @date 2025-03-26 04:46:07
 */
@Table(name = "`wavecenter_data_family_week_data`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcDataFamilyWeekData {
    /**
     * id
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 业务id, 预留多业务设计, 目前只有PP-10919088
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 公会id
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 自然周的开始时间, 周一的00:00:00.000
     */
    @Column(name= "`start_time`")
    private Date startTime;

    /**
     * 自然周的结束时间, 周日的23:59:59.999, 冗余字段
     */
    @Column(name= "`end_time`")
    private Date endTime;

    /**
     * 有效厅数
     */
    @Column(name= "`effective_room_number`")
    private Integer effectiveRoomNumber;

    /**
     * 流水环比, 使用小数表示百分比, 如0.25表示25%
     */
    @Column(name= "`flow_week_on_week`")
    private BigDecimal flowWeekOnWeek;

    /**
     * 留存新厅数
     */
    @Column(name= "`retained_new_room_number`")
    private Integer retainedNewRoomNumber;

    /**
     * 流失厅数
     */
    @Column(name= "`lost_room_number`")
    private Integer lostRoomNumber;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", familyId=").append(familyId);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", effectiveRoomNumber=").append(effectiveRoomNumber);
        sb.append(", flowWeekOnWeek=").append(flowWeekOnWeek);
        sb.append(", retainedNewRoomNumber=").append(retainedNewRoomNumber);
        sb.append(", lostRoomNumber=").append(lostRoomNumber);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}