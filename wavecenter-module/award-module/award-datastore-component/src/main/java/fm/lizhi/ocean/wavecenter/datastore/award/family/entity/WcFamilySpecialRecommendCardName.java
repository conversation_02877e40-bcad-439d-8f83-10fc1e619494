package fm.lizhi.ocean.wavecenter.datastore.award.family.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 公会特殊推荐卡名单配置
 *
 * @date 2025-03-24 07:30:18
 */
@Table(name = "`wavecenter_family_special_recommend_card_name`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcFamilySpecialRecommendCardName {
    /**
     * id
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 服务部署环境, TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 应用id
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 用户id
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 推荐卡发放数量
     */
    @Column(name= "`number`")
    private Integer number;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 创建者
     */
    @Column(name= "`creator`")
    private String creator;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 修改者
     */
    @Column(name= "`modifier`")
    private String modifier;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", appId=").append(appId);
        sb.append(", userId=").append(userId);
        sb.append(", number=").append(number);
        sb.append(", createTime=").append(createTime);
        sb.append(", creator=").append(creator);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", modifier=").append(modifier);
        sb.append("]");
        return sb.toString();
    }
}