<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>fm.lizhi.ocean.wavecenter</groupId>
        <artifactId>anchor-module</artifactId>
        <version>2.0.6-SNAPSHOT</version>
    </parent>
    <artifactId>anchor-datastore-component</artifactId>

    <!-- 添加依赖，参考grow-datastore-component -->
    <dependencies>
        <!--region =================基础架构的依赖=================-->
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>datastore-mysql-spring-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--endregion-->
        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>wavecenter-base</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>