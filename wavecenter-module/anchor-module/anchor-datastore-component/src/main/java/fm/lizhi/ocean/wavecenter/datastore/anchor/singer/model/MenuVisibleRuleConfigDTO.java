package fm.lizhi.ocean.wavecenter.datastore.anchor.singer.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 菜单可见性规则配置
 */
@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MenuVisibleRuleConfigDTO {
    /**
     * 规则编码
     */
    private String ruleCode;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 显示数量
     */
    private Integer count;
}
