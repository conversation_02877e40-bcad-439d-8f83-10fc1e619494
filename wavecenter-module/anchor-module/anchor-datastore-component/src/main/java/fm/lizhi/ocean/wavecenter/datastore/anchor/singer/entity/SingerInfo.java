package fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 歌手库
 *
 * @date 2025-08-05 02:48:44
 */
@Table(name = "`singer_info`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SingerInfo {
    /**
     * ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 业务ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 歌手ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 厅主ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 家族ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 审核表 ID
     */
    @Column(name= "`singer_verify_id`")
    private Long singerVerifyId;

    /**
     * 歌手状态 1: 认证中 2: 生效中  3: 已淘汰
     */
    @Column(name= "`singer_status`")
    private Integer singerStatus;

    /**
     * 歌曲风格
     */
    @Column(name= "`song_style`")
    private String songStyle;

    /**
     * 是否原创歌手
     */
    @Column(name= "`original_singer`")
    private Boolean originalSinger;

    /**
     * 是否已发放奖励 0 未发放 1 已发放
     */
    @Column(name= "`rewards_issued`")
    private Boolean rewardsIssued;

    /**
     * 1: 新锐歌手，2：优质歌手，3：明星歌手
     */
    @Column(name= "`singer_type`")
    private Integer singerType;

    /**
     * 淘汰时间
     */
    @Column(name= "`elimination_time`")
    private Date eliminationTime;

    /**
     * 通过时间
     */
    @Column(name= "`audit_time`")
    private Date auditTime;

    /**
     * 淘汰原因
     */
    @Column(name= "`elimination_reason`")
    private String eliminationReason;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 环境：TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 联系方式
     */
    @Column(name= "`contact_number`")
    private String contactNumber;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", userId=").append(userId);
        sb.append(", njId=").append(njId);
        sb.append(", familyId=").append(familyId);
        sb.append(", singerVerifyId=").append(singerVerifyId);
        sb.append(", singerStatus=").append(singerStatus);
        sb.append(", songStyle=").append(songStyle);
        sb.append(", originalSinger=").append(originalSinger);
        sb.append(", rewardsIssued=").append(rewardsIssued);
        sb.append(", singerType=").append(singerType);
        sb.append(", eliminationTime=").append(eliminationTime);
        sb.append(", auditTime=").append(auditTime);
        sb.append(", eliminationReason=").append(eliminationReason);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", operator=").append(operator);
        sb.append(", createTime=").append(createTime);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", contactNumber=").append(contactNumber);
        sb.append("]");
        return sb.toString();
    }
}