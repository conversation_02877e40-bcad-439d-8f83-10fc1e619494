package fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 点唱厅申请记录表
 *
 * @date 2025-03-28 02:22:10
 */
@Table(name = "`singer_sing_hall_apply_record`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SingerSingHallApplyRecord {
    /**
     * ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 家族ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 厅主ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 审核状态，0-审核中，1-审核通过，2-审核未通过
     */
    @Column(name= "`audit_status`")
    private Integer auditStatus;

    /**
     * 提交时间
     */
    @Column(name= "`apply_time`")
    private Date applyTime;

    /**
     * 环境：TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 来源: 1:创作者运营后台 2:定时任务自动添加
     */
    @Column(name= "`source`")
    private Integer source;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 是否删除, 0未删除 1已删除
     */
    @Column(name= "`deleted`")
    private Boolean deleted;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", familyId=").append(familyId);
        sb.append(", njId=").append(njId);
        sb.append(", auditStatus=").append(auditStatus);
        sb.append(", applyTime=").append(applyTime);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", source=").append(source);
        sb.append(", operator=").append(operator);
        sb.append(", deleted=").append(deleted);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}