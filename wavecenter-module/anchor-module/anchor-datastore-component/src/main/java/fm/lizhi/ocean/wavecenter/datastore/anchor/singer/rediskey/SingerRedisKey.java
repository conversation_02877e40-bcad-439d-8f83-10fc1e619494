package fm.lizhi.ocean.wavecenter.datastore.anchor.singer.rediskey;

import fm.lizhi.ocean.wavecenter.base.constants.IRedisKey;
import fm.lizhi.ocean.wavecenter.base.util.KeyUtils;

/**
 * 歌手相关Redis Key
 * <AUTHOR>
 * <AUTHOR>
 */
public enum SingerRedisKey implements IRedisKey {

    /**
     * 用户是否是歌手
     * WC_SINGER_IS_SINGER_[appId]_[userId]
     * value=1/0
     */
    IS_SINGER(true),

    /**
     * 厅内歌手总数
     * WC_SINGER_TOTAL_COUNT_IN_HALL_[njId]
     * value=歌手数量
     */
    SINGER_TOTAL_COUNT_IN_HALL,
    /**
     * 淘汰歌手ZSET
     * WAVECENTER_SINGER_ELIMINATE_SINGER_ZSET_[appId]
     * value=歌手ID
     * score=解约时间戳
     */
    ELIMINATE_SINGER_ZSET(true),

    /**
     * 歌手信息修改锁
     * WAVECENTER_SINGER_INFO_UPDATE_LOCK_[appId]_[njId]
     * value: 时间戳
     */
    SINGER_INFO_UPDATE_LOCK(true),

    /**
     * 歌手审核申请锁
     * WAVECENTER_SINGER_VERIFY_APPLY_LOCK_[appId]_[idCardNumber]
     * value: 时间戳
     */
    SINGER_VERIFY_APPLY_LOCK(true),

    /**
     * 厅内歌手总数
     * WC_SINGER_TOTAL_COUNT_METRIC_IN_HALL_KEY_[njId]
     * value=歌手数量
     */
    SINGER_TOTAL_COUNT_METRIC_IN_HALL_KEY,

    /**
     * 审核入口配置
     * WAVECENTER_SINGER_APPLY_MENU_CONFIG_[appId]_[singerType]_[deploy_env]
     * value=配置信息
     */
    APPLY_MENU_CONFIG,

    /**
     * 歌手认证申请失败标记
     * key：SINGER_VERIFY_NO_PASS_MARK_#{appId}_#{userId}
     * value：时间戳
     * 过期时间：动态配置
     */
    SINGER_VERIFY_NO_PASS_MARK,

    /**
     * 上周歌手收入缓存
     * key：WAVECENTER_SINGER_LAST_WEEK_INCOME_DATA_#{appId}_#{userId}_#{weekStartDate}
     * value：收入
     * 过期时间：7day+随机时间戳
     */
    LAST_WEEK_INCOME_DATA,

    /**
     * 上周歌手签约厅的厅收入 缓存
     * key：WAVECENTER_SINGER_ROOM_LAST_WEEK_INCOME_DATA_#{appId}_#{userId}_#{weekStartDate}
     * value：收入
     * 过期时间：7day+随机时间戳
     */
    ROOM_LAST_WEEK_INCOME_DATA
    ;

    /**
     * 是否区分环境
     */
    private boolean afx;

    SingerRedisKey() {
    }

    SingerRedisKey(boolean afx) {
        this.afx = afx;
    }

    @Override
    public String getPrefix() {
        return KeyUtils.getPrefix("WAVECENTER_SINGER", afx);
    }

    @Override
    public String getName() {
        return this.name();
    }
} 