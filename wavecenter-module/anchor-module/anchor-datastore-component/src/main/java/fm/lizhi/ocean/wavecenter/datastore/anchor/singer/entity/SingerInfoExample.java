package fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SingerInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_info
     *
     * @mbg.generated Tue Aug 05 14:48:44 CST 2025
     */
    public SingerInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_info
     *
     * @mbg.generated Tue Aug 05 14:48:44 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_info
     *
     * @mbg.generated Tue Aug 05 14:48:44 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_info
     *
     * @mbg.generated Tue Aug 05 14:48:44 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_info
     *
     * @mbg.generated Tue Aug 05 14:48:44 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_info
     *
     * @mbg.generated Tue Aug 05 14:48:44 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_info
     *
     * @mbg.generated Tue Aug 05 14:48:44 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_info
     *
     * @mbg.generated Tue Aug 05 14:48:44 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_info
     *
     * @mbg.generated Tue Aug 05 14:48:44 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_info
     *
     * @mbg.generated Tue Aug 05 14:48:44 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_info
     *
     * @mbg.generated Tue Aug 05 14:48:44 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerInfo.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table singer_info
     *
     * @mbg.generated Tue Aug 05 14:48:44 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andNjIdIsNull() {
            addCriterion("nj_id is null");
            return (Criteria) this;
        }

        public Criteria andNjIdIsNotNull() {
            addCriterion("nj_id is not null");
            return (Criteria) this;
        }

        public Criteria andNjIdEqualTo(Long value) {
            addCriterion("nj_id =", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotEqualTo(Long value) {
            addCriterion("nj_id <>", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdGreaterThan(Long value) {
            addCriterion("nj_id >", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdGreaterThanOrEqualTo(Long value) {
            addCriterion("nj_id >=", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdLessThan(Long value) {
            addCriterion("nj_id <", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdLessThanOrEqualTo(Long value) {
            addCriterion("nj_id <=", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdIn(List<Long> values) {
            addCriterion("nj_id in", values, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotIn(List<Long> values) {
            addCriterion("nj_id not in", values, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdBetween(Long value1, Long value2) {
            addCriterion("nj_id between", value1, value2, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotBetween(Long value1, Long value2) {
            addCriterion("nj_id not between", value1, value2, "njId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNull() {
            addCriterion("family_id is null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNotNull() {
            addCriterion("family_id is not null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdEqualTo(Long value) {
            addCriterion("family_id =", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotEqualTo(Long value) {
            addCriterion("family_id <>", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThan(Long value) {
            addCriterion("family_id >", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("family_id >=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThan(Long value) {
            addCriterion("family_id <", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("family_id <=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIn(List<Long> values) {
            addCriterion("family_id in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotIn(List<Long> values) {
            addCriterion("family_id not in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdBetween(Long value1, Long value2) {
            addCriterion("family_id between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("family_id not between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andSingerVerifyIdIsNull() {
            addCriterion("singer_verify_id is null");
            return (Criteria) this;
        }

        public Criteria andSingerVerifyIdIsNotNull() {
            addCriterion("singer_verify_id is not null");
            return (Criteria) this;
        }

        public Criteria andSingerVerifyIdEqualTo(Long value) {
            addCriterion("singer_verify_id =", value, "singerVerifyId");
            return (Criteria) this;
        }

        public Criteria andSingerVerifyIdNotEqualTo(Long value) {
            addCriterion("singer_verify_id <>", value, "singerVerifyId");
            return (Criteria) this;
        }

        public Criteria andSingerVerifyIdGreaterThan(Long value) {
            addCriterion("singer_verify_id >", value, "singerVerifyId");
            return (Criteria) this;
        }

        public Criteria andSingerVerifyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("singer_verify_id >=", value, "singerVerifyId");
            return (Criteria) this;
        }

        public Criteria andSingerVerifyIdLessThan(Long value) {
            addCriterion("singer_verify_id <", value, "singerVerifyId");
            return (Criteria) this;
        }

        public Criteria andSingerVerifyIdLessThanOrEqualTo(Long value) {
            addCriterion("singer_verify_id <=", value, "singerVerifyId");
            return (Criteria) this;
        }

        public Criteria andSingerVerifyIdIn(List<Long> values) {
            addCriterion("singer_verify_id in", values, "singerVerifyId");
            return (Criteria) this;
        }

        public Criteria andSingerVerifyIdNotIn(List<Long> values) {
            addCriterion("singer_verify_id not in", values, "singerVerifyId");
            return (Criteria) this;
        }

        public Criteria andSingerVerifyIdBetween(Long value1, Long value2) {
            addCriterion("singer_verify_id between", value1, value2, "singerVerifyId");
            return (Criteria) this;
        }

        public Criteria andSingerVerifyIdNotBetween(Long value1, Long value2) {
            addCriterion("singer_verify_id not between", value1, value2, "singerVerifyId");
            return (Criteria) this;
        }

        public Criteria andSingerStatusIsNull() {
            addCriterion("singer_status is null");
            return (Criteria) this;
        }

        public Criteria andSingerStatusIsNotNull() {
            addCriterion("singer_status is not null");
            return (Criteria) this;
        }

        public Criteria andSingerStatusEqualTo(Integer value) {
            addCriterion("singer_status =", value, "singerStatus");
            return (Criteria) this;
        }

        public Criteria andSingerStatusNotEqualTo(Integer value) {
            addCriterion("singer_status <>", value, "singerStatus");
            return (Criteria) this;
        }

        public Criteria andSingerStatusGreaterThan(Integer value) {
            addCriterion("singer_status >", value, "singerStatus");
            return (Criteria) this;
        }

        public Criteria andSingerStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("singer_status >=", value, "singerStatus");
            return (Criteria) this;
        }

        public Criteria andSingerStatusLessThan(Integer value) {
            addCriterion("singer_status <", value, "singerStatus");
            return (Criteria) this;
        }

        public Criteria andSingerStatusLessThanOrEqualTo(Integer value) {
            addCriterion("singer_status <=", value, "singerStatus");
            return (Criteria) this;
        }

        public Criteria andSingerStatusIn(List<Integer> values) {
            addCriterion("singer_status in", values, "singerStatus");
            return (Criteria) this;
        }

        public Criteria andSingerStatusNotIn(List<Integer> values) {
            addCriterion("singer_status not in", values, "singerStatus");
            return (Criteria) this;
        }

        public Criteria andSingerStatusBetween(Integer value1, Integer value2) {
            addCriterion("singer_status between", value1, value2, "singerStatus");
            return (Criteria) this;
        }

        public Criteria andSingerStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("singer_status not between", value1, value2, "singerStatus");
            return (Criteria) this;
        }

        public Criteria andSongStyleIsNull() {
            addCriterion("song_style is null");
            return (Criteria) this;
        }

        public Criteria andSongStyleIsNotNull() {
            addCriterion("song_style is not null");
            return (Criteria) this;
        }

        public Criteria andSongStyleEqualTo(String value) {
            addCriterion("song_style =", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleNotEqualTo(String value) {
            addCriterion("song_style <>", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleGreaterThan(String value) {
            addCriterion("song_style >", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleGreaterThanOrEqualTo(String value) {
            addCriterion("song_style >=", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleLessThan(String value) {
            addCriterion("song_style <", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleLessThanOrEqualTo(String value) {
            addCriterion("song_style <=", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleLike(String value) {
            addCriterion("song_style like", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleNotLike(String value) {
            addCriterion("song_style not like", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleIn(List<String> values) {
            addCriterion("song_style in", values, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleNotIn(List<String> values) {
            addCriterion("song_style not in", values, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleBetween(String value1, String value2) {
            addCriterion("song_style between", value1, value2, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleNotBetween(String value1, String value2) {
            addCriterion("song_style not between", value1, value2, "songStyle");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerIsNull() {
            addCriterion("original_singer is null");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerIsNotNull() {
            addCriterion("original_singer is not null");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerEqualTo(Boolean value) {
            addCriterion("original_singer =", value, "originalSinger");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerNotEqualTo(Boolean value) {
            addCriterion("original_singer <>", value, "originalSinger");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerGreaterThan(Boolean value) {
            addCriterion("original_singer >", value, "originalSinger");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerGreaterThanOrEqualTo(Boolean value) {
            addCriterion("original_singer >=", value, "originalSinger");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerLessThan(Boolean value) {
            addCriterion("original_singer <", value, "originalSinger");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerLessThanOrEqualTo(Boolean value) {
            addCriterion("original_singer <=", value, "originalSinger");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerIn(List<Boolean> values) {
            addCriterion("original_singer in", values, "originalSinger");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerNotIn(List<Boolean> values) {
            addCriterion("original_singer not in", values, "originalSinger");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerBetween(Boolean value1, Boolean value2) {
            addCriterion("original_singer between", value1, value2, "originalSinger");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerNotBetween(Boolean value1, Boolean value2) {
            addCriterion("original_singer not between", value1, value2, "originalSinger");
            return (Criteria) this;
        }

        public Criteria andRewardsIssuedIsNull() {
            addCriterion("rewards_issued is null");
            return (Criteria) this;
        }

        public Criteria andRewardsIssuedIsNotNull() {
            addCriterion("rewards_issued is not null");
            return (Criteria) this;
        }

        public Criteria andRewardsIssuedEqualTo(Boolean value) {
            addCriterion("rewards_issued =", value, "rewardsIssued");
            return (Criteria) this;
        }

        public Criteria andRewardsIssuedNotEqualTo(Boolean value) {
            addCriterion("rewards_issued <>", value, "rewardsIssued");
            return (Criteria) this;
        }

        public Criteria andRewardsIssuedGreaterThan(Boolean value) {
            addCriterion("rewards_issued >", value, "rewardsIssued");
            return (Criteria) this;
        }

        public Criteria andRewardsIssuedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("rewards_issued >=", value, "rewardsIssued");
            return (Criteria) this;
        }

        public Criteria andRewardsIssuedLessThan(Boolean value) {
            addCriterion("rewards_issued <", value, "rewardsIssued");
            return (Criteria) this;
        }

        public Criteria andRewardsIssuedLessThanOrEqualTo(Boolean value) {
            addCriterion("rewards_issued <=", value, "rewardsIssued");
            return (Criteria) this;
        }

        public Criteria andRewardsIssuedIn(List<Boolean> values) {
            addCriterion("rewards_issued in", values, "rewardsIssued");
            return (Criteria) this;
        }

        public Criteria andRewardsIssuedNotIn(List<Boolean> values) {
            addCriterion("rewards_issued not in", values, "rewardsIssued");
            return (Criteria) this;
        }

        public Criteria andRewardsIssuedBetween(Boolean value1, Boolean value2) {
            addCriterion("rewards_issued between", value1, value2, "rewardsIssued");
            return (Criteria) this;
        }

        public Criteria andRewardsIssuedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("rewards_issued not between", value1, value2, "rewardsIssued");
            return (Criteria) this;
        }

        public Criteria andSingerTypeIsNull() {
            addCriterion("singer_type is null");
            return (Criteria) this;
        }

        public Criteria andSingerTypeIsNotNull() {
            addCriterion("singer_type is not null");
            return (Criteria) this;
        }

        public Criteria andSingerTypeEqualTo(Integer value) {
            addCriterion("singer_type =", value, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeNotEqualTo(Integer value) {
            addCriterion("singer_type <>", value, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeGreaterThan(Integer value) {
            addCriterion("singer_type >", value, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("singer_type >=", value, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeLessThan(Integer value) {
            addCriterion("singer_type <", value, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeLessThanOrEqualTo(Integer value) {
            addCriterion("singer_type <=", value, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeIn(List<Integer> values) {
            addCriterion("singer_type in", values, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeNotIn(List<Integer> values) {
            addCriterion("singer_type not in", values, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeBetween(Integer value1, Integer value2) {
            addCriterion("singer_type between", value1, value2, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("singer_type not between", value1, value2, "singerType");
            return (Criteria) this;
        }

        public Criteria andEliminationTimeIsNull() {
            addCriterion("elimination_time is null");
            return (Criteria) this;
        }

        public Criteria andEliminationTimeIsNotNull() {
            addCriterion("elimination_time is not null");
            return (Criteria) this;
        }

        public Criteria andEliminationTimeEqualTo(Date value) {
            addCriterion("elimination_time =", value, "eliminationTime");
            return (Criteria) this;
        }

        public Criteria andEliminationTimeNotEqualTo(Date value) {
            addCriterion("elimination_time <>", value, "eliminationTime");
            return (Criteria) this;
        }

        public Criteria andEliminationTimeGreaterThan(Date value) {
            addCriterion("elimination_time >", value, "eliminationTime");
            return (Criteria) this;
        }

        public Criteria andEliminationTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("elimination_time >=", value, "eliminationTime");
            return (Criteria) this;
        }

        public Criteria andEliminationTimeLessThan(Date value) {
            addCriterion("elimination_time <", value, "eliminationTime");
            return (Criteria) this;
        }

        public Criteria andEliminationTimeLessThanOrEqualTo(Date value) {
            addCriterion("elimination_time <=", value, "eliminationTime");
            return (Criteria) this;
        }

        public Criteria andEliminationTimeIn(List<Date> values) {
            addCriterion("elimination_time in", values, "eliminationTime");
            return (Criteria) this;
        }

        public Criteria andEliminationTimeNotIn(List<Date> values) {
            addCriterion("elimination_time not in", values, "eliminationTime");
            return (Criteria) this;
        }

        public Criteria andEliminationTimeBetween(Date value1, Date value2) {
            addCriterion("elimination_time between", value1, value2, "eliminationTime");
            return (Criteria) this;
        }

        public Criteria andEliminationTimeNotBetween(Date value1, Date value2) {
            addCriterion("elimination_time not between", value1, value2, "eliminationTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeIsNull() {
            addCriterion("audit_time is null");
            return (Criteria) this;
        }

        public Criteria andAuditTimeIsNotNull() {
            addCriterion("audit_time is not null");
            return (Criteria) this;
        }

        public Criteria andAuditTimeEqualTo(Date value) {
            addCriterion("audit_time =", value, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeNotEqualTo(Date value) {
            addCriterion("audit_time <>", value, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeGreaterThan(Date value) {
            addCriterion("audit_time >", value, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("audit_time >=", value, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeLessThan(Date value) {
            addCriterion("audit_time <", value, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeLessThanOrEqualTo(Date value) {
            addCriterion("audit_time <=", value, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeIn(List<Date> values) {
            addCriterion("audit_time in", values, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeNotIn(List<Date> values) {
            addCriterion("audit_time not in", values, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeBetween(Date value1, Date value2) {
            addCriterion("audit_time between", value1, value2, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeNotBetween(Date value1, Date value2) {
            addCriterion("audit_time not between", value1, value2, "auditTime");
            return (Criteria) this;
        }

        public Criteria andEliminationReasonIsNull() {
            addCriterion("elimination_reason is null");
            return (Criteria) this;
        }

        public Criteria andEliminationReasonIsNotNull() {
            addCriterion("elimination_reason is not null");
            return (Criteria) this;
        }

        public Criteria andEliminationReasonEqualTo(String value) {
            addCriterion("elimination_reason =", value, "eliminationReason");
            return (Criteria) this;
        }

        public Criteria andEliminationReasonNotEqualTo(String value) {
            addCriterion("elimination_reason <>", value, "eliminationReason");
            return (Criteria) this;
        }

        public Criteria andEliminationReasonGreaterThan(String value) {
            addCriterion("elimination_reason >", value, "eliminationReason");
            return (Criteria) this;
        }

        public Criteria andEliminationReasonGreaterThanOrEqualTo(String value) {
            addCriterion("elimination_reason >=", value, "eliminationReason");
            return (Criteria) this;
        }

        public Criteria andEliminationReasonLessThan(String value) {
            addCriterion("elimination_reason <", value, "eliminationReason");
            return (Criteria) this;
        }

        public Criteria andEliminationReasonLessThanOrEqualTo(String value) {
            addCriterion("elimination_reason <=", value, "eliminationReason");
            return (Criteria) this;
        }

        public Criteria andEliminationReasonLike(String value) {
            addCriterion("elimination_reason like", value, "eliminationReason");
            return (Criteria) this;
        }

        public Criteria andEliminationReasonNotLike(String value) {
            addCriterion("elimination_reason not like", value, "eliminationReason");
            return (Criteria) this;
        }

        public Criteria andEliminationReasonIn(List<String> values) {
            addCriterion("elimination_reason in", values, "eliminationReason");
            return (Criteria) this;
        }

        public Criteria andEliminationReasonNotIn(List<String> values) {
            addCriterion("elimination_reason not in", values, "eliminationReason");
            return (Criteria) this;
        }

        public Criteria andEliminationReasonBetween(String value1, String value2) {
            addCriterion("elimination_reason between", value1, value2, "eliminationReason");
            return (Criteria) this;
        }

        public Criteria andEliminationReasonNotBetween(String value1, String value2) {
            addCriterion("elimination_reason not between", value1, value2, "eliminationReason");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNull() {
            addCriterion("operator is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNotNull() {
            addCriterion("operator is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorEqualTo(String value) {
            addCriterion("operator =", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotEqualTo(String value) {
            addCriterion("operator <>", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThan(String value) {
            addCriterion("operator >", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThanOrEqualTo(String value) {
            addCriterion("operator >=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThan(String value) {
            addCriterion("operator <", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThanOrEqualTo(String value) {
            addCriterion("operator <=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLike(String value) {
            addCriterion("operator like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotLike(String value) {
            addCriterion("operator not like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorIn(List<String> values) {
            addCriterion("operator in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotIn(List<String> values) {
            addCriterion("operator not in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorBetween(String value1, String value2) {
            addCriterion("operator between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotBetween(String value1, String value2) {
            addCriterion("operator not between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNull() {
            addCriterion("deploy_env is null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNotNull() {
            addCriterion("deploy_env is not null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvEqualTo(String value) {
            addCriterion("deploy_env =", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotEqualTo(String value) {
            addCriterion("deploy_env <>", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThan(String value) {
            addCriterion("deploy_env >", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThanOrEqualTo(String value) {
            addCriterion("deploy_env >=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThan(String value) {
            addCriterion("deploy_env <", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThanOrEqualTo(String value) {
            addCriterion("deploy_env <=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLike(String value) {
            addCriterion("deploy_env like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotLike(String value) {
            addCriterion("deploy_env not like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIn(List<String> values) {
            addCriterion("deploy_env in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotIn(List<String> values) {
            addCriterion("deploy_env not in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvBetween(String value1, String value2) {
            addCriterion("deploy_env between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotBetween(String value1, String value2) {
            addCriterion("deploy_env not between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andContactNumberIsNull() {
            addCriterion("contact_number is null");
            return (Criteria) this;
        }

        public Criteria andContactNumberIsNotNull() {
            addCriterion("contact_number is not null");
            return (Criteria) this;
        }

        public Criteria andContactNumberEqualTo(String value) {
            addCriterion("contact_number =", value, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberNotEqualTo(String value) {
            addCriterion("contact_number <>", value, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberGreaterThan(String value) {
            addCriterion("contact_number >", value, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberGreaterThanOrEqualTo(String value) {
            addCriterion("contact_number >=", value, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberLessThan(String value) {
            addCriterion("contact_number <", value, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberLessThanOrEqualTo(String value) {
            addCriterion("contact_number <=", value, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberLike(String value) {
            addCriterion("contact_number like", value, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberNotLike(String value) {
            addCriterion("contact_number not like", value, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberIn(List<String> values) {
            addCriterion("contact_number in", values, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberNotIn(List<String> values) {
            addCriterion("contact_number not in", values, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberBetween(String value1, String value2) {
            addCriterion("contact_number between", value1, value2, "contactNumber");
            return (Criteria) this;
        }

        public Criteria andContactNumberNotBetween(String value1, String value2) {
            addCriterion("contact_number not between", value1, value2, "contactNumber");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table singer_info
     *
     * @mbg.generated do_not_delete_during_merge Tue Aug 05 14:48:44 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table singer_info
     *
     * @mbg.generated Tue Aug 05 14:48:44 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}