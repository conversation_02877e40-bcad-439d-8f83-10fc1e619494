package fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 歌手库操作记录
 *
 * @date 2025-04-16 05:49:03
 */
@Table(name = "`singer_operate_record`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SingerOperateRecord {
    /**
     * ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 用户ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 家族ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 厅主ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 歌曲名称
     */
    @Column(name= "`song_name`")
    private String songName;

    /**
     * 歌曲风格
     */
    @Column(name= "`song_style`")
    private String songStyle;

    /**
     * 是否原创
     */
    @Column(name= "`original_singer`")
    private Boolean originalSinger;

    /**
     * 通过审核时间
     */
    @Column(name= "`pass_time`")
    private Date passTime;

    /**
     * 歌手类型, 1: 新锐歌手，2：优质歌手，3：明星歌手
     */
    @Column(name= "`singer_type`")
    private Integer singerType;

    /**
     * 淘汰原因
     */
    @Column(name= "`elimination_reason`")
    private String eliminationReason;

    /**
     * 操作类型, 1 认证通过; 2 淘汰; 3 晋升
     */
    @Column(name= "`operate_type`")
    private Integer operateType;

    /**
     * 环境：TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", userId=").append(userId);
        sb.append(", familyId=").append(familyId);
        sb.append(", njId=").append(njId);
        sb.append(", songName=").append(songName);
        sb.append(", songStyle=").append(songStyle);
        sb.append(", originalSinger=").append(originalSinger);
        sb.append(", passTime=").append(passTime);
        sb.append(", singerType=").append(singerType);
        sb.append(", eliminationReason=").append(eliminationReason);
        sb.append(", operateType=").append(operateType);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", operator=").append(operator);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}