package fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class SingerDataDayExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_data_day
     *
     * @mbg.generated Wed Jul 30 17:11:10 CST 2025
     */
    public SingerDataDayExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_data_day
     *
     * @mbg.generated Wed Jul 30 17:11:10 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_data_day
     *
     * @mbg.generated Wed Jul 30 17:11:10 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_data_day
     *
     * @mbg.generated Wed Jul 30 17:11:10 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_data_day
     *
     * @mbg.generated Wed Jul 30 17:11:10 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_data_day
     *
     * @mbg.generated Wed Jul 30 17:11:10 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_data_day
     *
     * @mbg.generated Wed Jul 30 17:11:10 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_data_day
     *
     * @mbg.generated Wed Jul 30 17:11:10 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_data_day
     *
     * @mbg.generated Wed Jul 30 17:11:10 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_data_day
     *
     * @mbg.generated Wed Jul 30 17:11:10 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_data_day
     *
     * @mbg.generated Wed Jul 30 17:11:10 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerDataDay.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table singer_data_day
     *
     * @mbg.generated Wed Jul 30 17:11:10 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andStatDateIsNull() {
            addCriterion("stat_date is null");
            return (Criteria) this;
        }

        public Criteria andStatDateIsNotNull() {
            addCriterion("stat_date is not null");
            return (Criteria) this;
        }

        public Criteria andStatDateEqualTo(Date value) {
            addCriterionForJDBCDate("stat_date =", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("stat_date <>", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateGreaterThan(Date value) {
            addCriterionForJDBCDate("stat_date >", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("stat_date >=", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateLessThan(Date value) {
            addCriterionForJDBCDate("stat_date <", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("stat_date <=", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateIn(List<Date> values) {
            addCriterionForJDBCDate("stat_date in", values, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("stat_date not in", values, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("stat_date between", value1, value2, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("stat_date not between", value1, value2, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateValueIsNull() {
            addCriterion("stat_date_value is null");
            return (Criteria) this;
        }

        public Criteria andStatDateValueIsNotNull() {
            addCriterion("stat_date_value is not null");
            return (Criteria) this;
        }

        public Criteria andStatDateValueEqualTo(Integer value) {
            addCriterion("stat_date_value =", value, "statDateValue");
            return (Criteria) this;
        }

        public Criteria andStatDateValueNotEqualTo(Integer value) {
            addCriterion("stat_date_value <>", value, "statDateValue");
            return (Criteria) this;
        }

        public Criteria andStatDateValueGreaterThan(Integer value) {
            addCriterion("stat_date_value >", value, "statDateValue");
            return (Criteria) this;
        }

        public Criteria andStatDateValueGreaterThanOrEqualTo(Integer value) {
            addCriterion("stat_date_value >=", value, "statDateValue");
            return (Criteria) this;
        }

        public Criteria andStatDateValueLessThan(Integer value) {
            addCriterion("stat_date_value <", value, "statDateValue");
            return (Criteria) this;
        }

        public Criteria andStatDateValueLessThanOrEqualTo(Integer value) {
            addCriterion("stat_date_value <=", value, "statDateValue");
            return (Criteria) this;
        }

        public Criteria andStatDateValueIn(List<Integer> values) {
            addCriterion("stat_date_value in", values, "statDateValue");
            return (Criteria) this;
        }

        public Criteria andStatDateValueNotIn(List<Integer> values) {
            addCriterion("stat_date_value not in", values, "statDateValue");
            return (Criteria) this;
        }

        public Criteria andStatDateValueBetween(Integer value1, Integer value2) {
            addCriterion("stat_date_value between", value1, value2, "statDateValue");
            return (Criteria) this;
        }

        public Criteria andStatDateValueNotBetween(Integer value1, Integer value2) {
            addCriterion("stat_date_value not between", value1, value2, "statDateValue");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andNjIdIsNull() {
            addCriterion("nj_id is null");
            return (Criteria) this;
        }

        public Criteria andNjIdIsNotNull() {
            addCriterion("nj_id is not null");
            return (Criteria) this;
        }

        public Criteria andNjIdEqualTo(Long value) {
            addCriterion("nj_id =", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotEqualTo(Long value) {
            addCriterion("nj_id <>", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdGreaterThan(Long value) {
            addCriterion("nj_id >", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdGreaterThanOrEqualTo(Long value) {
            addCriterion("nj_id >=", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdLessThan(Long value) {
            addCriterion("nj_id <", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdLessThanOrEqualTo(Long value) {
            addCriterion("nj_id <=", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdIn(List<Long> values) {
            addCriterion("nj_id in", values, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotIn(List<Long> values) {
            addCriterion("nj_id not in", values, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdBetween(Long value1, Long value2) {
            addCriterion("nj_id between", value1, value2, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotBetween(Long value1, Long value2) {
            addCriterion("nj_id not between", value1, value2, "njId");
            return (Criteria) this;
        }

        public Criteria andSingerTypeIsNull() {
            addCriterion("singer_type is null");
            return (Criteria) this;
        }

        public Criteria andSingerTypeIsNotNull() {
            addCriterion("singer_type is not null");
            return (Criteria) this;
        }

        public Criteria andSingerTypeEqualTo(Integer value) {
            addCriterion("singer_type =", value, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeNotEqualTo(Integer value) {
            addCriterion("singer_type <>", value, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeGreaterThan(Integer value) {
            addCriterion("singer_type >", value, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("singer_type >=", value, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeLessThan(Integer value) {
            addCriterion("singer_type <", value, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeLessThanOrEqualTo(Integer value) {
            addCriterion("singer_type <=", value, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeIn(List<Integer> values) {
            addCriterion("singer_type in", values, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeNotIn(List<Integer> values) {
            addCriterion("singer_type not in", values, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeBetween(Integer value1, Integer value2) {
            addCriterion("singer_type between", value1, value2, "singerType");
            return (Criteria) this;
        }

        public Criteria andSingerTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("singer_type not between", value1, value2, "singerType");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerIsNull() {
            addCriterion("original_singer is null");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerIsNotNull() {
            addCriterion("original_singer is not null");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerEqualTo(Boolean value) {
            addCriterion("original_singer =", value, "originalSinger");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerNotEqualTo(Boolean value) {
            addCriterion("original_singer <>", value, "originalSinger");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerGreaterThan(Boolean value) {
            addCriterion("original_singer >", value, "originalSinger");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerGreaterThanOrEqualTo(Boolean value) {
            addCriterion("original_singer >=", value, "originalSinger");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerLessThan(Boolean value) {
            addCriterion("original_singer <", value, "originalSinger");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerLessThanOrEqualTo(Boolean value) {
            addCriterion("original_singer <=", value, "originalSinger");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerIn(List<Boolean> values) {
            addCriterion("original_singer in", values, "originalSinger");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerNotIn(List<Boolean> values) {
            addCriterion("original_singer not in", values, "originalSinger");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerBetween(Boolean value1, Boolean value2) {
            addCriterion("original_singer between", value1, value2, "originalSinger");
            return (Criteria) this;
        }

        public Criteria andOriginalSingerNotBetween(Boolean value1, Boolean value2) {
            addCriterion("original_singer not between", value1, value2, "originalSinger");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNull() {
            addCriterion("family_id is null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNotNull() {
            addCriterion("family_id is not null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdEqualTo(Long value) {
            addCriterion("family_id =", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotEqualTo(Long value) {
            addCriterion("family_id <>", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThan(Long value) {
            addCriterion("family_id >", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("family_id >=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThan(Long value) {
            addCriterion("family_id <", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("family_id <=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIn(List<Long> values) {
            addCriterion("family_id in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotIn(List<Long> values) {
            addCriterion("family_id not in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdBetween(Long value1, Long value2) {
            addCriterion("family_id between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("family_id not between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andLastDayIncomeIsNull() {
            addCriterion("last_day_income is null");
            return (Criteria) this;
        }

        public Criteria andLastDayIncomeIsNotNull() {
            addCriterion("last_day_income is not null");
            return (Criteria) this;
        }

        public Criteria andLastDayIncomeEqualTo(Long value) {
            addCriterion("last_day_income =", value, "lastDayIncome");
            return (Criteria) this;
        }

        public Criteria andLastDayIncomeNotEqualTo(Long value) {
            addCriterion("last_day_income <>", value, "lastDayIncome");
            return (Criteria) this;
        }

        public Criteria andLastDayIncomeGreaterThan(Long value) {
            addCriterion("last_day_income >", value, "lastDayIncome");
            return (Criteria) this;
        }

        public Criteria andLastDayIncomeGreaterThanOrEqualTo(Long value) {
            addCriterion("last_day_income >=", value, "lastDayIncome");
            return (Criteria) this;
        }

        public Criteria andLastDayIncomeLessThan(Long value) {
            addCriterion("last_day_income <", value, "lastDayIncome");
            return (Criteria) this;
        }

        public Criteria andLastDayIncomeLessThanOrEqualTo(Long value) {
            addCriterion("last_day_income <=", value, "lastDayIncome");
            return (Criteria) this;
        }

        public Criteria andLastDayIncomeIn(List<Long> values) {
            addCriterion("last_day_income in", values, "lastDayIncome");
            return (Criteria) this;
        }

        public Criteria andLastDayIncomeNotIn(List<Long> values) {
            addCriterion("last_day_income not in", values, "lastDayIncome");
            return (Criteria) this;
        }

        public Criteria andLastDayIncomeBetween(Long value1, Long value2) {
            addCriterion("last_day_income between", value1, value2, "lastDayIncome");
            return (Criteria) this;
        }

        public Criteria andLastDayIncomeNotBetween(Long value1, Long value2) {
            addCriterion("last_day_income not between", value1, value2, "lastDayIncome");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncomeIsNull() {
            addCriterion("last_week_income is null");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncomeIsNotNull() {
            addCriterion("last_week_income is not null");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncomeEqualTo(Long value) {
            addCriterion("last_week_income =", value, "lastWeekIncome");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncomeNotEqualTo(Long value) {
            addCriterion("last_week_income <>", value, "lastWeekIncome");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncomeGreaterThan(Long value) {
            addCriterion("last_week_income >", value, "lastWeekIncome");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncomeGreaterThanOrEqualTo(Long value) {
            addCriterion("last_week_income >=", value, "lastWeekIncome");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncomeLessThan(Long value) {
            addCriterion("last_week_income <", value, "lastWeekIncome");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncomeLessThanOrEqualTo(Long value) {
            addCriterion("last_week_income <=", value, "lastWeekIncome");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncomeIn(List<Long> values) {
            addCriterion("last_week_income in", values, "lastWeekIncome");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncomeNotIn(List<Long> values) {
            addCriterion("last_week_income not in", values, "lastWeekIncome");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncomeBetween(Long value1, Long value2) {
            addCriterion("last_week_income between", value1, value2, "lastWeekIncome");
            return (Criteria) this;
        }

        public Criteria andLastWeekIncomeNotBetween(Long value1, Long value2) {
            addCriterion("last_week_income not between", value1, value2, "lastWeekIncome");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncomeIsNull() {
            addCriterion("last_month_income is null");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncomeIsNotNull() {
            addCriterion("last_month_income is not null");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncomeEqualTo(Long value) {
            addCriterion("last_month_income =", value, "lastMonthIncome");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncomeNotEqualTo(Long value) {
            addCriterion("last_month_income <>", value, "lastMonthIncome");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncomeGreaterThan(Long value) {
            addCriterion("last_month_income >", value, "lastMonthIncome");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncomeGreaterThanOrEqualTo(Long value) {
            addCriterion("last_month_income >=", value, "lastMonthIncome");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncomeLessThan(Long value) {
            addCriterion("last_month_income <", value, "lastMonthIncome");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncomeLessThanOrEqualTo(Long value) {
            addCriterion("last_month_income <=", value, "lastMonthIncome");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncomeIn(List<Long> values) {
            addCriterion("last_month_income in", values, "lastMonthIncome");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncomeNotIn(List<Long> values) {
            addCriterion("last_month_income not in", values, "lastMonthIncome");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncomeBetween(Long value1, Long value2) {
            addCriterion("last_month_income between", value1, value2, "lastMonthIncome");
            return (Criteria) this;
        }

        public Criteria andLastMonthIncomeNotBetween(Long value1, Long value2) {
            addCriterion("last_month_income not between", value1, value2, "lastMonthIncome");
            return (Criteria) this;
        }

        public Criteria andLastMonthUpMicCntIsNull() {
            addCriterion("last_month_up_mic_cnt is null");
            return (Criteria) this;
        }

        public Criteria andLastMonthUpMicCntIsNotNull() {
            addCriterion("last_month_up_mic_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andLastMonthUpMicCntEqualTo(Long value) {
            addCriterion("last_month_up_mic_cnt =", value, "lastMonthUpMicCnt");
            return (Criteria) this;
        }

        public Criteria andLastMonthUpMicCntNotEqualTo(Long value) {
            addCriterion("last_month_up_mic_cnt <>", value, "lastMonthUpMicCnt");
            return (Criteria) this;
        }

        public Criteria andLastMonthUpMicCntGreaterThan(Long value) {
            addCriterion("last_month_up_mic_cnt >", value, "lastMonthUpMicCnt");
            return (Criteria) this;
        }

        public Criteria andLastMonthUpMicCntGreaterThanOrEqualTo(Long value) {
            addCriterion("last_month_up_mic_cnt >=", value, "lastMonthUpMicCnt");
            return (Criteria) this;
        }

        public Criteria andLastMonthUpMicCntLessThan(Long value) {
            addCriterion("last_month_up_mic_cnt <", value, "lastMonthUpMicCnt");
            return (Criteria) this;
        }

        public Criteria andLastMonthUpMicCntLessThanOrEqualTo(Long value) {
            addCriterion("last_month_up_mic_cnt <=", value, "lastMonthUpMicCnt");
            return (Criteria) this;
        }

        public Criteria andLastMonthUpMicCntIn(List<Long> values) {
            addCriterion("last_month_up_mic_cnt in", values, "lastMonthUpMicCnt");
            return (Criteria) this;
        }

        public Criteria andLastMonthUpMicCntNotIn(List<Long> values) {
            addCriterion("last_month_up_mic_cnt not in", values, "lastMonthUpMicCnt");
            return (Criteria) this;
        }

        public Criteria andLastMonthUpMicCntBetween(Long value1, Long value2) {
            addCriterion("last_month_up_mic_cnt between", value1, value2, "lastMonthUpMicCnt");
            return (Criteria) this;
        }

        public Criteria andLastMonthUpMicCntNotBetween(Long value1, Long value2) {
            addCriterion("last_month_up_mic_cnt not between", value1, value2, "lastMonthUpMicCnt");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andLastXDayIncomeIsNull() {
            addCriterion("last_x_day_income is null");
            return (Criteria) this;
        }

        public Criteria andLastXDayIncomeIsNotNull() {
            addCriterion("last_x_day_income is not null");
            return (Criteria) this;
        }

        public Criteria andLastXDayIncomeEqualTo(Long value) {
            addCriterion("last_x_day_income =", value, "lastXDayIncome");
            return (Criteria) this;
        }

        public Criteria andLastXDayIncomeNotEqualTo(Long value) {
            addCriterion("last_x_day_income <>", value, "lastXDayIncome");
            return (Criteria) this;
        }

        public Criteria andLastXDayIncomeGreaterThan(Long value) {
            addCriterion("last_x_day_income >", value, "lastXDayIncome");
            return (Criteria) this;
        }

        public Criteria andLastXDayIncomeGreaterThanOrEqualTo(Long value) {
            addCriterion("last_x_day_income >=", value, "lastXDayIncome");
            return (Criteria) this;
        }

        public Criteria andLastXDayIncomeLessThan(Long value) {
            addCriterion("last_x_day_income <", value, "lastXDayIncome");
            return (Criteria) this;
        }

        public Criteria andLastXDayIncomeLessThanOrEqualTo(Long value) {
            addCriterion("last_x_day_income <=", value, "lastXDayIncome");
            return (Criteria) this;
        }

        public Criteria andLastXDayIncomeIn(List<Long> values) {
            addCriterion("last_x_day_income in", values, "lastXDayIncome");
            return (Criteria) this;
        }

        public Criteria andLastXDayIncomeNotIn(List<Long> values) {
            addCriterion("last_x_day_income not in", values, "lastXDayIncome");
            return (Criteria) this;
        }

        public Criteria andLastXDayIncomeBetween(Long value1, Long value2) {
            addCriterion("last_x_day_income between", value1, value2, "lastXDayIncome");
            return (Criteria) this;
        }

        public Criteria andLastXDayIncomeNotBetween(Long value1, Long value2) {
            addCriterion("last_x_day_income not between", value1, value2, "lastXDayIncome");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table singer_data_day
     *
     * @mbg.generated do_not_delete_during_merge Wed Jul 30 17:11:10 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table singer_data_day
     *
     * @mbg.generated Wed Jul 30 17:11:10 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}