package fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 歌手审核配置表
 *
 * @date 2025-04-24 03:40:43
 */
@Table(name = "`singer_audit_config`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SingerAuditConfig {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 配置的code
     */
    @Column(name= "`config_code`")
    private String configCode;

    /**
     * 0：禁用，1：启用，默认禁用
     */
    @Column(name= "`enabled`")
    private Boolean enabled;

    /**
     * 是否可编辑，0：不可，1：可以
     */
    @Column(name= "`editable`")
    private Boolean editable;

    /**
     * 配置说明
     */
    @Column(name= "`explanation`")
    private String explanation;

    /**
     * 审核场景，1：前端审核，2：后端审核
     */
    @Column(name= "`audit_scene`")
    private Integer auditScene;

    /**
     * 环境，TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    @Column(name= "`operator`")
    private String operator;

    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", configCode=").append(configCode);
        sb.append(", enabled=").append(enabled);
        sb.append(", editable=").append(editable);
        sb.append(", explanation=").append(explanation);
        sb.append(", auditScene=").append(auditScene);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", createTime=").append(createTime);
        sb.append(", operator=").append(operator);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}