package fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 歌手汇总-天
 *
 * @date 2025-07-30 05:11:10
 */
@Table(name = "`singer_data_day`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SingerDataDay {
    /**
     * ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 业务
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 日期 格式 YYYY-MM-DD
     */
    @Column(name= "`stat_date`")
    private Date statDate;

    /**
     * 日期 格式 YYYYMMDD
     */
    @Column(name= "`stat_date_value`")
    private Integer statDateValue;

    /**
     * 歌手ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 厅主ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 歌手类型
     */
    @Column(name= "`singer_type`")
    private Integer singerType;

    /**
     * 是否原创歌手
     */
    @Column(name= "`original_singer`")
    private Boolean originalSinger;

    /**
     * 家族ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 近一日营收（钻石）
     */
    @Column(name= "`last_day_income`")
    private Long lastDayIncome;

    /**
     * 近一周营收（钻石）
     */
    @Column(name= "`last_week_income`")
    private Long lastWeekIncome;

    /**
     * 近一个月营收（钻石）
     */
    @Column(name= "`last_month_income`")
    private Long lastMonthIncome;

    /**
     * 近一个月上麦数
     */
    @Column(name= "`last_month_up_mic_cnt`")
    private Long lastMonthUpMicCnt;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 近X日营收（钻石）
     */
    @Column(name= "`last_x_day_income`")
    private Long lastXDayIncome;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", statDate=").append(statDate);
        sb.append(", statDateValue=").append(statDateValue);
        sb.append(", userId=").append(userId);
        sb.append(", njId=").append(njId);
        sb.append(", singerType=").append(singerType);
        sb.append(", originalSinger=").append(originalSinger);
        sb.append(", familyId=").append(familyId);
        sb.append(", lastDayIncome=").append(lastDayIncome);
        sb.append(", lastWeekIncome=").append(lastWeekIncome);
        sb.append(", lastMonthIncome=").append(lastMonthIncome);
        sb.append(", lastMonthUpMicCnt=").append(lastMonthUpMicCnt);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", lastXDayIncome=").append(lastXDayIncome);
        sb.append("]");
        return sb.toString();
    }
}