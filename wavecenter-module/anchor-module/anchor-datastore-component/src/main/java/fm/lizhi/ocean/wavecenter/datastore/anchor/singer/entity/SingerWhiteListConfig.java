package fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 *
 * @date 2025-07-18 11:45:25
 */
@Table(name = "`singer_white_list_config`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SingerWhiteListConfig {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 歌手ID
     */
    @Column(name= "`singer_id`")
    private Long singerId;

    /**
     * 歌手等级
     */
    @Column(name= "`singer_type`")
    private Integer singerType;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 环境字段
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", singerId=").append(singerId);
        sb.append(", singerType=").append(singerType);
        sb.append(", appId=").append(appId);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }
}