package fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SingerVerifyApplySongInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_verify_apply_song_info
     *
     * @mbg.generated Wed Jul 23 11:21:54 CST 2025
     */
    public SingerVerifyApplySongInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_verify_apply_song_info
     *
     * @mbg.generated Wed Jul 23 11:21:54 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_verify_apply_song_info
     *
     * @mbg.generated Wed Jul 23 11:21:54 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_verify_apply_song_info
     *
     * @mbg.generated Wed Jul 23 11:21:54 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_verify_apply_song_info
     *
     * @mbg.generated Wed Jul 23 11:21:54 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_verify_apply_song_info
     *
     * @mbg.generated Wed Jul 23 11:21:54 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_verify_apply_song_info
     *
     * @mbg.generated Wed Jul 23 11:21:54 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_verify_apply_song_info
     *
     * @mbg.generated Wed Jul 23 11:21:54 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_verify_apply_song_info
     *
     * @mbg.generated Wed Jul 23 11:21:54 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_verify_apply_song_info
     *
     * @mbg.generated Wed Jul 23 11:21:54 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table singer_verify_apply_song_info
     *
     * @mbg.generated Wed Jul 23 11:21:54 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity.SingerVerifyApplySongInfo.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table singer_verify_apply_song_info
     *
     * @mbg.generated Wed Jul 23 11:21:54 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andApplyIdIsNull() {
            addCriterion("apply_id is null");
            return (Criteria) this;
        }

        public Criteria andApplyIdIsNotNull() {
            addCriterion("apply_id is not null");
            return (Criteria) this;
        }

        public Criteria andApplyIdEqualTo(Long value) {
            addCriterion("apply_id =", value, "applyId");
            return (Criteria) this;
        }

        public Criteria andApplyIdNotEqualTo(Long value) {
            addCriterion("apply_id <>", value, "applyId");
            return (Criteria) this;
        }

        public Criteria andApplyIdGreaterThan(Long value) {
            addCriterion("apply_id >", value, "applyId");
            return (Criteria) this;
        }

        public Criteria andApplyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("apply_id >=", value, "applyId");
            return (Criteria) this;
        }

        public Criteria andApplyIdLessThan(Long value) {
            addCriterion("apply_id <", value, "applyId");
            return (Criteria) this;
        }

        public Criteria andApplyIdLessThanOrEqualTo(Long value) {
            addCriterion("apply_id <=", value, "applyId");
            return (Criteria) this;
        }

        public Criteria andApplyIdIn(List<Long> values) {
            addCriterion("apply_id in", values, "applyId");
            return (Criteria) this;
        }

        public Criteria andApplyIdNotIn(List<Long> values) {
            addCriterion("apply_id not in", values, "applyId");
            return (Criteria) this;
        }

        public Criteria andApplyIdBetween(Long value1, Long value2) {
            addCriterion("apply_id between", value1, value2, "applyId");
            return (Criteria) this;
        }

        public Criteria andApplyIdNotBetween(Long value1, Long value2) {
            addCriterion("apply_id not between", value1, value2, "applyId");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andSongStyleIsNull() {
            addCriterion("song_style is null");
            return (Criteria) this;
        }

        public Criteria andSongStyleIsNotNull() {
            addCriterion("song_style is not null");
            return (Criteria) this;
        }

        public Criteria andSongStyleEqualTo(String value) {
            addCriterion("song_style =", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleNotEqualTo(String value) {
            addCriterion("song_style <>", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleGreaterThan(String value) {
            addCriterion("song_style >", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleGreaterThanOrEqualTo(String value) {
            addCriterion("song_style >=", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleLessThan(String value) {
            addCriterion("song_style <", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleLessThanOrEqualTo(String value) {
            addCriterion("song_style <=", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleLike(String value) {
            addCriterion("song_style like", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleNotLike(String value) {
            addCriterion("song_style not like", value, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleIn(List<String> values) {
            addCriterion("song_style in", values, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleNotIn(List<String> values) {
            addCriterion("song_style not in", values, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleBetween(String value1, String value2) {
            addCriterion("song_style between", value1, value2, "songStyle");
            return (Criteria) this;
        }

        public Criteria andSongStyleNotBetween(String value1, String value2) {
            addCriterion("song_style not between", value1, value2, "songStyle");
            return (Criteria) this;
        }

        public Criteria andPreAuditStatusIsNull() {
            addCriterion("pre_audit_status is null");
            return (Criteria) this;
        }

        public Criteria andPreAuditStatusIsNotNull() {
            addCriterion("pre_audit_status is not null");
            return (Criteria) this;
        }

        public Criteria andPreAuditStatusEqualTo(Integer value) {
            addCriterion("pre_audit_status =", value, "preAuditStatus");
            return (Criteria) this;
        }

        public Criteria andPreAuditStatusNotEqualTo(Integer value) {
            addCriterion("pre_audit_status <>", value, "preAuditStatus");
            return (Criteria) this;
        }

        public Criteria andPreAuditStatusGreaterThan(Integer value) {
            addCriterion("pre_audit_status >", value, "preAuditStatus");
            return (Criteria) this;
        }

        public Criteria andPreAuditStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("pre_audit_status >=", value, "preAuditStatus");
            return (Criteria) this;
        }

        public Criteria andPreAuditStatusLessThan(Integer value) {
            addCriterion("pre_audit_status <", value, "preAuditStatus");
            return (Criteria) this;
        }

        public Criteria andPreAuditStatusLessThanOrEqualTo(Integer value) {
            addCriterion("pre_audit_status <=", value, "preAuditStatus");
            return (Criteria) this;
        }

        public Criteria andPreAuditStatusIn(List<Integer> values) {
            addCriterion("pre_audit_status in", values, "preAuditStatus");
            return (Criteria) this;
        }

        public Criteria andPreAuditStatusNotIn(List<Integer> values) {
            addCriterion("pre_audit_status not in", values, "preAuditStatus");
            return (Criteria) this;
        }

        public Criteria andPreAuditStatusBetween(Integer value1, Integer value2) {
            addCriterion("pre_audit_status between", value1, value2, "preAuditStatus");
            return (Criteria) this;
        }

        public Criteria andPreAuditStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("pre_audit_status not between", value1, value2, "preAuditStatus");
            return (Criteria) this;
        }

        public Criteria andAudioPathIsNull() {
            addCriterion("audio_path is null");
            return (Criteria) this;
        }

        public Criteria andAudioPathIsNotNull() {
            addCriterion("audio_path is not null");
            return (Criteria) this;
        }

        public Criteria andAudioPathEqualTo(String value) {
            addCriterion("audio_path =", value, "audioPath");
            return (Criteria) this;
        }

        public Criteria andAudioPathNotEqualTo(String value) {
            addCriterion("audio_path <>", value, "audioPath");
            return (Criteria) this;
        }

        public Criteria andAudioPathGreaterThan(String value) {
            addCriterion("audio_path >", value, "audioPath");
            return (Criteria) this;
        }

        public Criteria andAudioPathGreaterThanOrEqualTo(String value) {
            addCriterion("audio_path >=", value, "audioPath");
            return (Criteria) this;
        }

        public Criteria andAudioPathLessThan(String value) {
            addCriterion("audio_path <", value, "audioPath");
            return (Criteria) this;
        }

        public Criteria andAudioPathLessThanOrEqualTo(String value) {
            addCriterion("audio_path <=", value, "audioPath");
            return (Criteria) this;
        }

        public Criteria andAudioPathLike(String value) {
            addCriterion("audio_path like", value, "audioPath");
            return (Criteria) this;
        }

        public Criteria andAudioPathNotLike(String value) {
            addCriterion("audio_path not like", value, "audioPath");
            return (Criteria) this;
        }

        public Criteria andAudioPathIn(List<String> values) {
            addCriterion("audio_path in", values, "audioPath");
            return (Criteria) this;
        }

        public Criteria andAudioPathNotIn(List<String> values) {
            addCriterion("audio_path not in", values, "audioPath");
            return (Criteria) this;
        }

        public Criteria andAudioPathBetween(String value1, String value2) {
            addCriterion("audio_path between", value1, value2, "audioPath");
            return (Criteria) this;
        }

        public Criteria andAudioPathNotBetween(String value1, String value2) {
            addCriterion("audio_path not between", value1, value2, "audioPath");
            return (Criteria) this;
        }

        public Criteria andSongNameIsNull() {
            addCriterion("song_name is null");
            return (Criteria) this;
        }

        public Criteria andSongNameIsNotNull() {
            addCriterion("song_name is not null");
            return (Criteria) this;
        }

        public Criteria andSongNameEqualTo(String value) {
            addCriterion("song_name =", value, "songName");
            return (Criteria) this;
        }

        public Criteria andSongNameNotEqualTo(String value) {
            addCriterion("song_name <>", value, "songName");
            return (Criteria) this;
        }

        public Criteria andSongNameGreaterThan(String value) {
            addCriterion("song_name >", value, "songName");
            return (Criteria) this;
        }

        public Criteria andSongNameGreaterThanOrEqualTo(String value) {
            addCriterion("song_name >=", value, "songName");
            return (Criteria) this;
        }

        public Criteria andSongNameLessThan(String value) {
            addCriterion("song_name <", value, "songName");
            return (Criteria) this;
        }

        public Criteria andSongNameLessThanOrEqualTo(String value) {
            addCriterion("song_name <=", value, "songName");
            return (Criteria) this;
        }

        public Criteria andSongNameLike(String value) {
            addCriterion("song_name like", value, "songName");
            return (Criteria) this;
        }

        public Criteria andSongNameNotLike(String value) {
            addCriterion("song_name not like", value, "songName");
            return (Criteria) this;
        }

        public Criteria andSongNameIn(List<String> values) {
            addCriterion("song_name in", values, "songName");
            return (Criteria) this;
        }

        public Criteria andSongNameNotIn(List<String> values) {
            addCriterion("song_name not in", values, "songName");
            return (Criteria) this;
        }

        public Criteria andSongNameBetween(String value1, String value2) {
            addCriterion("song_name between", value1, value2, "songName");
            return (Criteria) this;
        }

        public Criteria andSongNameNotBetween(String value1, String value2) {
            addCriterion("song_name not between", value1, value2, "songName");
            return (Criteria) this;
        }

        public Criteria andPreAuditRejectReasonIsNull() {
            addCriterion("pre_audit_reject_reason is null");
            return (Criteria) this;
        }

        public Criteria andPreAuditRejectReasonIsNotNull() {
            addCriterion("pre_audit_reject_reason is not null");
            return (Criteria) this;
        }

        public Criteria andPreAuditRejectReasonEqualTo(String value) {
            addCriterion("pre_audit_reject_reason =", value, "preAuditRejectReason");
            return (Criteria) this;
        }

        public Criteria andPreAuditRejectReasonNotEqualTo(String value) {
            addCriterion("pre_audit_reject_reason <>", value, "preAuditRejectReason");
            return (Criteria) this;
        }

        public Criteria andPreAuditRejectReasonGreaterThan(String value) {
            addCriterion("pre_audit_reject_reason >", value, "preAuditRejectReason");
            return (Criteria) this;
        }

        public Criteria andPreAuditRejectReasonGreaterThanOrEqualTo(String value) {
            addCriterion("pre_audit_reject_reason >=", value, "preAuditRejectReason");
            return (Criteria) this;
        }

        public Criteria andPreAuditRejectReasonLessThan(String value) {
            addCriterion("pre_audit_reject_reason <", value, "preAuditRejectReason");
            return (Criteria) this;
        }

        public Criteria andPreAuditRejectReasonLessThanOrEqualTo(String value) {
            addCriterion("pre_audit_reject_reason <=", value, "preAuditRejectReason");
            return (Criteria) this;
        }

        public Criteria andPreAuditRejectReasonLike(String value) {
            addCriterion("pre_audit_reject_reason like", value, "preAuditRejectReason");
            return (Criteria) this;
        }

        public Criteria andPreAuditRejectReasonNotLike(String value) {
            addCriterion("pre_audit_reject_reason not like", value, "preAuditRejectReason");
            return (Criteria) this;
        }

        public Criteria andPreAuditRejectReasonIn(List<String> values) {
            addCriterion("pre_audit_reject_reason in", values, "preAuditRejectReason");
            return (Criteria) this;
        }

        public Criteria andPreAuditRejectReasonNotIn(List<String> values) {
            addCriterion("pre_audit_reject_reason not in", values, "preAuditRejectReason");
            return (Criteria) this;
        }

        public Criteria andPreAuditRejectReasonBetween(String value1, String value2) {
            addCriterion("pre_audit_reject_reason between", value1, value2, "preAuditRejectReason");
            return (Criteria) this;
        }

        public Criteria andPreAuditRejectReasonNotBetween(String value1, String value2) {
            addCriterion("pre_audit_reject_reason not between", value1, value2, "preAuditRejectReason");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table singer_verify_apply_song_info
     *
     * @mbg.generated do_not_delete_during_merge Wed Jul 23 11:21:54 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table singer_verify_apply_song_info
     *
     * @mbg.generated Wed Jul 23 11:21:54 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}