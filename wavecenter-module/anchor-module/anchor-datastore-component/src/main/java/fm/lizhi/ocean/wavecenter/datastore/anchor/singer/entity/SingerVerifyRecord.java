package fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 歌手认证表
 *
 * @date 2025-07-29 04:34:02
 */
@Table(name = "`singer_verify_record`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SingerVerifyRecord {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 歌手ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 签约厅主ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 家族ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * appId
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 审核状态，1：待审核，2：待定，3：选中，4：审核通过，5： 审核不通过
     */
    @Column(name= "`audit_status`")
    private Integer auditStatus;

    /**
     * 是否原创歌手
     */
    @Column(name= "`original_singer`")
    private Boolean originalSinger;

    /**
     * 歌曲名称
     */
    @Column(name= "`song_name`")
    private String songName;

    /**
     * 原创作品链接，是原创歌手才有
     */
    @Column(name= "`original_song_url`")
    private String originalSongUrl;

    /**
     * 社交媒体认证图，是原创歌手才有，最多三张，多个逗号分隔
     */
    @Column(name= "`social_verify_image`")
    private String socialVerifyImage;

    /**
     * 歌曲风格
     */
    @Column(name= "`song_style`")
    private String songStyle;

    /**
     * 1: 新锐歌手，2：优质歌手，3：明星歌手
     */
    @Column(name= "`singer_type`")
    private Integer singerType;

    /**
     * 审核时间
     */
    @Column(name= "`audit_time`")
    private Date auditTime;

    /**
     * 音频文件地址
     */
    @Column(name= "`audio_path`")
    private String audioPath;

    /**
     * 环境：TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 实名认证证件号，不同证件类型号码长度不一样，不会冲突
     */
    @Column(name= "`id_card_number`")
    private String idCardNumber;

    /**
     * 预审核不过原因
     */
    @Column(name= "`pre_audit_reject_reason`")
    private String preAuditRejectReason;

    /**
     * 不通过原因
     */
    @Column(name= "`reject_reason`")
    private String rejectReason;

    /**
     * 备注
     */
    @Column(name= "`remark`")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 联系方式
     */
    @Column(name= "`contact_number`")
    private String contactNumber;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", njId=").append(njId);
        sb.append(", familyId=").append(familyId);
        sb.append(", appId=").append(appId);
        sb.append(", auditStatus=").append(auditStatus);
        sb.append(", originalSinger=").append(originalSinger);
        sb.append(", songName=").append(songName);
        sb.append(", originalSongUrl=").append(originalSongUrl);
        sb.append(", socialVerifyImage=").append(socialVerifyImage);
        sb.append(", songStyle=").append(songStyle);
        sb.append(", singerType=").append(singerType);
        sb.append(", auditTime=").append(auditTime);
        sb.append(", audioPath=").append(audioPath);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", operator=").append(operator);
        sb.append(", idCardNumber=").append(idCardNumber);
        sb.append(", preAuditRejectReason=").append(preAuditRejectReason);
        sb.append(", rejectReason=").append(rejectReason);
        sb.append(", remark=").append(remark);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", contactNumber=").append(contactNumber);
        sb.append("]");
        return sb.toString();
    }
}