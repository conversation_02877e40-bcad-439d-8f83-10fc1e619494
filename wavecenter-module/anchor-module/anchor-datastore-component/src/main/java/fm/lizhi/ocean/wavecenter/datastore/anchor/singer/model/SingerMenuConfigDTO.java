package fm.lizhi.ocean.wavecenter.datastore.anchor.singer.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SingerMenuConfigDTO {


    /**
     * 应用ID
     */
    private int appId;

    /**
     * 是否开启
     */
    private Boolean enabled;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 歌手类型
     */
    private Integer singerType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 菜单可见性规则配置
     */
    private List<MenuVisibleRuleConfigDTO> bizRuleConfigs = Collections.emptyList();

}