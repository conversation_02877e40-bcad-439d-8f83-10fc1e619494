package fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 认证申请-歌曲信息表
 *
 * @date 2025-07-23 11:21:54
 */
@Table(name = "`singer_verify_apply_song_info`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SingerVerifyApplySongInfo {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 申请ID
     */
    @Column(name= "`apply_id`")
    private Long applyId;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 用户ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 曲风
     */
    @Column(name= "`song_style`")
    private String songStyle;

    /**
     * 预审核状态，1：不通过，2：通过
     */
    @Column(name= "`pre_audit_status`")
    private Integer preAuditStatus;

    /**
     * 音频地址
     */
    @Column(name= "`audio_path`")
    private String audioPath;

    /**
     * 音频地址
     */
    @Column(name= "`song_name`")
    private String songName;

    /**
     * 音频地址
     */
    @Column(name= "`pre_audit_reject_reason`")
    private String preAuditRejectReason;

    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Column(name= "`create_time`")
    private Date createTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", applyId=").append(applyId);
        sb.append(", appId=").append(appId);
        sb.append(", userId=").append(userId);
        sb.append(", songStyle=").append(songStyle);
        sb.append(", preAuditStatus=").append(preAuditStatus);
        sb.append(", audioPath=").append(audioPath);
        sb.append(", songName=").append(songName);
        sb.append(", preAuditRejectReason=").append(preAuditRejectReason);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }
}