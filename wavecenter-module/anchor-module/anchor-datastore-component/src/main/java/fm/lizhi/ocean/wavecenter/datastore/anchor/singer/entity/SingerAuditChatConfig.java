package fm.lizhi.ocean.wavecenter.datastore.anchor.singer.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 歌手审核私信配置表
 *
 * @date 2025-04-25 11:00:24
 */
@Table(name = "`singer_audit_chat_config`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SingerAuditChatConfig {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 配置标题
     */
    @Column(name= "`title`")
    private String title;

    /**
     * 条件描述
     */
    @Column(name= "`condition_remark`")
    private String conditionRemark;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 歌手类型
     */
    @Column(name= "`singer_type`")
    private Integer singerType;

    /**
     * 场景码，有枚举
     */
    @Column(name= "`scene_code`")
    private String sceneCode;

    /**
     * 私信文案
     */
    @Column(name= "`content`")
    private String content;

    /**
     * 跳转链接
     */
    @Column(name= "`action_url`")
    private String actionUrl;

    /**
     * 环境
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", title=").append(title);
        sb.append(", conditionRemark=").append(conditionRemark);
        sb.append(", appId=").append(appId);
        sb.append(", singerType=").append(singerType);
        sb.append(", sceneCode=").append(sceneCode);
        sb.append(", content=").append(content);
        sb.append(", actionUrl=").append(actionUrl);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", operator=").append(operator);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }
}