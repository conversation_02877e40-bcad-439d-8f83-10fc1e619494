package fm.lizhi.ocean.wavecenter.datastore.background.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动模板流量资源图片表
 *
 * @date 2024-10-17 05:35:35
 */
@Table(name = "`activity_template_flow_resource_image`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityTemplateFlowResourceImage {
    /**
     * 活动模板流量资源图片ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 流量资源ID，对应activity_template_flow_resource.id
     */
    @Column(name= "`flow_resource_id`")
    private Long flowResourceId;

    /**
     * 图片地址，平台域名，相对路径，斜杆开头
     */
    @Column(name= "`image_url`")
    private String imageUrl;

    /**
     * 图片扩展配置, JSON结构，对应activity_image_fodder表的图片附加字段
     */
    @Column(name= "`extra`")
    private String extra;

    /**
     * 排序
     */
    @Column(name= "`index`")
    private Integer index;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", flowResourceId=").append(flowResourceId);
        sb.append(", imageUrl=").append(imageUrl);
        sb.append(", extra=").append(extra);
        sb.append(", index=").append(index);
        sb.append("]");
        return sb.toString();
    }
}