package fm.lizhi.ocean.wavecenter.datastore.background.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ActivityTemplateInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_info
     *
     * @mbg.generated Fri Sep 05 16:39:06 CST 2025
     */
    public ActivityTemplateInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_info
     *
     * @mbg.generated Fri Sep 05 16:39:06 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_info
     *
     * @mbg.generated Fri Sep 05 16:39:06 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_info
     *
     * @mbg.generated Fri Sep 05 16:39:06 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_info
     *
     * @mbg.generated Fri Sep 05 16:39:06 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_info
     *
     * @mbg.generated Fri Sep 05 16:39:06 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_info
     *
     * @mbg.generated Fri Sep 05 16:39:06 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_info
     *
     * @mbg.generated Fri Sep 05 16:39:06 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_info
     *
     * @mbg.generated Fri Sep 05 16:39:06 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_info
     *
     * @mbg.generated Fri Sep 05 16:39:06 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_info
     *
     * @mbg.generated Fri Sep 05 16:39:06 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return fm.lizhi.ocean.wavecenter.datastore.background.entity.ActivityTemplateInfo.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table activity_template_info
     *
     * @mbg.generated Fri Sep 05 16:39:06 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andClassIdIsNull() {
            addCriterion("class_id is null");
            return (Criteria) this;
        }

        public Criteria andClassIdIsNotNull() {
            addCriterion("class_id is not null");
            return (Criteria) this;
        }

        public Criteria andClassIdEqualTo(Long value) {
            addCriterion("class_id =", value, "classId");
            return (Criteria) this;
        }

        public Criteria andClassIdNotEqualTo(Long value) {
            addCriterion("class_id <>", value, "classId");
            return (Criteria) this;
        }

        public Criteria andClassIdGreaterThan(Long value) {
            addCriterion("class_id >", value, "classId");
            return (Criteria) this;
        }

        public Criteria andClassIdGreaterThanOrEqualTo(Long value) {
            addCriterion("class_id >=", value, "classId");
            return (Criteria) this;
        }

        public Criteria andClassIdLessThan(Long value) {
            addCriterion("class_id <", value, "classId");
            return (Criteria) this;
        }

        public Criteria andClassIdLessThanOrEqualTo(Long value) {
            addCriterion("class_id <=", value, "classId");
            return (Criteria) this;
        }

        public Criteria andClassIdIn(List<Long> values) {
            addCriterion("class_id in", values, "classId");
            return (Criteria) this;
        }

        public Criteria andClassIdNotIn(List<Long> values) {
            addCriterion("class_id not in", values, "classId");
            return (Criteria) this;
        }

        public Criteria andClassIdBetween(Long value1, Long value2) {
            addCriterion("class_id between", value1, value2, "classId");
            return (Criteria) this;
        }

        public Criteria andClassIdNotBetween(Long value1, Long value2) {
            addCriterion("class_id not between", value1, value2, "classId");
            return (Criteria) this;
        }

        public Criteria andGoalIsNull() {
            addCriterion("goal is null");
            return (Criteria) this;
        }

        public Criteria andGoalIsNotNull() {
            addCriterion("goal is not null");
            return (Criteria) this;
        }

        public Criteria andGoalEqualTo(String value) {
            addCriterion("goal =", value, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalNotEqualTo(String value) {
            addCriterion("goal <>", value, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalGreaterThan(String value) {
            addCriterion("goal >", value, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalGreaterThanOrEqualTo(String value) {
            addCriterion("goal >=", value, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalLessThan(String value) {
            addCriterion("goal <", value, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalLessThanOrEqualTo(String value) {
            addCriterion("goal <=", value, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalLike(String value) {
            addCriterion("goal like", value, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalNotLike(String value) {
            addCriterion("goal not like", value, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalIn(List<String> values) {
            addCriterion("goal in", values, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalNotIn(List<String> values) {
            addCriterion("goal not in", values, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalBetween(String value1, String value2) {
            addCriterion("goal between", value1, value2, "goal");
            return (Criteria) this;
        }

        public Criteria andGoalNotBetween(String value1, String value2) {
            addCriterion("goal not between", value1, value2, "goal");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlIsNull() {
            addCriterion("auxiliary_prop_url is null");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlIsNotNull() {
            addCriterion("auxiliary_prop_url is not null");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlEqualTo(String value) {
            addCriterion("auxiliary_prop_url =", value, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlNotEqualTo(String value) {
            addCriterion("auxiliary_prop_url <>", value, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlGreaterThan(String value) {
            addCriterion("auxiliary_prop_url >", value, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlGreaterThanOrEqualTo(String value) {
            addCriterion("auxiliary_prop_url >=", value, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlLessThan(String value) {
            addCriterion("auxiliary_prop_url <", value, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlLessThanOrEqualTo(String value) {
            addCriterion("auxiliary_prop_url <=", value, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlLike(String value) {
            addCriterion("auxiliary_prop_url like", value, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlNotLike(String value) {
            addCriterion("auxiliary_prop_url not like", value, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlIn(List<String> values) {
            addCriterion("auxiliary_prop_url in", values, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlNotIn(List<String> values) {
            addCriterion("auxiliary_prop_url not in", values, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlBetween(String value1, String value2) {
            addCriterion("auxiliary_prop_url between", value1, value2, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andAuxiliaryPropUrlNotBetween(String value1, String value2) {
            addCriterion("auxiliary_prop_url not between", value1, value2, "auxiliaryPropUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlIsNull() {
            addCriterion("poster_url is null");
            return (Criteria) this;
        }

        public Criteria andPosterUrlIsNotNull() {
            addCriterion("poster_url is not null");
            return (Criteria) this;
        }

        public Criteria andPosterUrlEqualTo(String value) {
            addCriterion("poster_url =", value, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlNotEqualTo(String value) {
            addCriterion("poster_url <>", value, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlGreaterThan(String value) {
            addCriterion("poster_url >", value, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlGreaterThanOrEqualTo(String value) {
            addCriterion("poster_url >=", value, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlLessThan(String value) {
            addCriterion("poster_url <", value, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlLessThanOrEqualTo(String value) {
            addCriterion("poster_url <=", value, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlLike(String value) {
            addCriterion("poster_url like", value, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlNotLike(String value) {
            addCriterion("poster_url not like", value, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlIn(List<String> values) {
            addCriterion("poster_url in", values, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlNotIn(List<String> values) {
            addCriterion("poster_url not in", values, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlBetween(String value1, String value2) {
            addCriterion("poster_url between", value1, value2, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andPosterUrlNotBetween(String value1, String value2) {
            addCriterion("poster_url not between", value1, value2, "posterUrl");
            return (Criteria) this;
        }

        public Criteria andActivityToolIsNull() {
            addCriterion("activity_tool is null");
            return (Criteria) this;
        }

        public Criteria andActivityToolIsNotNull() {
            addCriterion("activity_tool is not null");
            return (Criteria) this;
        }

        public Criteria andActivityToolEqualTo(String value) {
            addCriterion("activity_tool =", value, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolNotEqualTo(String value) {
            addCriterion("activity_tool <>", value, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolGreaterThan(String value) {
            addCriterion("activity_tool >", value, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolGreaterThanOrEqualTo(String value) {
            addCriterion("activity_tool >=", value, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolLessThan(String value) {
            addCriterion("activity_tool <", value, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolLessThanOrEqualTo(String value) {
            addCriterion("activity_tool <=", value, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolLike(String value) {
            addCriterion("activity_tool like", value, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolNotLike(String value) {
            addCriterion("activity_tool not like", value, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolIn(List<String> values) {
            addCriterion("activity_tool in", values, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolNotIn(List<String> values) {
            addCriterion("activity_tool not in", values, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolBetween(String value1, String value2) {
            addCriterion("activity_tool between", value1, value2, "activityTool");
            return (Criteria) this;
        }

        public Criteria andActivityToolNotBetween(String value1, String value2) {
            addCriterion("activity_tool not between", value1, value2, "activityTool");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdsIsNull() {
            addCriterion("room_background_ids is null");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdsIsNotNull() {
            addCriterion("room_background_ids is not null");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdsEqualTo(String value) {
            addCriterion("room_background_ids =", value, "roomBackgroundIds");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdsNotEqualTo(String value) {
            addCriterion("room_background_ids <>", value, "roomBackgroundIds");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdsGreaterThan(String value) {
            addCriterion("room_background_ids >", value, "roomBackgroundIds");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdsGreaterThanOrEqualTo(String value) {
            addCriterion("room_background_ids >=", value, "roomBackgroundIds");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdsLessThan(String value) {
            addCriterion("room_background_ids <", value, "roomBackgroundIds");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdsLessThanOrEqualTo(String value) {
            addCriterion("room_background_ids <=", value, "roomBackgroundIds");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdsLike(String value) {
            addCriterion("room_background_ids like", value, "roomBackgroundIds");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdsNotLike(String value) {
            addCriterion("room_background_ids not like", value, "roomBackgroundIds");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdsIn(List<String> values) {
            addCriterion("room_background_ids in", values, "roomBackgroundIds");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdsNotIn(List<String> values) {
            addCriterion("room_background_ids not in", values, "roomBackgroundIds");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdsBetween(String value1, String value2) {
            addCriterion("room_background_ids between", value1, value2, "roomBackgroundIds");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundIdsNotBetween(String value1, String value2) {
            addCriterion("room_background_ids not between", value1, value2, "roomBackgroundIds");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdsIsNull() {
            addCriterion("avatar_widget_ids is null");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdsIsNotNull() {
            addCriterion("avatar_widget_ids is not null");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdsEqualTo(String value) {
            addCriterion("avatar_widget_ids =", value, "avatarWidgetIds");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdsNotEqualTo(String value) {
            addCriterion("avatar_widget_ids <>", value, "avatarWidgetIds");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdsGreaterThan(String value) {
            addCriterion("avatar_widget_ids >", value, "avatarWidgetIds");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdsGreaterThanOrEqualTo(String value) {
            addCriterion("avatar_widget_ids >=", value, "avatarWidgetIds");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdsLessThan(String value) {
            addCriterion("avatar_widget_ids <", value, "avatarWidgetIds");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdsLessThanOrEqualTo(String value) {
            addCriterion("avatar_widget_ids <=", value, "avatarWidgetIds");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdsLike(String value) {
            addCriterion("avatar_widget_ids like", value, "avatarWidgetIds");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdsNotLike(String value) {
            addCriterion("avatar_widget_ids not like", value, "avatarWidgetIds");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdsIn(List<String> values) {
            addCriterion("avatar_widget_ids in", values, "avatarWidgetIds");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdsNotIn(List<String> values) {
            addCriterion("avatar_widget_ids not in", values, "avatarWidgetIds");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdsBetween(String value1, String value2) {
            addCriterion("avatar_widget_ids between", value1, value2, "avatarWidgetIds");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetIdsNotBetween(String value1, String value2) {
            addCriterion("avatar_widget_ids not between", value1, value2, "avatarWidgetIds");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementIsNull() {
            addCriterion("room_announcement is null");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementIsNotNull() {
            addCriterion("room_announcement is not null");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementEqualTo(String value) {
            addCriterion("room_announcement =", value, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementNotEqualTo(String value) {
            addCriterion("room_announcement <>", value, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementGreaterThan(String value) {
            addCriterion("room_announcement >", value, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementGreaterThanOrEqualTo(String value) {
            addCriterion("room_announcement >=", value, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementLessThan(String value) {
            addCriterion("room_announcement <", value, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementLessThanOrEqualTo(String value) {
            addCriterion("room_announcement <=", value, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementLike(String value) {
            addCriterion("room_announcement like", value, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementNotLike(String value) {
            addCriterion("room_announcement not like", value, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementIn(List<String> values) {
            addCriterion("room_announcement in", values, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementNotIn(List<String> values) {
            addCriterion("room_announcement not in", values, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementBetween(String value1, String value2) {
            addCriterion("room_announcement between", value1, value2, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementNotBetween(String value1, String value2) {
            addCriterion("room_announcement not between", value1, value2, "roomAnnouncement");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImageIsNull() {
            addCriterion("room_announcement_image is null");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImageIsNotNull() {
            addCriterion("room_announcement_image is not null");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImageEqualTo(String value) {
            addCriterion("room_announcement_image =", value, "roomAnnouncementImage");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImageNotEqualTo(String value) {
            addCriterion("room_announcement_image <>", value, "roomAnnouncementImage");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImageGreaterThan(String value) {
            addCriterion("room_announcement_image >", value, "roomAnnouncementImage");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImageGreaterThanOrEqualTo(String value) {
            addCriterion("room_announcement_image >=", value, "roomAnnouncementImage");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImageLessThan(String value) {
            addCriterion("room_announcement_image <", value, "roomAnnouncementImage");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImageLessThanOrEqualTo(String value) {
            addCriterion("room_announcement_image <=", value, "roomAnnouncementImage");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImageLike(String value) {
            addCriterion("room_announcement_image like", value, "roomAnnouncementImage");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImageNotLike(String value) {
            addCriterion("room_announcement_image not like", value, "roomAnnouncementImage");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImageIn(List<String> values) {
            addCriterion("room_announcement_image in", values, "roomAnnouncementImage");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImageNotIn(List<String> values) {
            addCriterion("room_announcement_image not in", values, "roomAnnouncementImage");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImageBetween(String value1, String value2) {
            addCriterion("room_announcement_image between", value1, value2, "roomAnnouncementImage");
            return (Criteria) this;
        }

        public Criteria andRoomAnnouncementImageNotBetween(String value1, String value2) {
            addCriterion("room_announcement_image not between", value1, value2, "roomAnnouncementImage");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Boolean value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Boolean value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Boolean value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Boolean value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Boolean> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Boolean> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andCoverIsNull() {
            addCriterion("cover is null");
            return (Criteria) this;
        }

        public Criteria andCoverIsNotNull() {
            addCriterion("cover is not null");
            return (Criteria) this;
        }

        public Criteria andCoverEqualTo(String value) {
            addCriterion("cover =", value, "cover");
            return (Criteria) this;
        }

        public Criteria andCoverNotEqualTo(String value) {
            addCriterion("cover <>", value, "cover");
            return (Criteria) this;
        }

        public Criteria andCoverGreaterThan(String value) {
            addCriterion("cover >", value, "cover");
            return (Criteria) this;
        }

        public Criteria andCoverGreaterThanOrEqualTo(String value) {
            addCriterion("cover >=", value, "cover");
            return (Criteria) this;
        }

        public Criteria andCoverLessThan(String value) {
            addCriterion("cover <", value, "cover");
            return (Criteria) this;
        }

        public Criteria andCoverLessThanOrEqualTo(String value) {
            addCriterion("cover <=", value, "cover");
            return (Criteria) this;
        }

        public Criteria andCoverLike(String value) {
            addCriterion("cover like", value, "cover");
            return (Criteria) this;
        }

        public Criteria andCoverNotLike(String value) {
            addCriterion("cover not like", value, "cover");
            return (Criteria) this;
        }

        public Criteria andCoverIn(List<String> values) {
            addCriterion("cover in", values, "cover");
            return (Criteria) this;
        }

        public Criteria andCoverNotIn(List<String> values) {
            addCriterion("cover not in", values, "cover");
            return (Criteria) this;
        }

        public Criteria andCoverBetween(String value1, String value2) {
            addCriterion("cover between", value1, value2, "cover");
            return (Criteria) this;
        }

        public Criteria andCoverNotBetween(String value1, String value2) {
            addCriterion("cover not between", value1, value2, "cover");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andWeightIsNull() {
            addCriterion("weight is null");
            return (Criteria) this;
        }

        public Criteria andWeightIsNotNull() {
            addCriterion("weight is not null");
            return (Criteria) this;
        }

        public Criteria andWeightEqualTo(Integer value) {
            addCriterion("weight =", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotEqualTo(Integer value) {
            addCriterion("weight <>", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightGreaterThan(Integer value) {
            addCriterion("weight >", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("weight >=", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightLessThan(Integer value) {
            addCriterion("weight <", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightLessThanOrEqualTo(Integer value) {
            addCriterion("weight <=", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightIn(List<Integer> values) {
            addCriterion("weight in", values, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotIn(List<Integer> values) {
            addCriterion("weight not in", values, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightBetween(Integer value1, Integer value2) {
            addCriterion("weight between", value1, value2, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotBetween(Integer value1, Integer value2) {
            addCriterion("weight not between", value1, value2, "weight");
            return (Criteria) this;
        }

        public Criteria andHotRecIsNull() {
            addCriterion("hot_rec is null");
            return (Criteria) this;
        }

        public Criteria andHotRecIsNotNull() {
            addCriterion("hot_rec is not null");
            return (Criteria) this;
        }

        public Criteria andHotRecEqualTo(Boolean value) {
            addCriterion("hot_rec =", value, "hotRec");
            return (Criteria) this;
        }

        public Criteria andHotRecNotEqualTo(Boolean value) {
            addCriterion("hot_rec <>", value, "hotRec");
            return (Criteria) this;
        }

        public Criteria andHotRecGreaterThan(Boolean value) {
            addCriterion("hot_rec >", value, "hotRec");
            return (Criteria) this;
        }

        public Criteria andHotRecGreaterThanOrEqualTo(Boolean value) {
            addCriterion("hot_rec >=", value, "hotRec");
            return (Criteria) this;
        }

        public Criteria andHotRecLessThan(Boolean value) {
            addCriterion("hot_rec <", value, "hotRec");
            return (Criteria) this;
        }

        public Criteria andHotRecLessThanOrEqualTo(Boolean value) {
            addCriterion("hot_rec <=", value, "hotRec");
            return (Criteria) this;
        }

        public Criteria andHotRecIn(List<Boolean> values) {
            addCriterion("hot_rec in", values, "hotRec");
            return (Criteria) this;
        }

        public Criteria andHotRecNotIn(List<Boolean> values) {
            addCriterion("hot_rec not in", values, "hotRec");
            return (Criteria) this;
        }

        public Criteria andHotRecBetween(Boolean value1, Boolean value2) {
            addCriterion("hot_rec between", value1, value2, "hotRec");
            return (Criteria) this;
        }

        public Criteria andHotRecNotBetween(Boolean value1, Boolean value2) {
            addCriterion("hot_rec not between", value1, value2, "hotRec");
            return (Criteria) this;
        }

        public Criteria andHotWeightIsNull() {
            addCriterion("hot_weight is null");
            return (Criteria) this;
        }

        public Criteria andHotWeightIsNotNull() {
            addCriterion("hot_weight is not null");
            return (Criteria) this;
        }

        public Criteria andHotWeightEqualTo(Integer value) {
            addCriterion("hot_weight =", value, "hotWeight");
            return (Criteria) this;
        }

        public Criteria andHotWeightNotEqualTo(Integer value) {
            addCriterion("hot_weight <>", value, "hotWeight");
            return (Criteria) this;
        }

        public Criteria andHotWeightGreaterThan(Integer value) {
            addCriterion("hot_weight >", value, "hotWeight");
            return (Criteria) this;
        }

        public Criteria andHotWeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("hot_weight >=", value, "hotWeight");
            return (Criteria) this;
        }

        public Criteria andHotWeightLessThan(Integer value) {
            addCriterion("hot_weight <", value, "hotWeight");
            return (Criteria) this;
        }

        public Criteria andHotWeightLessThanOrEqualTo(Integer value) {
            addCriterion("hot_weight <=", value, "hotWeight");
            return (Criteria) this;
        }

        public Criteria andHotWeightIn(List<Integer> values) {
            addCriterion("hot_weight in", values, "hotWeight");
            return (Criteria) this;
        }

        public Criteria andHotWeightNotIn(List<Integer> values) {
            addCriterion("hot_weight not in", values, "hotWeight");
            return (Criteria) this;
        }

        public Criteria andHotWeightBetween(Integer value1, Integer value2) {
            addCriterion("hot_weight between", value1, value2, "hotWeight");
            return (Criteria) this;
        }

        public Criteria andHotWeightNotBetween(Integer value1, Integer value2) {
            addCriterion("hot_weight not between", value1, value2, "hotWeight");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNull() {
            addCriterion("deploy_env is null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNotNull() {
            addCriterion("deploy_env is not null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvEqualTo(String value) {
            addCriterion("deploy_env =", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotEqualTo(String value) {
            addCriterion("deploy_env <>", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThan(String value) {
            addCriterion("deploy_env >", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThanOrEqualTo(String value) {
            addCriterion("deploy_env >=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThan(String value) {
            addCriterion("deploy_env <", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThanOrEqualTo(String value) {
            addCriterion("deploy_env <=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLike(String value) {
            addCriterion("deploy_env like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotLike(String value) {
            addCriterion("deploy_env not like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIn(List<String> values) {
            addCriterion("deploy_env in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotIn(List<String> values) {
            addCriterion("deploy_env not in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvBetween(String value1, String value2) {
            addCriterion("deploy_env between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotBetween(String value1, String value2) {
            addCriterion("deploy_env not between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNull() {
            addCriterion("operator is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNotNull() {
            addCriterion("operator is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorEqualTo(String value) {
            addCriterion("operator =", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotEqualTo(String value) {
            addCriterion("operator <>", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThan(String value) {
            addCriterion("operator >", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThanOrEqualTo(String value) {
            addCriterion("operator >=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThan(String value) {
            addCriterion("operator <", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThanOrEqualTo(String value) {
            addCriterion("operator <=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLike(String value) {
            addCriterion("operator like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotLike(String value) {
            addCriterion("operator not like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorIn(List<String> values) {
            addCriterion("operator in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotIn(List<String> values) {
            addCriterion("operator not in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorBetween(String value1, String value2) {
            addCriterion("operator between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotBetween(String value1, String value2) {
            addCriterion("operator not between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andAiGenIsNull() {
            addCriterion("ai_gen is null");
            return (Criteria) this;
        }

        public Criteria andAiGenIsNotNull() {
            addCriterion("ai_gen is not null");
            return (Criteria) this;
        }

        public Criteria andAiGenEqualTo(Boolean value) {
            addCriterion("ai_gen =", value, "aiGen");
            return (Criteria) this;
        }

        public Criteria andAiGenNotEqualTo(Boolean value) {
            addCriterion("ai_gen <>", value, "aiGen");
            return (Criteria) this;
        }

        public Criteria andAiGenGreaterThan(Boolean value) {
            addCriterion("ai_gen >", value, "aiGen");
            return (Criteria) this;
        }

        public Criteria andAiGenGreaterThanOrEqualTo(Boolean value) {
            addCriterion("ai_gen >=", value, "aiGen");
            return (Criteria) this;
        }

        public Criteria andAiGenLessThan(Boolean value) {
            addCriterion("ai_gen <", value, "aiGen");
            return (Criteria) this;
        }

        public Criteria andAiGenLessThanOrEqualTo(Boolean value) {
            addCriterion("ai_gen <=", value, "aiGen");
            return (Criteria) this;
        }

        public Criteria andAiGenIn(List<Boolean> values) {
            addCriterion("ai_gen in", values, "aiGen");
            return (Criteria) this;
        }

        public Criteria andAiGenNotIn(List<Boolean> values) {
            addCriterion("ai_gen not in", values, "aiGen");
            return (Criteria) this;
        }

        public Criteria andAiGenBetween(Boolean value1, Boolean value2) {
            addCriterion("ai_gen between", value1, value2, "aiGen");
            return (Criteria) this;
        }

        public Criteria andAiGenNotBetween(Boolean value1, Boolean value2) {
            addCriterion("ai_gen not between", value1, value2, "aiGen");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundLimitIsNull() {
            addCriterion("room_background_limit is null");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundLimitIsNotNull() {
            addCriterion("room_background_limit is not null");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundLimitEqualTo(Integer value) {
            addCriterion("room_background_limit =", value, "roomBackgroundLimit");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundLimitNotEqualTo(Integer value) {
            addCriterion("room_background_limit <>", value, "roomBackgroundLimit");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundLimitGreaterThan(Integer value) {
            addCriterion("room_background_limit >", value, "roomBackgroundLimit");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("room_background_limit >=", value, "roomBackgroundLimit");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundLimitLessThan(Integer value) {
            addCriterion("room_background_limit <", value, "roomBackgroundLimit");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundLimitLessThanOrEqualTo(Integer value) {
            addCriterion("room_background_limit <=", value, "roomBackgroundLimit");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundLimitIn(List<Integer> values) {
            addCriterion("room_background_limit in", values, "roomBackgroundLimit");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundLimitNotIn(List<Integer> values) {
            addCriterion("room_background_limit not in", values, "roomBackgroundLimit");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundLimitBetween(Integer value1, Integer value2) {
            addCriterion("room_background_limit between", value1, value2, "roomBackgroundLimit");
            return (Criteria) this;
        }

        public Criteria andRoomBackgroundLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("room_background_limit not between", value1, value2, "roomBackgroundLimit");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetLimitIsNull() {
            addCriterion("avatar_widget_limit is null");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetLimitIsNotNull() {
            addCriterion("avatar_widget_limit is not null");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetLimitEqualTo(Integer value) {
            addCriterion("avatar_widget_limit =", value, "avatarWidgetLimit");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetLimitNotEqualTo(Integer value) {
            addCriterion("avatar_widget_limit <>", value, "avatarWidgetLimit");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetLimitGreaterThan(Integer value) {
            addCriterion("avatar_widget_limit >", value, "avatarWidgetLimit");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("avatar_widget_limit >=", value, "avatarWidgetLimit");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetLimitLessThan(Integer value) {
            addCriterion("avatar_widget_limit <", value, "avatarWidgetLimit");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetLimitLessThanOrEqualTo(Integer value) {
            addCriterion("avatar_widget_limit <=", value, "avatarWidgetLimit");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetLimitIn(List<Integer> values) {
            addCriterion("avatar_widget_limit in", values, "avatarWidgetLimit");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetLimitNotIn(List<Integer> values) {
            addCriterion("avatar_widget_limit not in", values, "avatarWidgetLimit");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetLimitBetween(Integer value1, Integer value2) {
            addCriterion("avatar_widget_limit between", value1, value2, "avatarWidgetLimit");
            return (Criteria) this;
        }

        public Criteria andAvatarWidgetLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("avatar_widget_limit not between", value1, value2, "avatarWidgetLimit");
            return (Criteria) this;
        }

        public Criteria andActivityDurationLimitIsNull() {
            addCriterion("activity_duration_limit is null");
            return (Criteria) this;
        }

        public Criteria andActivityDurationLimitIsNotNull() {
            addCriterion("activity_duration_limit is not null");
            return (Criteria) this;
        }

        public Criteria andActivityDurationLimitEqualTo(Integer value) {
            addCriterion("activity_duration_limit =", value, "activityDurationLimit");
            return (Criteria) this;
        }

        public Criteria andActivityDurationLimitNotEqualTo(Integer value) {
            addCriterion("activity_duration_limit <>", value, "activityDurationLimit");
            return (Criteria) this;
        }

        public Criteria andActivityDurationLimitGreaterThan(Integer value) {
            addCriterion("activity_duration_limit >", value, "activityDurationLimit");
            return (Criteria) this;
        }

        public Criteria andActivityDurationLimitGreaterThanOrEqualTo(Integer value) {
            addCriterion("activity_duration_limit >=", value, "activityDurationLimit");
            return (Criteria) this;
        }

        public Criteria andActivityDurationLimitLessThan(Integer value) {
            addCriterion("activity_duration_limit <", value, "activityDurationLimit");
            return (Criteria) this;
        }

        public Criteria andActivityDurationLimitLessThanOrEqualTo(Integer value) {
            addCriterion("activity_duration_limit <=", value, "activityDurationLimit");
            return (Criteria) this;
        }

        public Criteria andActivityDurationLimitIn(List<Integer> values) {
            addCriterion("activity_duration_limit in", values, "activityDurationLimit");
            return (Criteria) this;
        }

        public Criteria andActivityDurationLimitNotIn(List<Integer> values) {
            addCriterion("activity_duration_limit not in", values, "activityDurationLimit");
            return (Criteria) this;
        }

        public Criteria andActivityDurationLimitBetween(Integer value1, Integer value2) {
            addCriterion("activity_duration_limit between", value1, value2, "activityDurationLimit");
            return (Criteria) this;
        }

        public Criteria andActivityDurationLimitNotBetween(Integer value1, Integer value2) {
            addCriterion("activity_duration_limit not between", value1, value2, "activityDurationLimit");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeLimitIsNull() {
            addCriterion("activity_start_time_limit is null");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeLimitIsNotNull() {
            addCriterion("activity_start_time_limit is not null");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeLimitEqualTo(Date value) {
            addCriterion("activity_start_time_limit =", value, "activityStartTimeLimit");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeLimitNotEqualTo(Date value) {
            addCriterion("activity_start_time_limit <>", value, "activityStartTimeLimit");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeLimitGreaterThan(Date value) {
            addCriterion("activity_start_time_limit >", value, "activityStartTimeLimit");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeLimitGreaterThanOrEqualTo(Date value) {
            addCriterion("activity_start_time_limit >=", value, "activityStartTimeLimit");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeLimitLessThan(Date value) {
            addCriterion("activity_start_time_limit <", value, "activityStartTimeLimit");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeLimitLessThanOrEqualTo(Date value) {
            addCriterion("activity_start_time_limit <=", value, "activityStartTimeLimit");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeLimitIn(List<Date> values) {
            addCriterion("activity_start_time_limit in", values, "activityStartTimeLimit");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeLimitNotIn(List<Date> values) {
            addCriterion("activity_start_time_limit not in", values, "activityStartTimeLimit");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeLimitBetween(Date value1, Date value2) {
            addCriterion("activity_start_time_limit between", value1, value2, "activityStartTimeLimit");
            return (Criteria) this;
        }

        public Criteria andActivityStartTimeLimitNotBetween(Date value1, Date value2) {
            addCriterion("activity_start_time_limit not between", value1, value2, "activityStartTimeLimit");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeLimitIsNull() {
            addCriterion("activity_end_time_limit is null");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeLimitIsNotNull() {
            addCriterion("activity_end_time_limit is not null");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeLimitEqualTo(Date value) {
            addCriterion("activity_end_time_limit =", value, "activityEndTimeLimit");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeLimitNotEqualTo(Date value) {
            addCriterion("activity_end_time_limit <>", value, "activityEndTimeLimit");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeLimitGreaterThan(Date value) {
            addCriterion("activity_end_time_limit >", value, "activityEndTimeLimit");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeLimitGreaterThanOrEqualTo(Date value) {
            addCriterion("activity_end_time_limit >=", value, "activityEndTimeLimit");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeLimitLessThan(Date value) {
            addCriterion("activity_end_time_limit <", value, "activityEndTimeLimit");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeLimitLessThanOrEqualTo(Date value) {
            addCriterion("activity_end_time_limit <=", value, "activityEndTimeLimit");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeLimitIn(List<Date> values) {
            addCriterion("activity_end_time_limit in", values, "activityEndTimeLimit");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeLimitNotIn(List<Date> values) {
            addCriterion("activity_end_time_limit not in", values, "activityEndTimeLimit");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeLimitBetween(Date value1, Date value2) {
            addCriterion("activity_end_time_limit between", value1, value2, "activityEndTimeLimit");
            return (Criteria) this;
        }

        public Criteria andActivityEndTimeLimitNotBetween(Date value1, Date value2) {
            addCriterion("activity_end_time_limit not between", value1, value2, "activityEndTimeLimit");
            return (Criteria) this;
        }

        public Criteria andUpStartTimeIsNull() {
            addCriterion("up_start_time is null");
            return (Criteria) this;
        }

        public Criteria andUpStartTimeIsNotNull() {
            addCriterion("up_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpStartTimeEqualTo(Date value) {
            addCriterion("up_start_time =", value, "upStartTime");
            return (Criteria) this;
        }

        public Criteria andUpStartTimeNotEqualTo(Date value) {
            addCriterion("up_start_time <>", value, "upStartTime");
            return (Criteria) this;
        }

        public Criteria andUpStartTimeGreaterThan(Date value) {
            addCriterion("up_start_time >", value, "upStartTime");
            return (Criteria) this;
        }

        public Criteria andUpStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("up_start_time >=", value, "upStartTime");
            return (Criteria) this;
        }

        public Criteria andUpStartTimeLessThan(Date value) {
            addCriterion("up_start_time <", value, "upStartTime");
            return (Criteria) this;
        }

        public Criteria andUpStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("up_start_time <=", value, "upStartTime");
            return (Criteria) this;
        }

        public Criteria andUpStartTimeIn(List<Date> values) {
            addCriterion("up_start_time in", values, "upStartTime");
            return (Criteria) this;
        }

        public Criteria andUpStartTimeNotIn(List<Date> values) {
            addCriterion("up_start_time not in", values, "upStartTime");
            return (Criteria) this;
        }

        public Criteria andUpStartTimeBetween(Date value1, Date value2) {
            addCriterion("up_start_time between", value1, value2, "upStartTime");
            return (Criteria) this;
        }

        public Criteria andUpStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("up_start_time not between", value1, value2, "upStartTime");
            return (Criteria) this;
        }

        public Criteria andUpEndTimeIsNull() {
            addCriterion("up_end_time is null");
            return (Criteria) this;
        }

        public Criteria andUpEndTimeIsNotNull() {
            addCriterion("up_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpEndTimeEqualTo(Date value) {
            addCriterion("up_end_time =", value, "upEndTime");
            return (Criteria) this;
        }

        public Criteria andUpEndTimeNotEqualTo(Date value) {
            addCriterion("up_end_time <>", value, "upEndTime");
            return (Criteria) this;
        }

        public Criteria andUpEndTimeGreaterThan(Date value) {
            addCriterion("up_end_time >", value, "upEndTime");
            return (Criteria) this;
        }

        public Criteria andUpEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("up_end_time >=", value, "upEndTime");
            return (Criteria) this;
        }

        public Criteria andUpEndTimeLessThan(Date value) {
            addCriterion("up_end_time <", value, "upEndTime");
            return (Criteria) this;
        }

        public Criteria andUpEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("up_end_time <=", value, "upEndTime");
            return (Criteria) this;
        }

        public Criteria andUpEndTimeIn(List<Date> values) {
            addCriterion("up_end_time in", values, "upEndTime");
            return (Criteria) this;
        }

        public Criteria andUpEndTimeNotIn(List<Date> values) {
            addCriterion("up_end_time not in", values, "upEndTime");
            return (Criteria) this;
        }

        public Criteria andUpEndTimeBetween(Date value1, Date value2) {
            addCriterion("up_end_time between", value1, value2, "upEndTime");
            return (Criteria) this;
        }

        public Criteria andUpEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("up_end_time not between", value1, value2, "upEndTime");
            return (Criteria) this;
        }

        public Criteria andWhiteNjUserGroupIdIsNull() {
            addCriterion("white_nj_user_group_id is null");
            return (Criteria) this;
        }

        public Criteria andWhiteNjUserGroupIdIsNotNull() {
            addCriterion("white_nj_user_group_id is not null");
            return (Criteria) this;
        }

        public Criteria andWhiteNjUserGroupIdEqualTo(Long value) {
            addCriterion("white_nj_user_group_id =", value, "whiteNjUserGroupId");
            return (Criteria) this;
        }

        public Criteria andWhiteNjUserGroupIdNotEqualTo(Long value) {
            addCriterion("white_nj_user_group_id <>", value, "whiteNjUserGroupId");
            return (Criteria) this;
        }

        public Criteria andWhiteNjUserGroupIdGreaterThan(Long value) {
            addCriterion("white_nj_user_group_id >", value, "whiteNjUserGroupId");
            return (Criteria) this;
        }

        public Criteria andWhiteNjUserGroupIdGreaterThanOrEqualTo(Long value) {
            addCriterion("white_nj_user_group_id >=", value, "whiteNjUserGroupId");
            return (Criteria) this;
        }

        public Criteria andWhiteNjUserGroupIdLessThan(Long value) {
            addCriterion("white_nj_user_group_id <", value, "whiteNjUserGroupId");
            return (Criteria) this;
        }

        public Criteria andWhiteNjUserGroupIdLessThanOrEqualTo(Long value) {
            addCriterion("white_nj_user_group_id <=", value, "whiteNjUserGroupId");
            return (Criteria) this;
        }

        public Criteria andWhiteNjUserGroupIdIn(List<Long> values) {
            addCriterion("white_nj_user_group_id in", values, "whiteNjUserGroupId");
            return (Criteria) this;
        }

        public Criteria andWhiteNjUserGroupIdNotIn(List<Long> values) {
            addCriterion("white_nj_user_group_id not in", values, "whiteNjUserGroupId");
            return (Criteria) this;
        }

        public Criteria andWhiteNjUserGroupIdBetween(Long value1, Long value2) {
            addCriterion("white_nj_user_group_id between", value1, value2, "whiteNjUserGroupId");
            return (Criteria) this;
        }

        public Criteria andWhiteNjUserGroupIdNotBetween(Long value1, Long value2) {
            addCriterion("white_nj_user_group_id not between", value1, value2, "whiteNjUserGroupId");
            return (Criteria) this;
        }

        public Criteria andGiftIdsIsNull() {
            addCriterion("gift_ids is null");
            return (Criteria) this;
        }

        public Criteria andGiftIdsIsNotNull() {
            addCriterion("gift_ids is not null");
            return (Criteria) this;
        }

        public Criteria andGiftIdsEqualTo(String value) {
            addCriterion("gift_ids =", value, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsNotEqualTo(String value) {
            addCriterion("gift_ids <>", value, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsGreaterThan(String value) {
            addCriterion("gift_ids >", value, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsGreaterThanOrEqualTo(String value) {
            addCriterion("gift_ids >=", value, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsLessThan(String value) {
            addCriterion("gift_ids <", value, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsLessThanOrEqualTo(String value) {
            addCriterion("gift_ids <=", value, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsLike(String value) {
            addCriterion("gift_ids like", value, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsNotLike(String value) {
            addCriterion("gift_ids not like", value, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsIn(List<String> values) {
            addCriterion("gift_ids in", values, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsNotIn(List<String> values) {
            addCriterion("gift_ids not in", values, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsBetween(String value1, String value2) {
            addCriterion("gift_ids between", value1, value2, "giftIds");
            return (Criteria) this;
        }

        public Criteria andGiftIdsNotBetween(String value1, String value2) {
            addCriterion("gift_ids not between", value1, value2, "giftIds");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdIsNull() {
            addCriterion("room_mark_id is null");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdIsNotNull() {
            addCriterion("room_mark_id is not null");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdEqualTo(Long value) {
            addCriterion("room_mark_id =", value, "roomMarkId");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdNotEqualTo(Long value) {
            addCriterion("room_mark_id <>", value, "roomMarkId");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdGreaterThan(Long value) {
            addCriterion("room_mark_id >", value, "roomMarkId");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdGreaterThanOrEqualTo(Long value) {
            addCriterion("room_mark_id >=", value, "roomMarkId");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdLessThan(Long value) {
            addCriterion("room_mark_id <", value, "roomMarkId");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdLessThanOrEqualTo(Long value) {
            addCriterion("room_mark_id <=", value, "roomMarkId");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdIn(List<Long> values) {
            addCriterion("room_mark_id in", values, "roomMarkId");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdNotIn(List<Long> values) {
            addCriterion("room_mark_id not in", values, "roomMarkId");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdBetween(Long value1, Long value2) {
            addCriterion("room_mark_id between", value1, value2, "roomMarkId");
            return (Criteria) this;
        }

        public Criteria andRoomMarkIdNotBetween(Long value1, Long value2) {
            addCriterion("room_mark_id not between", value1, value2, "roomMarkId");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlIsNull() {
            addCriterion("room_mark_url is null");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlIsNotNull() {
            addCriterion("room_mark_url is not null");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlEqualTo(String value) {
            addCriterion("room_mark_url =", value, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlNotEqualTo(String value) {
            addCriterion("room_mark_url <>", value, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlGreaterThan(String value) {
            addCriterion("room_mark_url >", value, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlGreaterThanOrEqualTo(String value) {
            addCriterion("room_mark_url >=", value, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlLessThan(String value) {
            addCriterion("room_mark_url <", value, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlLessThanOrEqualTo(String value) {
            addCriterion("room_mark_url <=", value, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlLike(String value) {
            addCriterion("room_mark_url like", value, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlNotLike(String value) {
            addCriterion("room_mark_url not like", value, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlIn(List<String> values) {
            addCriterion("room_mark_url in", values, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlNotIn(List<String> values) {
            addCriterion("room_mark_url not in", values, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlBetween(String value1, String value2) {
            addCriterion("room_mark_url between", value1, value2, "roomMarkUrl");
            return (Criteria) this;
        }

        public Criteria andRoomMarkUrlNotBetween(String value1, String value2) {
            addCriterion("room_mark_url not between", value1, value2, "roomMarkUrl");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table activity_template_info
     *
     * @mbg.generated do_not_delete_during_merge Fri Sep 05 16:39:06 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table activity_template_info
     *
     * @mbg.generated Fri Sep 05 16:39:06 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}