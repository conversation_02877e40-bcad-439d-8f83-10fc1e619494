package fm.lizhi.ocean.wavecenter.datastore.background.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动推荐卡配置
 *
 * @date 2024-10-10 06:34:31
 */
@Table(name = "`activity_recommend_card_config`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityRecommendCardConfig {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用id
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 等级ID
     */
    @Column(name= "`level_id`")
    private Long levelId;

    /**
     * 发放张数
     */
    @Column(name= "`count`")
    private Integer count;

    /**
     * 有效天数
     */
    @Column(name= "`valid_day`")
    private Integer validDay;

    /**
     * 环境隔离 TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 是否删除
     */
    @Column(name= "`deleted`")
    private Integer deleted;

    /**
     * 操作者
     */
    @Column(name= "`operator`")
    private String operator;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", levelId=").append(levelId);
        sb.append(", count=").append(count);
        sb.append(", validDay=").append(validDay);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", deleted=").append(deleted);
        sb.append(", operator=").append(operator);
        sb.append("]");
        return sb.toString();
    }
}