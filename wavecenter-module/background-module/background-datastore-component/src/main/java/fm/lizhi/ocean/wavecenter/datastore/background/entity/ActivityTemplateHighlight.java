package fm.lizhi.ocean.wavecenter.datastore.background.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动模板亮点标签表
 *
 * @date 2025-06-27 06:17:53
 */
@Table(name = "`activity_template_highlight`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityTemplateHighlight {
    /**
     * 活动模板亮点标签ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 活动模板ID
     */
    @Column(name= "`template_id`")
    private Long templateId;

    /**
     * 亮点标签key
     */
    @Column(name= "`highlight_key`")
    private String highlightKey;

    /**
     * 亮点标签value
     */
    @Column(name= "`highlight_value`")
    private String highlightValue;

    /**
     * 排序
     */
    @Column(name= "`index`")
    private Integer index;

    /**
     * 0：亮点标签，1：奖励标签
     */
    @Column(name= "`type`")
    private Integer type;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", templateId=").append(templateId);
        sb.append(", highlightKey=").append(highlightKey);
        sb.append(", highlightValue=").append(highlightValue);
        sb.append(", index=").append(index);
        sb.append(", type=").append(type);
        sb.append("]");
        return sb.toString();
    }
}