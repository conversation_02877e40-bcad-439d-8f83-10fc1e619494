package fm.lizhi.ocean.wavecenter.datastore.background.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 品类-公告关联表
 *
 * @date 2025-05-16 11:35:21
 */
@Table(name = "`activity_notice_category_relation`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityNoticeCategoryRelation {
    /**
     * ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 公告ID
     */
    @Column(name= "`notice_id`")
    private Long noticeId;

    /**
     * 品类值
     */
    @Column(name= "`category_value`")
    private Integer categoryValue;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 环境区分
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", noticeId=").append(noticeId);
        sb.append(", categoryValue=").append(categoryValue);
        sb.append(", appId=").append(appId);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append("]");
        return sb.toString();
    }
}