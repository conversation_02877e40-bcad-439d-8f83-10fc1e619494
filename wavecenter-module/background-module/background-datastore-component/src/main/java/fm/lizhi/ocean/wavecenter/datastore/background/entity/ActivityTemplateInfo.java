package fm.lizhi.ocean.wavecenter.datastore.background.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动模板信息表
 *
 * @date 2025-09-05 04:39:06
 */
@Table(name = "`activity_template_info`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityTemplateInfo {
    /**
     * 活动模板ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 活动名
     */
    @Column(name= "`name`")
    private String name;

    /**
     * 分类ID，对应activity_class_config.id
     */
    @Column(name= "`class_id`")
    private Long classId;

    /**
     * 活动目标
     */
    @Column(name= "`goal`")
    private String goal;

    /**
     * 辅助道具地址列表，逗号分隔，平台域名，相对路径，斜杆开头
     */
    @Column(name= "`auxiliary_prop_url`")
    private String auxiliaryPropUrl;

    /**
     * 活动海报地址，平台域名，相对路径，斜杆开头
     */
    @Column(name= "`poster_url`")
    private String posterUrl;

    /**
     * 玩法工具列表，逗号分隔.
     */
    @Column(name= "`activity_tool`")
    private String activityTool;

    /**
     * 房间背景ID列表，逗号分隔
     */
    @Column(name= "`room_background_ids`")
    private String roomBackgroundIds;

    /**
     * 头像框ID列表，逗号分隔
     */
    @Column(name= "`avatar_widget_ids`")
    private String avatarWidgetIds;

    /**
     * 房间公告
     */
    @Column(name= "`room_announcement`")
    private String roomAnnouncement;

    /**
     * 房间公告图片列表，逗号分隔，平台域名，相对路径，斜杆开头
     */
    @Column(name= "`room_announcement_image`")
    private String roomAnnouncementImage;

    /**
     * 是否已删除，0：否，1：是
     */
    @Column(name= "`deleted`")
    private Boolean deleted;

    /**
     * 封面地址，平台域名，相对路径，斜杠开头
     */
    @Column(name= "`cover`")
    private String cover;

    /**
     * 上下架状态，1：下架，2：上架
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 权重，权重值高的排在前面
     */
    @Column(name= "`weight`")
    private Integer weight;

    /**
     * 是否上热门推荐，0：否，1：是
     */
    @Column(name= "`hot_rec`")
    private Boolean hotRec;

    /**
     * 热门权重，权重值高的排在前面，当筛选热门推荐时，按此权重排序
     */
    @Column(name= "`hot_weight`")
    private Integer hotWeight;

    /**
     * 服务部署环境，TEST/PRE/PRO，用于环境隔离
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 操作者
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 是否是 AI 生成的模板
     */
    @Column(name= "`ai_gen`")
    private Boolean aiGen;

    /**
     * 允许选择的背景数,-1表示没有限制
     */
    @Column(name= "`room_background_limit`")
    private Integer roomBackgroundLimit;

    /**
     * 允许选择的头像框数量,-1表示没有限制
     */
    @Column(name= "`avatar_widget_limit`")
    private Integer avatarWidgetLimit;

    /**
     * 活动时长限制，单位分钟
     */
    @Column(name= "`activity_duration_limit`")
    private Integer activityDurationLimit;

    /**
     * 活动举办开始时间限制
     */
    @Column(name= "`activity_start_time_limit`")
    private Date activityStartTimeLimit;

    /**
     * 活动举办结束时间限制
     */
    @Column(name= "`activity_end_time_limit`")
    private Date activityEndTimeLimit;

    /**
     * 上架开始时间
     */
    @Column(name= "`up_start_time`")
    private Date upStartTime;

    /**
     * 上架结束时间
     */
    @Column(name= "`up_end_time`")
    private Date upEndTime;

    /**
     * 白名单厅主用户组ID，礼物或挂件等用到，使用该模板的活动首次审批通过时创建
     */
    @Column(name= "`white_nj_user_group_id`")
    private Long whiteNjUserGroupId;

    /**
     * 礼物列表ID列表, 逗号分隔
     */
    @Column(name= "`gift_ids`")
    private String giftIds;

    /**
     * 房间角标 ID
     */
    @Column(name= "`room_mark_id`")
    private Long roomMarkId;

    /**
     * 房间角标URL,xm的角标只是一张图片配置，所以没有 ID
     */
    @Column(name= "`room_mark_url`")
    private String roomMarkUrl;

    /**
     * 活动介绍，支持富文本
     */
    @Column(name= "`introduction`")
    private String introduction;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", name=").append(name);
        sb.append(", classId=").append(classId);
        sb.append(", goal=").append(goal);
        sb.append(", auxiliaryPropUrl=").append(auxiliaryPropUrl);
        sb.append(", posterUrl=").append(posterUrl);
        sb.append(", activityTool=").append(activityTool);
        sb.append(", roomBackgroundIds=").append(roomBackgroundIds);
        sb.append(", avatarWidgetIds=").append(avatarWidgetIds);
        sb.append(", roomAnnouncement=").append(roomAnnouncement);
        sb.append(", roomAnnouncementImage=").append(roomAnnouncementImage);
        sb.append(", deleted=").append(deleted);
        sb.append(", cover=").append(cover);
        sb.append(", status=").append(status);
        sb.append(", weight=").append(weight);
        sb.append(", hotRec=").append(hotRec);
        sb.append(", hotWeight=").append(hotWeight);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", operator=").append(operator);
        sb.append(", aiGen=").append(aiGen);
        sb.append(", roomBackgroundLimit=").append(roomBackgroundLimit);
        sb.append(", avatarWidgetLimit=").append(avatarWidgetLimit);
        sb.append(", activityDurationLimit=").append(activityDurationLimit);
        sb.append(", activityStartTimeLimit=").append(activityStartTimeLimit);
        sb.append(", activityEndTimeLimit=").append(activityEndTimeLimit);
        sb.append(", upStartTime=").append(upStartTime);
        sb.append(", upEndTime=").append(upEndTime);
        sb.append(", whiteNjUserGroupId=").append(whiteNjUserGroupId);
        sb.append(", giftIds=").append(giftIds);
        sb.append(", roomMarkId=").append(roomMarkId);
        sb.append(", roomMarkUrl=").append(roomMarkUrl);
        sb.append(", introduction=").append(introduction);
        sb.append("]");
        return sb.toString();
    }
}