package fm.lizhi.ocean.wavecenter.datastore.background.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动分类配置表
 *
 * @date 2024-10-28 05:11:30
 */
@Table(name = "`activity_class_config`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityClassConfig {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 主类型ID
     */
    @Column(name= "`big_class_id`")
    private Long bigClassId;

    /**
     * 类型名称
     */
    @Column(name= "`name`")
    private String name;

    /**
     * 等级ID
     */
    @Column(name= "`level_id`")
    private Long levelId;

    /**
     * 权重
     */
    @Column(name= "`weight`")
    private Integer weight;

    /**
     * 环境隔离 TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 逻辑删除
     */
    @Column(name= "`deleted`")
    private Boolean deleted;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", bigClassId=").append(bigClassId);
        sb.append(", name=").append(name);
        sb.append(", levelId=").append(levelId);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", deleted=").append(deleted);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", operator=").append(operator);
        sb.append("]");
        return sb.toString();
    }
}