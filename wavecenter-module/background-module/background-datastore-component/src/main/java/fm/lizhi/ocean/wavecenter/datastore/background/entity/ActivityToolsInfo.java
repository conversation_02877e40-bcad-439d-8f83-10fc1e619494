package fm.lizhi.ocean.wavecenter.datastore.background.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 玩法工具库
 *
 * @date 2025-05-08 02:04:22
 */
@Table(name = "`activity_tools_info`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityToolsInfo {
    /**
     * ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 业务ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 工具名称
     */
    @Column(name= "`name`")
    private String name;

    /**
     * 玩法工具值，用于关联其他表
     */
    @Column(name= "`tool_value`")
    private Integer toolValue;

    /**
     * 类型, 1:玩法; 2: 工具
     */
    @Column(name= "`type`")
    private Integer type;

    /**
     * 玩法工具描述
     */
    @Column(name= "`tool_desc`")
    private String toolDesc;

    /**
     * 状态 0: 下架; 1: 上架
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 可选值  TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", name=").append(name);
        sb.append(", toolValue=").append(toolValue);
        sb.append(", type=").append(type);
        sb.append(", toolDesc=").append(toolDesc);
        sb.append(", status=").append(status);
        sb.append(", operator=").append(operator);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append("]");
        return sb.toString();
    }
}