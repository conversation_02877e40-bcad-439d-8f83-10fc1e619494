package fm.lizhi.ocean.wavecenter.datastore.background.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动模板流量资源表
 *
 * @date 2024-11-07 03:11:36
 */
@Table(name = "`activity_template_flow_resource`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityTemplateFlowResource {
    /**
     * 活动模板流量资源ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 活动模板ID
     */
    @Column(name= "`template_id`")
    private Long templateId;

    /**
     * 资源配置ID，对应activity_resource_config.id
     */
    @Column(name= "`resource_config_id`")
    private Long resourceConfigId;

    /**
     * 排序
     */
    @Column(name= "`index`")
    private Integer index;

    /**
     * 资源扩展配置，JSON结构
     */
    @Column(name= "`extra`")
    private String extra;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", templateId=").append(templateId);
        sb.append(", resourceConfigId=").append(resourceConfigId);
        sb.append(", index=").append(index);
        sb.append(", extra=").append(extra);
        sb.append("]");
        return sb.toString();
    }
}