package fm.lizhi.ocean.wavecenter.datastore.background.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动模板状态变更任务
 *
 * @date 2025-05-08 05:02:08
 */
@Table(name = "`activity_template_status_task`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityTemplateStatusTask {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 模板ID
     */
    @Column(name= "`template_id`")
    private Long templateId;

    /**
     * 目标状态，1：下架，2：上架
     */
    @Column(name= "`template_target_status`")
    private Integer templateTargetStatus;

    /**
     * 任务执行时间
     */
    @Column(name= "`execute_time`")
    private Date executeTime;

    /**
     * 任务状态，0=待执行，1=已执行
     */
    @Column(name= "`task_status`")
    private Integer taskStatus;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`update_time`")
    private Date updateTime;

    /**
     * 0=未删除，1=已删除
     */
    @Column(name= "`deleted`")
    private Integer deleted;

    /**
     * 业务
     */
    @Column(name= "`app_id`")
    private Integer appId;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", templateId=").append(templateId);
        sb.append(", templateTargetStatus=").append(templateTargetStatus);
        sb.append(", executeTime=").append(executeTime);
        sb.append(", taskStatus=").append(taskStatus);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleted=").append(deleted);
        sb.append(", appId=").append(appId);
        sb.append("]");
        return sb.toString();
    }
}