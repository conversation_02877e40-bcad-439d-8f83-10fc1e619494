package fm.lizhi.ocean.wavecenter.datastore.background.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动图片素材表
 *
 * @date 2024-10-18 04:27:09
 */
@Table(name = "`activity_image_fodder`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityImageFodder {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 素材名称
     */
    @Column(name= "`name`")
    private String name;

    /**
     * 素材分类，参考枚举值
     */
    @Column(name= "`type`")
    private Integer type;

    /**
     * 宽高比
     */
    @Column(name= "`scale`")
    private String scale;

    /**
     * 素材颜色，包含#
     */
    @Column(name= "`color`")
    private String color;

    /**
     * 素材图片地址，斜杠开头
     */
    @Column(name= "`image_url`")
    private String imageUrl;

    /**
     * 是否删除
     */
    @Column(name= "`deleted`")
    private Integer deleted;

    /**
     * 环境隔离 TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", name=").append(name);
        sb.append(", type=").append(type);
        sb.append(", scale=").append(scale);
        sb.append(", color=").append(color);
        sb.append(", imageUrl=").append(imageUrl);
        sb.append(", deleted=").append(deleted);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", operator=").append(operator);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }
}