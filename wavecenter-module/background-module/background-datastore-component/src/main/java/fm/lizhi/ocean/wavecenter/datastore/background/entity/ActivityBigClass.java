package fm.lizhi.ocean.wavecenter.datastore.background.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动大类表
 *
 * @date 2024-12-16 10:29:53
 */
@Table(name = "`activity_big_class`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityBigClass {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 类型名称
     */
    @Column(name= "`name`")
    private String name;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 权重值，用于排序
     */
    @Column(name= "`weight`")
    private Integer weight;

    /**
     * 是否逻辑删除
     */
    @Column(name= "`deleted`")
    private Boolean deleted;

    /**
     * 环境隔离 TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 分类类型，1：常规，2：厅战
     */
    @Column(name= "`type`")
    private Integer type;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", appId=").append(appId);
        sb.append(", weight=").append(weight);
        sb.append(", deleted=").append(deleted);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", operator=").append(operator);
        sb.append(", type=").append(type);
        sb.append("]");
        return sb.toString();
    }
}