package fm.lizhi.ocean.wavecenter.datastore.background.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动公告配置表
 *
 * @date 2025-05-08 04:04:01
 */
@Table(name = "`activity_notice_config`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityNoticeConfig {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 环境，测试：TEST,预发：PRE,线上：PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 创建者
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 公告内容
     */
    @Column(name= "`content`")
    private String content;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", operator=").append(operator);
        sb.append(", createTime=").append(createTime);
        sb.append(", content=").append(content);
        sb.append("]");
        return sb.toString();
    }
}