package fm.lizhi.ocean.wavecenter.datastore.background.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 资源-等级关联表
 *
 * @date 2024-10-10 06:34:31
 */
@Table(name = "`activity_resource_level_relation`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityResourceLevelRelation {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    @Column(name= "`resource_id`")
    private Long resourceId;

    @Column(name= "`level_id`")
    private Long levelId;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", resourceId=").append(resourceId);
        sb.append(", levelId=").append(levelId);
        sb.append("]");
        return sb.toString();
    }
}