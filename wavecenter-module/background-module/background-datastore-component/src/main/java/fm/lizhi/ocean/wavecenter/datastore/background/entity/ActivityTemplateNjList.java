package fm.lizhi.ocean.wavecenter.datastore.background.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动模板厅主白名单黑名单列表
 *
 * @date 2025-05-08 05:02:08
 */
@Table(name = "`activity_template_nj_list`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityTemplateNjList {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 模板ID
     */
    @Column(name= "`template_id`")
    private Long templateId;

    /**
     * 厅主ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`update_time`")
    private Date updateTime;

    /**
     * 业务
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 1=白名单，2=黑名单
     */
    @Column(name= "`list_type`")
    private Integer listType;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", templateId=").append(templateId);
        sb.append(", njId=").append(njId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", appId=").append(appId);
        sb.append(", listType=").append(listType);
        sb.append("]");
        return sb.toString();
    }
}