package fm.lizhi.ocean.wavecenter.datastore.background.entity;

import java.util.ArrayList;
import java.util.List;

public class ActivityTemplateHighlightExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_highlight
     *
     * @mbg.generated Fri Jun 27 18:17:53 CST 2025
     */
    public ActivityTemplateHighlightExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_highlight
     *
     * @mbg.generated Fri Jun 27 18:17:53 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_highlight
     *
     * @mbg.generated Fri Jun 27 18:17:53 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_highlight
     *
     * @mbg.generated Fri Jun 27 18:17:53 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_highlight
     *
     * @mbg.generated Fri Jun 27 18:17:53 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_highlight
     *
     * @mbg.generated Fri Jun 27 18:17:53 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_highlight
     *
     * @mbg.generated Fri Jun 27 18:17:53 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_highlight
     *
     * @mbg.generated Fri Jun 27 18:17:53 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_highlight
     *
     * @mbg.generated Fri Jun 27 18:17:53 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_highlight
     *
     * @mbg.generated Fri Jun 27 18:17:53 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table activity_template_highlight
     *
     * @mbg.generated Fri Jun 27 18:17:53 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return ActivityTemplateHighlight.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table activity_template_highlight
     *
     * @mbg.generated Fri Jun 27 18:17:53 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNull() {
            addCriterion("template_id is null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNotNull() {
            addCriterion("template_id is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdEqualTo(Long value) {
            addCriterion("template_id =", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotEqualTo(Long value) {
            addCriterion("template_id <>", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThan(Long value) {
            addCriterion("template_id >", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanOrEqualTo(Long value) {
            addCriterion("template_id >=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThan(Long value) {
            addCriterion("template_id <", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanOrEqualTo(Long value) {
            addCriterion("template_id <=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIn(List<Long> values) {
            addCriterion("template_id in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotIn(List<Long> values) {
            addCriterion("template_id not in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdBetween(Long value1, Long value2) {
            addCriterion("template_id between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotBetween(Long value1, Long value2) {
            addCriterion("template_id not between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andHighlightKeyIsNull() {
            addCriterion("highlight_key is null");
            return (Criteria) this;
        }

        public Criteria andHighlightKeyIsNotNull() {
            addCriterion("highlight_key is not null");
            return (Criteria) this;
        }

        public Criteria andHighlightKeyEqualTo(String value) {
            addCriterion("highlight_key =", value, "highlightKey");
            return (Criteria) this;
        }

        public Criteria andHighlightKeyNotEqualTo(String value) {
            addCriterion("highlight_key <>", value, "highlightKey");
            return (Criteria) this;
        }

        public Criteria andHighlightKeyGreaterThan(String value) {
            addCriterion("highlight_key >", value, "highlightKey");
            return (Criteria) this;
        }

        public Criteria andHighlightKeyGreaterThanOrEqualTo(String value) {
            addCriterion("highlight_key >=", value, "highlightKey");
            return (Criteria) this;
        }

        public Criteria andHighlightKeyLessThan(String value) {
            addCriterion("highlight_key <", value, "highlightKey");
            return (Criteria) this;
        }

        public Criteria andHighlightKeyLessThanOrEqualTo(String value) {
            addCriterion("highlight_key <=", value, "highlightKey");
            return (Criteria) this;
        }

        public Criteria andHighlightKeyLike(String value) {
            addCriterion("highlight_key like", value, "highlightKey");
            return (Criteria) this;
        }

        public Criteria andHighlightKeyNotLike(String value) {
            addCriterion("highlight_key not like", value, "highlightKey");
            return (Criteria) this;
        }

        public Criteria andHighlightKeyIn(List<String> values) {
            addCriterion("highlight_key in", values, "highlightKey");
            return (Criteria) this;
        }

        public Criteria andHighlightKeyNotIn(List<String> values) {
            addCriterion("highlight_key not in", values, "highlightKey");
            return (Criteria) this;
        }

        public Criteria andHighlightKeyBetween(String value1, String value2) {
            addCriterion("highlight_key between", value1, value2, "highlightKey");
            return (Criteria) this;
        }

        public Criteria andHighlightKeyNotBetween(String value1, String value2) {
            addCriterion("highlight_key not between", value1, value2, "highlightKey");
            return (Criteria) this;
        }

        public Criteria andHighlightValueIsNull() {
            addCriterion("highlight_value is null");
            return (Criteria) this;
        }

        public Criteria andHighlightValueIsNotNull() {
            addCriterion("highlight_value is not null");
            return (Criteria) this;
        }

        public Criteria andHighlightValueEqualTo(String value) {
            addCriterion("highlight_value =", value, "highlightValue");
            return (Criteria) this;
        }

        public Criteria andHighlightValueNotEqualTo(String value) {
            addCriterion("highlight_value <>", value, "highlightValue");
            return (Criteria) this;
        }

        public Criteria andHighlightValueGreaterThan(String value) {
            addCriterion("highlight_value >", value, "highlightValue");
            return (Criteria) this;
        }

        public Criteria andHighlightValueGreaterThanOrEqualTo(String value) {
            addCriterion("highlight_value >=", value, "highlightValue");
            return (Criteria) this;
        }

        public Criteria andHighlightValueLessThan(String value) {
            addCriterion("highlight_value <", value, "highlightValue");
            return (Criteria) this;
        }

        public Criteria andHighlightValueLessThanOrEqualTo(String value) {
            addCriterion("highlight_value <=", value, "highlightValue");
            return (Criteria) this;
        }

        public Criteria andHighlightValueLike(String value) {
            addCriterion("highlight_value like", value, "highlightValue");
            return (Criteria) this;
        }

        public Criteria andHighlightValueNotLike(String value) {
            addCriterion("highlight_value not like", value, "highlightValue");
            return (Criteria) this;
        }

        public Criteria andHighlightValueIn(List<String> values) {
            addCriterion("highlight_value in", values, "highlightValue");
            return (Criteria) this;
        }

        public Criteria andHighlightValueNotIn(List<String> values) {
            addCriterion("highlight_value not in", values, "highlightValue");
            return (Criteria) this;
        }

        public Criteria andHighlightValueBetween(String value1, String value2) {
            addCriterion("highlight_value between", value1, value2, "highlightValue");
            return (Criteria) this;
        }

        public Criteria andHighlightValueNotBetween(String value1, String value2) {
            addCriterion("highlight_value not between", value1, value2, "highlightValue");
            return (Criteria) this;
        }

        public Criteria andIndexIsNull() {
            addCriterion("index is null");
            return (Criteria) this;
        }

        public Criteria andIndexIsNotNull() {
            addCriterion("index is not null");
            return (Criteria) this;
        }

        public Criteria andIndexEqualTo(Integer value) {
            addCriterion("index =", value, "index");
            return (Criteria) this;
        }

        public Criteria andIndexNotEqualTo(Integer value) {
            addCriterion("index <>", value, "index");
            return (Criteria) this;
        }

        public Criteria andIndexGreaterThan(Integer value) {
            addCriterion("index >", value, "index");
            return (Criteria) this;
        }

        public Criteria andIndexGreaterThanOrEqualTo(Integer value) {
            addCriterion("index >=", value, "index");
            return (Criteria) this;
        }

        public Criteria andIndexLessThan(Integer value) {
            addCriterion("index <", value, "index");
            return (Criteria) this;
        }

        public Criteria andIndexLessThanOrEqualTo(Integer value) {
            addCriterion("index <=", value, "index");
            return (Criteria) this;
        }

        public Criteria andIndexIn(List<Integer> values) {
            addCriterion("index in", values, "index");
            return (Criteria) this;
        }

        public Criteria andIndexNotIn(List<Integer> values) {
            addCriterion("index not in", values, "index");
            return (Criteria) this;
        }

        public Criteria andIndexBetween(Integer value1, Integer value2) {
            addCriterion("index between", value1, value2, "index");
            return (Criteria) this;
        }

        public Criteria andIndexNotBetween(Integer value1, Integer value2) {
            addCriterion("index not between", value1, value2, "index");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table activity_template_highlight
     *
     * @mbg.generated do_not_delete_during_merge Fri Jun 27 18:17:53 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table activity_template_highlight
     *
     * @mbg.generated Fri Jun 27 18:17:53 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}