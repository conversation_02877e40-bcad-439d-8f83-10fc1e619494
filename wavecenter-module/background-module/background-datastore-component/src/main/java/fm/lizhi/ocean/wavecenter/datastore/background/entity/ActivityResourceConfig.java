package fm.lizhi.ocean.wavecenter.datastore.background.entity;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动资源配置表
 *
 * @date 2024-10-11 04:22:39
 */
@Table(name = "`activity_resource_config`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityResourceConfig {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 资源名称
     */
    @Column(name= "`name`")
    private String name;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 资源code，只有自动配置的资源有
     */
    @Column(name= "`resource_code`")
    private String resourceCode;

    /**
     * 资源介绍
     */
    @Column(name= "`introduction`")
    private String introduction;

    /**
     * 资源图片，斜杠开头
     */
    @Column(name= "`image_url`")
    private String imageUrl;

    /**
     * 资源配置类型，1：自动，2：手动
     */
    @Column(name= "`deploy_type`")
    private Integer deployType;

    /**
     * 状态，0：禁用，1：启用
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 是否必选
     */
    @Column(name= "`required`")
    private Boolean required;

    /**
     * 环境隔离 TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 是否删除
     */
    @Column(name= "`deleted`")
    private Integer deleted;

    /**
     * 操作者
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", appId=").append(appId);
        sb.append(", resourceCode=").append(resourceCode);
        sb.append(", introduction=").append(introduction);
        sb.append(", imageUrl=").append(imageUrl);
        sb.append(", deployType=").append(deployType);
        sb.append(", status=").append(status);
        sb.append(", required=").append(required);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", deleted=").append(deleted);
        sb.append(", operator=").append(operator);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}