package fm.lizhi.ocean.wavecenter.datastore.background.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 活动模板流程表
 *
 * @date 2024-10-17 05:35:35
 */
@Table(name = "`activity_template_process`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityTemplateProcess {
    /**
     * 活动模板环节ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 活动模板ID
     */
    @Column(name= "`template_id`")
    private Long templateId;

    /**
     * 环节
     */
    @Column(name= "`name`")
    private String name;

    /**
     * 时长
     */
    @Column(name= "`duration`")
    private String duration;

    /**
     * 说明
     */
    @Column(name= "`explanation`")
    private String explanation;

    /**
     * 排序
     */
    @Column(name= "`index`")
    private Integer index;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", templateId=").append(templateId);
        sb.append(", name=").append(name);
        sb.append(", duration=").append(duration);
        sb.append(", explanation=").append(explanation);
        sb.append(", index=").append(index);
        sb.append("]");
        return sb.toString();
    }
}