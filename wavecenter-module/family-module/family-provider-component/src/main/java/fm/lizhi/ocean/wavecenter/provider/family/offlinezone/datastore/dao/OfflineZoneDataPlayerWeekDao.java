package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataPlayerWeek;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataPlayerWeekExample;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneDataPlayerWeekMapper;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.GetPlayerDataListParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 线下专区-主播明细-周 Dao
 * <AUTHOR>
 */
@Component
@Slf4j
public class OfflineZoneDataPlayerWeekDao {

    @Autowired
    private OfflineZoneDataPlayerWeekMapper offlineZoneDataPlayerWeekMapper;

    /**
     * 根据条件查询主播数据列表（支持分页和排序）
     *
     * @param queryDTO 查询DTO
     * @return 分页主播数据列表
     */
    public PageList<OfflineZoneDataPlayerWeek> getPlayerDataList(GetPlayerDataListParam queryDTO) {
        // 参数校验和默认值设置
        int pageNumber = queryDTO.getPageNo() != null && queryDTO.getPageNo() > 0 ? queryDTO.getPageNo() : 1;
        int pageSize = queryDTO.getPageSize() != null && queryDTO.getPageSize() > 0 ? queryDTO.getPageSize() : 20;

        // 限制页大小
        if (pageSize > 100) {
            pageSize = 100;
        }

        OfflineZoneDataPlayerWeekExample example = new OfflineZoneDataPlayerWeekExample();
        OfflineZoneDataPlayerWeekExample.Criteria criteria = example.createCriteria();

        // 必须条件
        criteria.andAppIdEqualTo(queryDTO.getAppId());

        // 可选条件
        if (queryDTO.getPlayerId() != null && queryDTO.getPlayerId() > 0) {
            Long playerId = queryDTO.getPlayerId();
            criteria.andUserIdEqualTo(playerId);
        }

//        if (StringUtils.hasText(queryDTO.getPlayerName())) {
//            criteria.andUserNameLike("%" + queryDTO.getPlayerName() + "%");
//        }

        if (queryDTO.getCategory() != null) {
            criteria.andCategoryEqualTo(queryDTO.getCategory());
        }

        if (StringUtils.hasText(queryDTO.getProvince())) {
            criteria.andProvinceEqualTo(queryDTO.getProvince());
        }

        if (StringUtils.hasText(queryDTO.getCity())) {
            criteria.andCityEqualTo(queryDTO.getCity());
        }

        if (queryDTO.getNjId() != null) {
            criteria.andNjIdEqualTo(queryDTO.getNjId());
        }

        if (queryDTO.getFamilyId() != null) {
            criteria.andFamilyIdEqualTo(queryDTO.getFamilyId());
        }

        // 日期范围条件
        if (StringUtils.hasText(queryDTO.getStartDate())) {
            Date start = DateUtil.formatStrToDate(queryDTO.getStartDate(), DateUtil.date_2);
            criteria.andStartWeekDateGreaterThanOrEqualTo(start);
        }

        if (StringUtils.hasText(queryDTO.getEndDate())) {
            Date end = DateUtil.formatStrToDate(queryDTO.getEndDate(), DateUtil.date_2);
            criteria.andEndWeekDateLessThanOrEqualTo(end);
        }

        // 排序条件
        String orderByClause = buildOrderByClause(queryDTO.getOrderMetrics(), queryDTO.getOrderType());
        if (StringUtils.hasText(orderByClause)) {
            example.setOrderByClause(orderByClause);
        } else {
            // 默认排序
            example.setOrderByClause("start_week_date DESC");
        }

        return offlineZoneDataPlayerWeekMapper.pageByExample(example, pageNumber, pageSize);
    }

    /**
     * 构建排序子句
     *
     * @param orderMetrics 排序字段
     * @param orderType    排序类型
     * @return 排序子句
     */
    private String buildOrderByClause(String orderMetrics, String orderType) {
        return buildOrderByClauseWithPrefix(orderMetrics, orderType, null);
    }

    /**
     * 构建排序子句（支持表别名前缀）
     *
     * @param orderMetrics 排序字段
     * @param orderType    排序类型
     * @param tablePrefix  表别名前缀（如"player."）
     * @return 排序子句
     */
    public String buildOrderByClauseWithPrefix(String orderMetrics, String orderType, String tablePrefix) {
        // 排序类型，默认为 DESC
        String order = "ASC".equalsIgnoreCase(orderType) ? "ASC" : "DESC";

        if (!StringUtils.hasText(orderMetrics)) {
            return Optional.ofNullable(tablePrefix).orElse("") + "income" + " " + order;
        }


        // 支持的排序字段映射
        String columnName;
        switch (orderMetrics) {
            case "income":
                columnName = "income";
                break;
            default:
                log.warn("不支持的排序字段: {}", orderMetrics);
                return null;
        }

        // 添加表别名前缀
        if (StringUtils.hasText(tablePrefix)) {
            columnName = tablePrefix + columnName;
        }

        return columnName + " " + order;
    }

    /**
     * 获取最新的一条数据
     */
    public OfflineZoneDataPlayerWeek getLatestPlayerData(int appId, Long familyId, Long njId, Long playerId) {

        OfflineZoneDataPlayerWeekExample example = new OfflineZoneDataPlayerWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andFamilyIdEqualTo(familyId)
                .andNjIdEqualTo(njId)
                .andUserIdEqualTo(playerId)
                .andStartWeekDateEqualTo(MyDateUtil.getLastWeekStartDay())
        ;
        example.setOrderByClause("start_week_date DESC");

        List<OfflineZoneDataPlayerWeek> list = offlineZoneDataPlayerWeekMapper.selectByExample(example);
        if (list.isEmpty()) {
            return null;
        }
        return list.get(0);
    }

    /**
     * 获取上周主播数据列表（用于统计行政区划）
     *
     * @param appId 应用ID
     * @return 上周主播数据列表
     */
    public List<OfflineZoneDataPlayerWeek> getLastWeekPlayerData(Integer appId) {
        Date lastWeekStartDay = MyDateUtil.getLastWeekStartDay();
        Date lastWeekEndDay = MyDateUtil.getLastWeekEndDay();

        OfflineZoneDataPlayerWeekExample example = new OfflineZoneDataPlayerWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andStartWeekDateEqualTo(lastWeekStartDay)
                .andEndWeekDateEqualTo(lastWeekEndDay);

        return offlineZoneDataPlayerWeekMapper.selectByExample(example);
    }
}
