package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.service;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZoneRoomDataBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestGetRoomDataList;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneRoomDataService;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert.OfflineZoneRoomDataConvert;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.GetRoomDataListParam;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager.OfflineZoneRoomDataManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * 线下专区厅数据服务实现
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class OfflineZoneRoomDataServiceImpl implements OfflineZoneRoomDataService {

    @Autowired
    private OfflineZoneRoomDataManager offlineZoneRoomDataManager;

    /**
     * 获取厅数据列表
     *
     * @param request 请求参数
     * @return 分页厅数据列表
     */
    @Override
    public Result<PageBean<OfflineZoneRoomDataBean>> getRoomDataList( RequestGetRoomDataList request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));

        // 参数校验
        if (request.getAppId() == null) {
            return RpcResult.fail(OfflineZoneRoomDataService.GET_ROOM_DATA_LIST_PARAM_ERROR);
        }

        GetRoomDataListParam param = OfflineZoneRoomDataConvert.INSTANCE.requestToDTO(request);
        PageBean<OfflineZoneRoomDataBean> pageBean = offlineZoneRoomDataManager.getRoomDataList(param);
        return RpcResult.success(pageBean);
    }
}
