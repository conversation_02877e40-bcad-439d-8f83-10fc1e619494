package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert;

import fm.lizhi.ocean.wavecenter.base.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneAdPosition;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListAdPositionBean;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 线下专区广告展位转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR,
        uses = {
                CommonConvert.class,
        }
)
public abstract class OfflineZoneAdPositionConvert {

    @Autowired
    protected CommonConfig commonConfig;

    /**
     * 转换为按类型列出广告展位的bean列表
     *
     * @param entities 广告展位实体列表
     * @return 按类型列出的广告展位bean列表
     */
    public abstract List<ListAdPositionBean> toListAdPositionBeans(List<OfflineZoneAdPosition> entities);

    @Mapping(target = "banner", source = "entity.banner", qualifiedByName = "addWaveCdnHost")
    protected abstract ListAdPositionBean toListAdPositionBean(OfflineZoneAdPosition entity);

    @Named("addWaveCdnHost")
    protected String addWaveCdnHost(String path) {
        return UrlUtils.addHostOrEmpty(path, commonConfig.getRomeFsDownloadCdn());
    }
}
