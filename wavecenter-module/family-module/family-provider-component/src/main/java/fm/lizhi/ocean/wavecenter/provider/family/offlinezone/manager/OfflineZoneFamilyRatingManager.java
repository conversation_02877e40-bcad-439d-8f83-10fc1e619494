package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneFamilyRating;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelConfig;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.SimpleLevelBean;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.config.OfflineZoneFamilyConfig;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert.OfflineZoneLevelConfigConvert;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneFamilyRatingDao;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneLevelConfigDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class OfflineZoneFamilyRatingManager {

    @Autowired
    private OfflineZoneFamilyRatingDao offlineZoneFamilyRatingDao;

    @Autowired
    private OfflineZoneFamilyConfig offlineZoneFamilyConfig;

    @Autowired
    private OfflineZoneLevelConfigDao offlineZoneLevelConfigDao;

    @Autowired
    private OfflineZoneLevelConfigConvert offlineZoneLevelConfigConvert;


    /**
     * 获取公会评级信息 - 带默认值
     * @param appId
     * @param familyId
     * @return
     */
    public Result<SimpleLevelBean> getFamilyLevelWithDefault(int appId, Long familyId) {

        OfflineZoneFamilyRating familyLevel = offlineZoneFamilyRatingDao.getFamilyLevel(appId, familyId);
        OfflineZoneLevelConfig levelConfig;
        if (familyLevel == null) {
            String defaultLevelKey = offlineZoneFamilyConfig.getDefaultLevelKey();
            levelConfig = offlineZoneLevelConfigDao.getLevelConfigByLevelKey(appId, defaultLevelKey);
        }else {
            levelConfig = offlineZoneLevelConfigDao.getLevelConfigByLevelId(appId, familyLevel.getLevelId());
        }

        SimpleLevelBean simpleLevelBean = offlineZoneLevelConfigConvert.convertToSimpleLevelBean(levelConfig);
        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, simpleLevelBean);
    }
}
