package fm.lizhi.ocean.wavecenter.provider.family.report.listener;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.common.validation.BeanValidator;
import fm.lizhi.ocean.wavecenter.common.validation.ValidateResult;
import fm.lizhi.ocean.wavecenter.datastore.family.report.entity.ReportPlayerApplyRecord;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.JudgeReason;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.ReportTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Room;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.Operate;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.ReportRoomOperateItem;
import fm.lizhi.ocean.wavecenter.domain.family.report.event.RoomFinishOperateEvent;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.OperateCommand;
import fm.lizhi.ocean.wavecenter.provider.family.report.config.PlayerReportConfig;
import fm.lizhi.ocean.wavecenter.provider.family.report.convert.PlayerReportApplyInfoConvert;
import fm.lizhi.ocean.wavecenter.provider.family.report.datastore.dao.ReportApplyDao;
import fm.lizhi.ocean.wavecenter.provider.family.report.kafka.message.ReportSyncAuditPunishMessage;
import fm.lizhi.ocean.wavecenter.provider.family.report.kafka.producer.ReportKafkaProducer;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 举报-完成厅惩罚
 */
@Component
@Slf4j
public class ReportRoomFinishOperateEventListener implements ApplicationListener<RoomFinishOperateEvent> {

    @Autowired
    private BeanValidator beanValidator;
    @Autowired
    private ReportKafkaProducer reportKafkaProducer;
    @Autowired
    private PlayerReportConfig playerReportConfig;
    @Autowired
    private PlayerReportApplyInfoConvert convert;
    @Autowired
    private ReportApplyDao reportApplyDao;
    @Autowired
    private OperateCommand command;

    @Override
    public void onApplicationEvent(@NotNull RoomFinishOperateEvent event) {
        Long applyId = event.getApplyId();
        Integer appId = event.getAppId();
        try {
            log.info("ReportRoomFinishOperateEventListener, applyId={};appId={}", applyId, appId);
            ValidateResult validateResult = beanValidator.validate(event);
            if (validateResult.isInvalid()) {
                log.error("ReportRoomFinishOperateEventListener, event invalid, event={}, validateResult={}", event, validateResult);
                return;
            }
            ReportPlayerApplyRecord record = reportApplyDao.selectReportPlayerApplyRecordById(applyId);
            if(record == null) {
                log.info("ReportRoomFinishOperateEventListener, applyId={} not exist", applyId);
                return;
            }

            ReportTypeEnum reportType = ReportTypeEnum.findType(record.getReportType());
            ReportRoomOperateItem operateItem = event.getOperateItem();
            Integer type = playerReportConfig.getBizConfig(record.getAppId()).getSyncAuditType();
            BusinessEvnEnum appEnum = BusinessEvnEnum.from(record.getAppId());
            Room accusedRoom = operateItem.getOperatedRoom();
            //厅处罚
            if (operateItem.isSuccess()) {
                Operate operate = operateItem.getOperate();
                ReportSyncAuditPunishMessage roomMsg = convert.toReportSyncAuditPunishMessage(record, reportType, operate, type, appEnum, "14",
                        accusedRoom.getId().toString());
                //同步审核
                reportKafkaProducer.sendReportSyncAuditPunishMessage(roomMsg);

                //发私信
                Integer operateMin = operate.getOperateDuration();
                Date operateInvalidTime = operateItem.calOperateInvalidTime();
                String accusedRoomMessage = operateItem.isPermanent() ?
                        JudgeReason.buildReason(accusedRoom, JudgeReason.ORIGINAL_ACCOUNT_REPORT_RESULT_FORVER_BANNED_TO_ACCUSED_ROOM_WITH_HALL_BAN) :
                        JudgeReason.buildReason(accusedRoom, operateMin, operateInvalidTime, JudgeReason.ORIGINAL_ACCOUNT_REPORT_RESULT_BANNED_TO_ACCUSED_ROOM_WITH_HALL_BAN);
                String familyLeaderMessage = operateItem.isPermanent() ?
                        JudgeReason.buildReason(accusedRoom, JudgeReason.ROOM_REPORT_RESULT_FORVER_BANNED_TO_ACCUSED_FAMILY) :
                        JudgeReason.buildReason(accusedRoom, operateMin, operateInvalidTime, JudgeReason.ROOM_BANNED_TO_ACCUSED_FAMILY);
                //私信挖槽厅主家族长+web站系统消息
                command.sendFamilyMessage(appId, accusedRoom.getFamilyId(), "违规通知", familyLeaderMessage);
                //私信挖槽厅主+web站系统消息
                command.sendUserMessage(appId, accusedRoom.getId(), accusedRoomMessage);
                command.sendWebSystemMessage(appId, accusedRoom.getId(), "违规通知", accusedRoomMessage);
            }
        } catch (RuntimeException e) {
            log.error("MultiAccountPlayerReportApplyPunishedEventListener failed, applyId={};appId={}", applyId, appId, e);
        }
    }
}
