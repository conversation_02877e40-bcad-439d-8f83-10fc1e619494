package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert;

import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataPlayerWeek;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZonePlayerDataBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestGetPlayerDataList;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.GetPlayerDataListParam;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.OfflineZonePlayerDataWithProtectionDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 线下专区主播数据转换器
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface OfflineZonePlayerDataConvert {

    OfflineZonePlayerDataConvert INSTANCE = Mappers.getMapper(OfflineZonePlayerDataConvert.class);

    /**
     * Request 转换为 DTO
     *
     * @param request 请求对象
     * @return DTO对象
     */
    GetPlayerDataListParam requestToDTO(RequestGetPlayerDataList request);

    /**
     * Entity 转换为 Bean
     *
     * @param entity 实体对象
     * @return Bean对象
     */
    @Mapping(target = "playerInfo", ignore = true)
    @Mapping(target = "njInfo", ignore = true)
    @Mapping(target = "protection", ignore = true)
    @Mapping(target = "protectionStatus", ignore = true)
    OfflineZonePlayerDataBean entityToBean(OfflineZoneDataPlayerWeek entity);

    /**
     * Entity 列表转换为 Bean 列表
     *
     * @param entities 实体列表
     * @return Bean列表
     */
    List<OfflineZonePlayerDataBean> entityListToBeanList(List<OfflineZoneDataPlayerWeek> entities);

    /**
     * 带保护状态的DTO转换为Bean
     *
     * @param dto 带保护状态的DTO
     * @return Bean对象
     */
    @Mapping(target = "playerInfo", ignore = true)
    @Mapping(target = "njInfo", ignore = true)
    @Mapping(target = "protection", source = "isProtected")
    @Mapping(target = "protectionStatus", source = "protectionStatus")
    OfflineZonePlayerDataBean withProtectionDTOToBean(OfflineZonePlayerDataWithProtectionDTO dto);

    /**
     * 带保护状态的DTO列表转换为Bean列表
     *
     * @param dtoList 带保护状态的DTO列表
     * @return Bean列表
     */
    List<OfflineZonePlayerDataBean> withProtectionDTOListToBeanList(List<OfflineZonePlayerDataWithProtectionDTO> dtoList);

}
