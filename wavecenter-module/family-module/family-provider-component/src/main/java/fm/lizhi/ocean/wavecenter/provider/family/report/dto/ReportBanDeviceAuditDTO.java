package fm.lizhi.ocean.wavecenter.provider.family.report.dto;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 审核Kafka消息结构
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportBanDeviceAuditDTO {

    private BusinessEvnEnum appEnv;

    /**
     * 一次调用过程的唯一标识
     */
    private String transactionId;

    /**
     * 名单类型 (1-白名单, 2-黑名单, 3-解封黑名单)
     */
    private Integer color;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 封禁时长(秒)，-1:表示永久 0 :默认值
     */
    private Integer duration;
    /**
     * 操作者 (system_auto-系统自动, admin_001-管理员等)
     */
    private String operator;
    /**
     * 备注
     */
    private String remark;
}
