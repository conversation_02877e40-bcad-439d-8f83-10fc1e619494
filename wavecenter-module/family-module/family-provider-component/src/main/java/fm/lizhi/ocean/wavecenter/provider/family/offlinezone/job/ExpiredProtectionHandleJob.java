package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.job;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtection;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtectionExample;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneProtectionMapper;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager.OfflineZoneProtectionManager;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestPlayerHandleAgreement;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.ProtectionAgreeStatusEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 定时处理逾期未确认跳槽保护Job
 * 
 * 功能说明：
 * 1. 分页查询 player_agree = -1 && archived = 0 的数据
 * 2. 调用 getProtectionValidityDate 获取有效时间
 * 3. 判断当前时间是否超过有效时间
 * 4. 如果超过，调用 playerHandleProtection 方法，设置为拒绝状态
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ExpiredProtectionHandleJob implements JobHandler {

    @Autowired
    private OfflineZoneProtectionMapper offlineZoneProtectionMapper;

    @Autowired
    private OfflineZoneProtectionManager offlineZoneProtectionManager;

    /**
     * 分页大小
     */
    private static final int PAGE_SIZE = 100;

    @Override
    public void execute(JobExecuteContext context) {
        log.info("开始执行定时处理逾期未确认跳槽保护Job");
        
        long startTime = System.currentTimeMillis();
        AtomicInteger totalCount = new AtomicInteger(0);
        AtomicInteger processedCount = new AtomicInteger(0);
        AtomicInteger expiredCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        
        try {
            // 分页处理逾期未确认的跳槽保护数据
            processExpiredProtections(totalCount, processedCount, expiredCount, errorCount);
            
        } catch (Exception e) {
            log.error("定时处理逾期未确认跳槽保护Job执行异常", e);
            throw e;
        } finally {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.info("定时处理逾期未确认跳槽保护Job执行完成，" +
                    "总查询数据: {}, 处理数据: {}, 逾期处理: {}, 异常数据: {}, 耗时: {}ms",
                    totalCount.get(), processedCount.get(), expiredCount.get(), errorCount.get(), duration);
        }
    }

    /**
     * 分页处理逾期未确认的跳槽保护数据
     */
    private void processExpiredProtections(AtomicInteger totalCount, AtomicInteger processedCount, 
                                         AtomicInteger expiredCount, AtomicInteger errorCount) {
        int pageNumber = 1;
         PageList<OfflineZoneProtection> pageList = new PageList<>();
        
        do {
            try {
                // 构建查询条件：player_agree = -1 && archived = 0
                OfflineZoneProtectionExample example = new OfflineZoneProtectionExample();
                example.createCriteria()
                        .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name())
                        .andPlayerAgreeEqualTo(ProtectionAgreeStatusEnums.NOT_PROCESSED.getCode())
                        .andArchivedEqualTo(false);
                
                // 分页查询
                pageList = offlineZoneProtectionMapper.pageByExample(example, pageNumber, PAGE_SIZE);
                
                if (!CollectionUtils.isEmpty(pageList)) {
                    totalCount.addAndGet(pageList.size());
                    
                    log.info("第{}页查询到{}条未确认跳槽保护数据", pageNumber, pageList.size());
                    
                    // 处理当前页数据
                    processCurrentPageData(pageList, processedCount, expiredCount, errorCount);
                }
                
                pageNumber++;
                
            } catch (Exception e) {
                log.error("分页查询第{}页数据异常", pageNumber, e);
                errorCount.incrementAndGet();
                pageNumber++;
            }
            
        } while (pageList.isHasNextPage());
    }

    /**
     * 处理当前页数据
     */
    private void processCurrentPageData(PageList<OfflineZoneProtection> protectionList,
                                      AtomicInteger processedCount, AtomicInteger expiredCount, 
                                      AtomicInteger errorCount) {
        for (OfflineZoneProtection protection : protectionList) {
            try {
                processedCount.incrementAndGet();
                // 设置一下环境变量
                ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.from(protection.getAppId()));


                // 获取协议有效期
                Date validityDate = offlineZoneProtectionManager.getProtectionValidityDate(
                        protection.getAppId(), protection.getFamilyId(), protection.getNjId(), protection.getPlayerId());
                
                // 判断是否超过有效期
                Date currentTime = new Date();
                if (validityDate != null && currentTime.after(validityDate)) {
                    // 超期处理：设置为拒绝状态
                    handleExpiredProtection(protection);
                    expiredCount.incrementAndGet();
                    
                    log.info("处理逾期跳槽保护，protectionId: {}, playerId: {}, validityDate: {}", 
                            protection.getId(), protection.getPlayerId(), validityDate);
                }
                
            } catch (Exception e) {
                log.error("处理跳槽保护数据异常，protectionId: {}, playerId: {}", 
                        protection.getId(), protection.getPlayerId(), e);
                errorCount.incrementAndGet();
            }finally {
                ContextUtils.clearContext();
            }
        }
    }

    /**
     * 处理逾期的跳槽保护
     */
    private void handleExpiredProtection(OfflineZoneProtection protection) {
        try {
            // 构建拒绝请求
            RequestPlayerHandleAgreement request = new RequestPlayerHandleAgreement();
            request.setProtectionId(protection.getId());
            request.setPlayerId(protection.getPlayerId());
            request.setAppId(protection.getAppId());
            request.setAgreeStatus(ProtectionAgreeStatusEnums.NOT_PROCESSED.getCode());
            request.setVerifyValidityDate(false);

            // 调用处理方法，设置为拒绝状态并归档
            offlineZoneProtectionManager.playerHandleProtection(request);
            
            log.debug("成功处理逾期跳槽保护，protectionId: {}, playerId: {}", 
                    protection.getId(), protection.getPlayerId());
                    
        } catch (Exception e) {
            log.error("处理逾期跳槽保护失败，protectionId: {}, playerId: {}", 
                    protection.getId(), protection.getPlayerId(), e);
            throw e;
        }
    }
}