package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.rediskey.OfflineZoneRedisKey;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.MapDataBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.RegionBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 离线区域数据Redis操作管理实现
 * <AUTHOR>
 */
@Component
public class OfflineZoneRedisManager {

    @Resource(name = "redis_ocean_wavecenter")
    private RedisClient redisClient;

    /**
     * 获取行政区划列表缓存
     *
     * @param appId 应用ID
     * @return 行政区划列表，如果缓存中无记录返回null
     */
    public List<RegionBean> getRegionList(Integer appId) {
        String key = OfflineZoneRedisKey.REGION_LIST_X.getKey(appId);
        String value = redisClient.get(key);
        if (value == null) {
            return null;
        }
        return JSON.parseObject(value, new TypeReference<List<RegionBean>>() {});
    }

    /**
     * 设置行政区划列表缓存
     *
     * @param appId         应用ID
     * @param regionList    行政区划列表
     * @param expireSeconds 过期时间（秒）
     */
    public void setRegionList(Integer appId, List<RegionBean> regionList, int expireSeconds) {
        String key = OfflineZoneRedisKey.REGION_LIST_X.getKey(appId);
        redisClient.setex(key, expireSeconds, JSON.toJSONString(regionList));
    }

    /**
     * 获取地图数据缓存
     *
     * @param appId    应用ID
     * @param familyId 家族ID
     * @return 地图数据列表，如果缓存中无记录返回null
     */
    public List<MapDataBean> getMapData(Integer appId, Long familyId) {
        String key = OfflineZoneRedisKey.MAP_DATA_X.getKey(appId, familyId);
        String value = redisClient.get(key);
        if (value == null) {
            return null;
        }
        return JSON.parseObject(value, new TypeReference<List<MapDataBean>>() {});
    }

    /**
     * 设置地图数据缓存
     *
     * @param appId         应用ID
     * @param familyId      家族ID
     * @param mapData       地图数据列表
     * @param expireSeconds 过期时间（秒）
     */
    public void setMapData(Integer appId, Long familyId, List<MapDataBean> mapData, int expireSeconds) {
        String key = OfflineZoneRedisKey.MAP_DATA_X.getKey(appId, familyId);
        redisClient.setex(key, expireSeconds, JSON.toJSONString(mapData));
    }
}