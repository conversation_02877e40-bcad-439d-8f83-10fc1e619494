package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRightRelation;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface OfflineZoneLevelRightRelationExtMapper {

    @Select("<script>\n" +
            "  SELECT * FROM `offline_zone_level_right_relation`\n" +
            "  WHERE `level_id` IN\n" +
            "    <foreach item=\"levelId\" collection=\"levelIds\" open=\"(\" separator=\",\" close=\")\">\n" +
            "      #{levelId}\n" +
            "    </foreach>\n" +
            "  ORDER BY `level_id` ASC, `index` ASC\n" +
            "</script>")
    List<OfflineZoneLevelRightRelation> selectByLevelIds(@Param("levelIds") List<Long> levelIds);
}
