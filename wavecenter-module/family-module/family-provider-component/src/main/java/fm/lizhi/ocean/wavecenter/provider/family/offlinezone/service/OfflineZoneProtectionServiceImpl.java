package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyAuthBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserFamilyService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.AgreementFileBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.FamilyInfoBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZoneProtectionWithStatusBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.PlayerInfoBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.*;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ProtectionDetailResponse;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ProtectionSupportInfoResponse;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneProtectionService;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.config.CommonOfflineZoneConfig;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.config.OfflineZoneFamilyConfig;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert.OfflineZoneProtectionConvert;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.OfflineZoneDataPlayerWeekDTO;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.OfflineZoneProtectionDTO;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.OfflineZoneProtectionWithStatusDTO;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager.OfflineZoneProtectionManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.GetUserVerifyResultDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.GetUserVerifyResultParamDTO;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserInfoDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserVerifyManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * 线下专区主播跳槽保护协议服务实现
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class OfflineZoneProtectionServiceImpl implements OfflineZoneProtectionService {

    @Autowired
    private OfflineZoneProtectionManager offlineZoneProtectionManager;

    @Autowired
    private OfflineZoneFamilyConfig offlineZoneFamilyConfig;

    @Autowired
    private UserFamilyService userFamilyService;

    @Autowired
    private UserVerifyManager userVerifyManager;

    @Autowired
    private UserManager userManager;



    @Override
    public Result<Void> submitAgreement(RequestSubmitAgreement request) {

        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));

        if (StrUtil.isEmpty(request.getAgreementFileJson())){
            return new Result<>(SUBMIT_AGREEMENT_PARAM_ERROR,  null);
        }

        // 校验是否在时效内
        Date validDate =
                offlineZoneProtectionManager.getProtectionValidityDate(request.getAppId(), request.getFamilyId(), request.getNjId(), request.getPlayerId());
        if (validDate == null || validDate.before(new Date())){
            log.info("协议已过期, familyId={}, njId={}, playerId={}, expiredTime={}",
                    request.getFamilyId(), request.getNjId(), request.getPlayerId(), validDate);
            return new Result<>(SUBMIT_AGREEMENT_EXPIRED,  null);
        }

        if (request.getId() == null){
            return offlineZoneProtectionManager.createProtection(request);
        }else {
            return offlineZoneProtectionManager.updateProtection(request);
        }
    }

    @Override
    public Result<Void> playerHandleAgreement(RequestPlayerHandleAgreement request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));

        return offlineZoneProtectionManager.playerHandleProtection(request);
    }

    @Override
    public Result<ProtectionDetailResponse> getDetail(Long id) {
        LogContext.addReqLog("id={}", id);
        LogContext.addResLog("id={}", id);

        if (id == null) {
            return new Result<>(GET_DETAIL_PARAM_ERROR, null);
        }

        OfflineZoneProtectionDTO protection = offlineZoneProtectionManager.getProtectionDetail(id);

        if (protection == null) {
            log.info("协议不存在, id={}", id);
            return new Result<>(GET_DETAIL_NOT_FOUND, null);
        }

        // 获取协议过期时间
        Date protectionValidityDate = offlineZoneProtectionManager.getProtectionValidityDate(protection.getAppId(), protection.getFamilyId(), protection.getNjId(), protection.getPlayerId());

        OfflineZoneProtectionWithStatusDTO protectionStatus = offlineZoneProtectionManager.getProtectionStatus(protection.getAppId(), protection.getFamilyId(), protection.getNjId(), protection.getPlayerId());

        ProtectionDetailResponse response = OfflineZoneProtectionConvert.INSTANCE.buildProtectionDetailResponse(protection, protectionValidityDate, protectionStatus);

        // 解析协议文件JSON
        if (StrUtil.isNotEmpty(protection.getAgreementFileJson())) {
            List<AgreementFileBean> agreementFiles = JSON.parseArray(protection.getAgreementFileJson(), AgreementFileBean.class);
            response.setAgreementFile(agreementFiles);
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, response);
    }

    @Override
    public Result<ProtectionSupportInfoResponse> getSupportInfo(RequestGetProtectionSupportInfo request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));

        Long playerId = request.getPlayerId();
        Long familyId = request.getFamilyId();
        Long njId = request.getNjId();
        PlayerInfoBean playerInfo;


        // 获取主播身份信息, 先查大数据的表
        OfflineZoneDataPlayerWeekDTO playerData = offlineZoneProtectionManager.getPlayerIdentityInfo(request.getAppId(), familyId, njId, playerId);
        Optional<UserInfoDto> userInfoDtoOptional = userManager.getUserInfoById(request.getPlayerId());
        if (playerData == null) {
            // 再查实名认证信息
            Result<GetUserVerifyResultDTO> userVerifyResult = userVerifyManager.getUserVerifyResult(new GetUserVerifyResultParamDTO()
                    .setUserId(request.getPlayerId())
                    .setAppId(request.getAppId())
            );

            if (RpcResult.isFail(userVerifyResult)){
                log.warn("getUserVerifyResult failed, rCode: {}, req: {}", userVerifyResult.rCode(), JsonUtils.toJsonString(request));
                return new Result<>(GET_SUPPORT_INFO_PLAYER_NOT_FOUND, null);
            }

            playerInfo = OfflineZoneProtectionConvert.INSTANCE.convertUserVerifyResultToPlayerInfoBean(userVerifyResult.target(), userInfoDtoOptional.orElse(null));
        }else {
            // 构建主播信息
            playerInfo = OfflineZoneProtectionConvert.INSTANCE.buildPlayerInfo(playerData, userInfoDtoOptional.orElse(null));
        }


        // 获取公会认证信息
        FamilyAuthBean familyAuth = getFamilyAuthInfo(request.getAppId(), familyId);
        FamilyInfoBean familyInfo = null;
        if (familyAuth != null) {
            // 构建公会信息
            familyInfo = OfflineZoneProtectionConvert.INSTANCE.buildFamilyInfo(familyAuth);
        }

        // 获取告示信息
        CommonOfflineZoneConfig bizConfig = offlineZoneFamilyConfig.getBizConfig(request.getAppId());
        String notice = bizConfig.getProtectionNotice();
        ProtectionSupportInfoResponse response = OfflineZoneProtectionConvert.INSTANCE.buildSupportInfoResponse(notice, playerInfo, familyInfo);

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, response);
    }

    /**
     * 批量查询主播的跳槽保护状态
     *
     * @param appId 应用ID
     * @param familyId 公会ID
     * @param njId 厅ID
     * @param playerIds 主播ID集合
     * @return 主播保护状态映射结果
     */
    @Override
    public Result<Map<Long, OfflineZoneProtectionWithStatusBean>> batchGetProtectionStatus(
            Integer appId, Long familyId, Long njId, Set<Long> playerIds) {
        LogContext.addReqLog("appId={}, familyId={}, njId={}, playerIds={}", appId, familyId, njId, playerIds);

        // 参数校验
        if (appId == null || familyId == null || njId == null || 
            playerIds == null || playerIds.isEmpty()) {
            return RpcResult.fail(OfflineZoneProtectionService.BATCH_GET_PROTECTION_STATUS_PARAM_ERROR, "参数不能为空");
        }

        // 调用Manager层方法
        Map<Long, OfflineZoneProtectionWithStatusDTO> dtoResult = 
            offlineZoneProtectionManager.batchGetProtectionStatus(appId, familyId, njId, playerIds);
        
        // 转换DTO为Bean
        Map<Long, OfflineZoneProtectionWithStatusBean> beanResult =
                OfflineZoneProtectionConvert.INSTANCE.protectionWithStatusDTOMapToBeanMap(dtoResult);

        LogContext.addResLog("result size={}", beanResult.size());
        return RpcResult.success(beanResult);
    }

    @Override
    public Result<Void> batchResetAgreementValidity(RequestBatchResetAgreementValidity request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));

        // 参数校验
        if (request == null || CollectionUtils.isEmpty(request.getPlayerList())) {
            return RpcResult.fail(OfflineZoneProtectionService.BATCH_RESET_AGREEMENT_VALIDITY_PARAM_ERROR, "参数不能为空");
        }

        Boolean success = offlineZoneProtectionManager.batchResetAgreementValidity(request);
        if (!success) {
            log.warn("批量重置协议有效期失败, request={}", JsonUtils.toJsonString(request));
            return RpcResult.fail(OfflineZoneProtectionService.BATCH_RESET_AGREEMENT_VALIDITY_FAIL, "重置失败");
        }

        return RpcResult.success();
    }

    @Override
    public Result<Void> addAgreementValidity(RequestAddAgreementValidity request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        
        if (request == null || request.getAppId() == null || request.getFamilyId() == null 
            || request.getNjId() == null || request.getPlayerId() == null 
            || request.getBeginSignTime() == null) {
            return new Result<>(ADD_AGREEMENT_VALIDITY_PARAM_ERROR, null);
        }
        
        return offlineZoneProtectionManager.addAgreementValidity(request);
    }

    /**
     * 获取公会认证信息
     * @param appId 应用ID
     * @param familyId 公会ID
     * @return 公会认证信息
     */
    private FamilyAuthBean getFamilyAuthInfo(int appId, long familyId) {
        Result<FamilyAuthBean> result = userFamilyService.getUserFamilyAuth(appId, familyId);
        if (result != null && result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS) {
            return result.target();
        }
        return null;
    }
}
