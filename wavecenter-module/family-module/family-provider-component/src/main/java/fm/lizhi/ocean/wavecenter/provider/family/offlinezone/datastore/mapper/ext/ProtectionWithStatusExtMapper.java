package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.common.datastore.mysql.constant.ParamContants;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.GetPlayerDataListParam;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.OfflineZonePlayerDataWithProtectionDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 线下专区主播保护状态扩展查询 Mapper
 *
 * <AUTHOR>
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface ProtectionWithStatusExtMapper {

    /**
     * 查询主播数据列表（包含保护状态）
     *
     * @param param 查询参数
     * @return 分页数据
     */
    @Select({
            "SELECT ",
            "    player.id,",
            "    player.app_id as appId,",
            "    player.start_week_date as startWeekDate,",
            "    player.end_week_date as endWeekDate,",
            "    player.user_id as userId,",
            "    player.user_name as userName,",
            "    player.id_card_number as idCardNumber,",
            "    player.id_name as idName,",
            "    player.family_id as familyId,",
            "    player.family_name as familyName,",
            "    player.nj_id as njId,",
            "    player.nj_name as njName,",
            "    player.begin_sign_time as beginSignTime,",
            "    player.category,",
            "    player.country,",
            "    player.province,",
            "    player.city,",
            "    player.income,",
            "    player.create_time as createTime,",
            "    player.modify_time as modifyTime,",
            "    protection.id as protectionId,",
            "    protection.player_agree as playerAgree,",
            "    protection.archived,",
            "    COALESCE(validity.expired_time, player.begin_sign_time) as expiredTime,",
            "    CASE ",
            "        WHEN COALESCE(validity.expired_time, player.begin_sign_time) <= NOW() AND (protection.id IS NULL OR protection.player_agree = 0) THEN 5 ",
            "        WHEN protection.id IS NULL THEN 0 ",
            "        WHEN protection.player_agree = -1 AND protection.archived = 0 THEN 1 ",
            "        WHEN protection.player_agree = -1 AND protection.archived = 1 THEN 2 ",
            "        WHEN protection.player_agree = 1 AND protection.archived = 1 THEN 3 ",
            "        WHEN protection.player_agree = 0 AND protection.archived = 1 THEN 4 ",
            "        ELSE 0 ",
            "    END as protectionStatus,",
            "    CASE ",
            "        WHEN protection.player_agree = 1 AND protection.archived = 1 THEN 1 ",
            "        ELSE 0 ",
            "    END as isProtected ",
            "FROM offline_zone_data_player_week player ",
            "LEFT JOIN offline_zone_protection protection ON protection.id = (",
            "    SELECT p1.id ",
            "    FROM offline_zone_protection p1 ",
            "    WHERE p1.player_id = player.user_id ",
            "        AND p1.nj_id = player.nj_id ",
            "        AND p1.family_id = player.family_id ",
            "        AND p1.app_id = player.app_id ",
            "        AND p1.deploy_env = #{entity.deployEnv} ",
            "    ORDER BY p1.create_time DESC ",
            "    LIMIT 1 ",
            ") ",
            "LEFT JOIN offline_zone_protection_agreement_validity validity ",
            "    ON player.user_id = validity.player_id ",
            "    AND player.nj_id = validity.nj_id ",
            "    AND player.family_id = validity.family_id ",
            "    AND player.app_id = validity.app_id ",
            "    AND validity.deploy_env = #{entity.deployEnv} ",
            "WHERE player.app_id = #{entity.appId} ",
            "    AND (#{entity.familyId} IS NULL OR player.family_id = #{entity.familyId}) ",
            "    AND (#{entity.njId} IS NULL OR player.nj_id = #{entity.njId}) ",
            "    AND (#{entity.playerId} IS NULL OR player.user_id = #{entity.playerId}) ",
            "    AND (#{entity.category} IS NULL OR player.category = #{entity.category}) ",
            "    AND (#{entity.province} IS NULL OR player.province = #{entity.province}) ",
            "    AND (#{entity.city} IS NULL OR player.city = #{entity.city}) ",
            "    AND (#{entity.startDate} IS NULL OR player.start_week_date >= #{entity.startDate}) ",
            "    AND (#{entity.endDate} IS NULL OR player.end_week_date <= #{entity.endDate}) ",
            "    AND (#{entity.protectionStatus} IS NULL OR ( ",
            "        CASE ",
            "            WHEN COALESCE(validity.expired_time, player.begin_sign_time) <= NOW() AND (protection.id IS NULL OR protection.player_agree = 0) THEN 5 ",
            "            WHEN protection.id IS NULL THEN 0 ",
            "            WHEN protection.player_agree = -1 AND protection.archived = 0 THEN 1 ",
            "            WHEN protection.player_agree = -1 AND protection.archived = 1 THEN 2 ",
            "            WHEN protection.player_agree = 1 AND protection.archived = 1 THEN 3 ",
            "            WHEN protection.player_agree = 0 AND protection.archived = 1 THEN 4 ",
            "            ELSE 0 ",
            "        END = #{entity.protectionStatus} ",
            "    )) ",
            "    AND (#{entity.protection} IS NULL OR ( ",
            "        CASE ",
            "            WHEN protection.player_agree = 1 AND protection.archived = 1 THEN 1 ",
            "            ELSE 0 ",
            "        END = #{entity.protection} ",
            "    )) ",
            "    ORDER BY ${orderByClause} "
    })
    PageList<OfflineZonePlayerDataWithProtectionDTO> selectPlayerDataWithProtection(
            @Param(ParamContants.PAGE_NUMBER) int pageNumber,
            @Param(ParamContants.PAGE_SIZE) int pageSize,
            @Param(ParamContants.ENTITY) GetPlayerDataListParam entity,
            @Param("orderByClause") String orderByClause);

}
