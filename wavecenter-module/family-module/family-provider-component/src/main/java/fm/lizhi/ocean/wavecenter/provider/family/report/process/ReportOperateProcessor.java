package fm.lizhi.ocean.wavecenter.provider.family.report.process;

import fm.lizhi.ocean.wavecenter.base.processor.BusinessEnvAwareProcessor;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.OperateObjTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.OperateTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Player;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Room;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.Operate;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.ReportPlayerOperateItem;

import java.util.ArrayList;
import java.util.List;

public interface ReportOperateProcessor extends BusinessEnvAwareProcessor {
    @Override
    default Class<? extends BusinessEnvAwareProcessor> getBaseBusinessProcessor() {
        return ReportOperateProcessor.class;
    }


    List<ReportPlayerOperateItem> createOperateAccountItems(Player subPlayer, Player mainPlayer, Room accusedRoom);
    default ReportPlayerOperateItem createOperateDeviceIdItem(long id, Player accusedPlayer, boolean auto) {
        Operate banDeviceId;
        if(auto) {
            banDeviceId = Operate.createAutoPersistentOperate(OperateTypeEnum.BAN, OperateObjTypeEnum.DEVICE_ID);
        } else {
            banDeviceId = Operate.createManualPersistentOperate(OperateTypeEnum.BAN, OperateObjTypeEnum.DEVICE_ID);
        }
        return ReportPlayerOperateItem.create(id, accusedPlayer, banDeviceId);
    }
}
