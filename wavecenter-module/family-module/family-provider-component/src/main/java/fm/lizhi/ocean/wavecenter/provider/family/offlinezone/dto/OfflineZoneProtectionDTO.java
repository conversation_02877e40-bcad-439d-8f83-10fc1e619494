package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 跳槽保护协议DTO
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class OfflineZoneProtectionDTO {
    
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 公会ID
     */
    private Long familyId;

    /**
     * 上传用户 ID
     */
    private Long uploadUserId;

    /**
     * 厅ID
     */
    private Long njId;

    /**
     * 主播ID
     */
    private Long playerId;

    /**
     * 协议生效开始时间
     */
    private Date agreementStartTime;

    /**
     * 协议生效结束时间
     */
    private Date agreementEndTime;

    /**
     * 协议更新时间
     */
    private Date agreementUpdateTime;

    /**
     * 是否盖章签字：0-否，1-是
     */
    private Boolean stampSign;

    /**
     * 主播同意状态：-1-未处理，0-不同意，1-同意
     */
    private Integer playerAgree;

    /**
     * 是否归档：0-否，1-是
     */
    private Boolean archived;

    /**
     * 环境：TEST/PRE/PRO
     */
    private String deployEnv;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 协议链接列表 格式: [{"url":"","md5":"","name":""}]
     */
    private String agreementFileJson;
}
