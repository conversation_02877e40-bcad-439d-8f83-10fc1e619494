package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager;

import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelConfig;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRightRelation;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListLevelConfigBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestListLevelConfig;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert.OfflineZoneLevelConfigConvert;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneLevelConfigDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListValuedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 线下专区等级配置管理器
 */
@Component
@Slf4j
public class OfflineZoneLevelConfigManager {

    @Autowired
    private OfflineZoneLevelConfigConvert offlineZoneLevelConfigConvert;

    @Autowired
    private OfflineZoneLevelConfigDao offlineZoneLevelConfigDao;

    /**
     * 列出所有等级配置
     *
     * @param request 请求参数
     * @return 等级配置列表
     */
    public List<ListLevelConfigBean> listLevelConfig(RequestListLevelConfig request) {
        Integer appId = request.getAppId();
        List<OfflineZoneLevelConfig> levelConfigs = offlineZoneLevelConfigDao.listLevelConfig(appId);
        List<Long> levelIds = levelConfigs.stream().map(OfflineZoneLevelConfig::getId).collect(Collectors.toList());
        ListValuedMap<Long, OfflineZoneLevelRightRelation> levelRightRelationsMap = offlineZoneLevelConfigDao.getLevelRightRelationsMapByLevelIds(levelIds);
        return offlineZoneLevelConfigConvert.toListLevelConfigBeans(levelConfigs, levelRightRelationsMap);
    }
}
