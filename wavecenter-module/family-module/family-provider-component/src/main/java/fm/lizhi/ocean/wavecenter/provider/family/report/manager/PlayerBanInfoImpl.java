package fm.lizhi.ocean.wavecenter.provider.family.report.manager;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.report.entity.ReportPlayerApplyOperateResult;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.model.PlayerBanTarget;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.PlayerBanInfo;
import fm.lizhi.ocean.wavecenter.provider.family.report.constant.PlayerReportConstant;
import fm.lizhi.ocean.wavecenter.provider.family.report.datastore.mapper.ReportPlayerApplyRecordExtMapper;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserBanDetailBean;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import fm.lizhi.xm.security.constant.KafkaBanTypeEnum;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
public class PlayerBanInfoImpl implements PlayerBanInfo {


    private final UserManager userManager;
    private final ReportPlayerApplyRecordExtMapper extMapper;

    private PlayerBanTarget result;

    public PlayerBanInfoImpl(UserManager userManager, ReportPlayerApplyRecordExtMapper extMapper) {
        this.userManager = userManager;
        this.extMapper = extMapper;
    }
    @Override
    public PlayerBanTarget getPlayerBanInfo(Integer appId, Long playerId) {
        if(result != null) {
            return result;
        }
        result = new PlayerBanTarget();
        UserBanDetailBean userBanDetail = userManager.getUserBanDetail(playerId);
        result.setBan(userBanDetail.isBan());
        if(userBanDetail.getSubType() != null) {
            result.setBanByWaveReport(userBanDetail.getSubType() == KafkaBanTypeEnum.SUB_TYPE_JOB_HOP.getType());
        }
        if(userBanDetail.getReason() != null) {
            result.setBanByWaveReport(PlayerReportConstant.PLAYER_REPORT_SUBMIT_AUDIT_REASON.equals(userBanDetail.getReason()));
        }
        ReportPlayerApplyOperateResult reportPlayerApplyRecord =
                extMapper.queryReportPlayerApplyOperateResult(appId, playerId, ConfigUtils.getEnvRequired().name());
        log.info("getPlayerBanInfo appId={}, playerId={}, reportPlayerApplyRecord={}", appId, playerId, JsonUtils.toJsonString(reportPlayerApplyRecord));
        result.setExistReportRecord(reportPlayerApplyRecord != null && reportPlayerApplyRecord.getOperateDuration() == -1);
        log.info("getPlayerBanInfo appId={}, playerId={}, result={}", appId, playerId, result);
        return result;
    }
}
