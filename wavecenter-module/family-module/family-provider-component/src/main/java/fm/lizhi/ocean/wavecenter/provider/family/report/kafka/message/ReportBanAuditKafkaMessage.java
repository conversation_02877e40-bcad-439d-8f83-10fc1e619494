package fm.lizhi.ocean.wavecenter.provider.family.report.kafka.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 审核Kafka消息结构
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportBanAuditKafkaMessage {

    /**
     * 应用名称，从指定URL中获取
     */
    private String appName;

    /**
     * 审核系统为调用方分配的标识符
     */
    private String source;

    /**
     * 操作标识，参考审核系统枚举类 fm.lizhi.content.review.constant.OperationMarkEnum
     */
    private Integer op;

    /**
     * 一次调用过程的唯一标识，由调用方生成
     */
    private String requestId;

    /**
     * 操作者标识
     */
    private String opUser;

    /**
     * 触发时间戳，毫秒值
     */
    private Long modifyTime;

    /**
     * 操作原因
     */
    private String reason;

    /**
     * 备注，100个字符以内
     */
    private String remarks;

    /**
     * 封禁效果失效的时间戳，毫秒值，永久封禁传0
     */
    private Long invalidTimestamp;

    /**
     * 附加信息，请和审核开发同事确认传参后使用
     */
    private String extra;

    /**
     * 操作的目标用户id
     */
    private Long userId;

    /**
     * 直播业务类型，参考审核系统枚举类
     */
    private Integer liveType;

    /**
     * 直播id
     */
    private Long liveId;

    /**
     * 直播间id
     */
    private Long liveRoomId;
}
