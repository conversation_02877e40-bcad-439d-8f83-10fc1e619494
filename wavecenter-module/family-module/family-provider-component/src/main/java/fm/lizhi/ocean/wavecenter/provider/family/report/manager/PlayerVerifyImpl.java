package fm.lizhi.ocean.wavecenter.provider.family.report.manager;

import fm.lizhi.ocean.wavecenter.domain.family.report.repository.model.VerifyInfoTarget;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.PlayerVerify;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserVerifyDataDTO;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class PlayerVerifyImpl implements PlayerVerify {


    @Autowired
    private UserManager userManager;

    @Override
    public Optional<VerifyInfoTarget> getVerifyInfo(Long playerId) {
        Optional<UserVerifyDataDTO> verifyData = userManager.getVerifyData(playerId);
        return verifyData.map(userVerifyDataDTO ->
                new VerifyInfoTarget(userVerifyDataDTO.getName(), userVerifyDataDTO.getIdCardNumber()));
    }
}
