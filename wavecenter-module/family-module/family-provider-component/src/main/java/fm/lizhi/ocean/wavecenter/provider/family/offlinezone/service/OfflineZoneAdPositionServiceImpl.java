package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.service;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListAdPositionBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestListAdPosition;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneAdPositionService;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager.OfflineZoneAdPositionManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 线下专区广告展位服务实现
 */
@ServiceProvider
@Slf4j
public class OfflineZoneAdPositionServiceImpl implements OfflineZoneAdPositionService {

    @Autowired
    private OfflineZoneAdPositionManager offlineZoneAdPositionManager;

    @Override
    public Result<List<ListAdPositionBean>> listAdPosition(RequestListAdPosition request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        try {
            List<ListAdPositionBean> list = offlineZoneAdPositionManager.listAdPosition(request);
            LogContext.addResLog("listSize={}", list.size());
            if (log.isDebugEnabled()) {
                log.debug("listAdPosition, request={}, list={}", JsonUtils.toJsonString(request), JsonUtils.toJsonString(list));
            }
            return RpcResult.success(list);
        } catch (Exception e) {
            log.error("listAdPosition error, request={}", JsonUtils.toJsonString(request), e);
            return RpcResult.fail(CommonService.INTERNAL_ERROR, "系统异常，请稍后重试");
        }
    }
}
