package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert;

import fm.lizhi.ocean.wavecenter.base.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRight;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRightIntroduction;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListLevelRightBean;
import org.apache.commons.collections4.ListValuedMap;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

/**
 * 线下专区等级权益转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR,
        uses = {
                CommonConvert.class,
        }
)
public abstract class OfflineZoneLevelRightConvert {

    @Autowired
    protected CommonConfig commonConfig;

    public abstract List<ListLevelRightBean> toListLevelRightBeans(List<OfflineZoneLevelRight> entities, @Context ListValuedMap<Long, OfflineZoneLevelRightIntroduction> levelRightIntroductionsMap);

    @Mapping(target = "image", source = "entity.image", qualifiedByName = "addWaveCdnHost")
    @Mapping(target = "introductions", source = "entity.id", qualifiedByName = "toListLevelRightBeanIntroductions")
    protected abstract ListLevelRightBean toListLevelRightBean(OfflineZoneLevelRight entity, @Context ListValuedMap<Long, OfflineZoneLevelRightIntroduction> levelRightIntroductionsMap);

    @Named("addWaveCdnHost")
    protected String addWaveCdnHost(String path) {
        return UrlUtils.addHostOrEmpty(path, commonConfig.getRomeFsDownloadCdn());
    }

    @Named("toListLevelRightBeanIntroductions")
    protected List<ListLevelRightBean.Introduction> toListLevelRightBeanIntroductions(Long rightId, @Context ListValuedMap<Long, OfflineZoneLevelRightIntroduction> levelRightIntroductionsMap) {
        if (rightId == null || levelRightIntroductionsMap == null) {
            return Collections.emptyList();
        }
        List<OfflineZoneLevelRightIntroduction> introductionEntities = levelRightIntroductionsMap.get(rightId);
        return toListLevelRightBeanIntroductions(introductionEntities);
    }

    protected abstract List<ListLevelRightBean.Introduction> toListLevelRightBeanIntroductions(List<OfflineZoneLevelRightIntroduction> introductionEntities);
}
