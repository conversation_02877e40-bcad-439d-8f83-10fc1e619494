package fm.lizhi.ocean.wavecenter.provider.family.report.listener;

import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.common.validation.BeanValidator;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.ReportTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply.JudgeResult;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply.PlayerJobHopReportApply;
import fm.lizhi.ocean.wavecenter.domain.family.report.event.PlayerReportApplyFinishJudgeEvent;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.OperateCommand;
import fm.lizhi.ocean.wavecenter.provider.family.report.manager.PlayerReportApplyManager;
import fm.lizhi.ocean.wavecenter.provider.family.report.manager.PlayerReportRedisManager;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * 大小号判定结果监听器
 */
@Component
@Slf4j
public class ReportFinishJudgeEventListener implements ApplicationListener<PlayerReportApplyFinishJudgeEvent> {

    @Autowired
    private BeanValidator beanValidator;
    @Autowired
    private PlayerReportApplyManager playerReportApplyManager;
    @Autowired
    private PlayerReportRedisManager playerReportRedisManager;
    @Autowired
    private OperateCommand operateCommand;

    private String titleTemplate = "「%s」 举报结果";

    @Override
    public void onApplicationEvent(@NotNull PlayerReportApplyFinishJudgeEvent event) {
        try {
            log.info("PlayerReportJudgeEventListener, event={}", JsonUtils.toJsonString(event));
            PlayerJobHopReportApply apply = event.getApply();
            //通过判定
            if (apply.isPass()) {
                playerReportRedisManager.recordReporterPassApply(apply.getAppId(),
                        apply.getReportUser().getId(),
                        apply.getAccusedUser().getId(),
                        apply.getAccusedRoom().getId());
            } else {
                JudgeResult judgeResult = apply.getJudgeResult();
                ReportTypeEnum reportTypeEnum = apply.getReportType();
                String title = String.format(titleTemplate, reportTypeEnum.getDescription());
                operateCommand.sendWebSystemMessage(apply.getAppId(), apply.getReportUser().getId(), title, judgeResult.getReason());
                operateCommand.sendUserMessage(apply.getAppId(), apply.getReportUser().getId(), judgeResult.getReason());
            }
        } catch (RuntimeException e) {
            log.error("PlayerReportJudgeEventListener failed, event={}", event.getApply().getId(), e);
        }
    }
}
