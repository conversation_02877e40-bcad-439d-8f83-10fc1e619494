package fm.lizhi.ocean.wavecenter.provider.family.report.config;

import fm.lizhi.ocean.wavecenter.domain.family.report.constant.ReportTypeEnum;
import lombok.Data;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class XmPlayerReportConfig implements CommonPlayerReportConfig{

    /**
     * 跳槽厅白名单
     */
    private Set<Long> playerJobHoppingRoomWhiteList = new HashSet<>();
    private Set<Long> playerJobHoppingFamilyWhiteList = new HashSet<>();

    private Set<Long> accusedRoomBlackList = new HashSet<>();
    private Set<Long> accusedFamilyBlackList = new HashSet<>();

    /**
     * 获取跳槽违规榜单发送的飞书webhook
     */
    private String accusedRankWebHook = "";

    /**
     * 跳槽违规次数榜单的web url
     */
    private String punishCountRankWebUrl = "";

    /**
     * 跳槽违规收入榜单的web url
     */
    private String punishIncomeRankWebUrl = "";

    /**
     * 发送私信的官方用户id
     */
    private Long sendMessageOfficialUserId;

    /**
     * 大小号-违规收入对比数值
     * @return
     */
    private Integer reportViolationIncome = Integer.MAX_VALUE;
    private Integer originJobHopReportViolationIncome = Integer.MAX_VALUE;

    private String roomGradientOperateRuleListJson = "[{\"sequence\":1,\"operate\":{\"operateType\":\"BAN\",\"operateObjType\":\"ROOM_OPEN_LIVE\",\"operateDuration\":1440,\"manual\":true}},{\"sequence\":2,\"operate\":{\"operateType\":\"BAN\",\"operateObjType\":\"ROOM_OPEN_LIVE\",\"operateDuration\":4320,\"manual\":true}},{\"sequence\":3,\"operate\":{\"operateType\":\"BAN\",\"operateObjType\":\"ROOM_OPEN_LIVE\",\"operateDuration\":-1,\"manual\":true}}]";

    private Integer syncAuditType = 1510043;

    private Long roomOperateConditionCount = 3L;

    /**
     * 是否自动封禁设备
     */
    private boolean autoBanDeviceId = false;

    private Set<Integer> supportReportType = new HashSet<>(ReportTypeEnum.JOB_HOPPING.getCode());
}
