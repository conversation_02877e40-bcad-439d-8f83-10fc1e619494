package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataPlayerWeek;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZonePlayerDataBean;

import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.ProtectionStatusEnums;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.config.OfflineZoneFamilyConfig;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert.OfflineZonePlayerDataConvert;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneDataPlayerWeekDao;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.mapper.ext.ProtectionWithStatusExtMapper;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.GetPlayerDataListParam;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.OfflineZonePlayerDataWithProtectionDTO;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.OfflineZoneProtectionDTO;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.OfflineZoneProtectionWithStatusDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 线下专区主播数据管理器
 * <AUTHOR>
 */
@Component
@Slf4j
public class OfflineZonePlayerDataManager {

    @Autowired
    private OfflineZoneDataPlayerWeekDao offlineZoneDataPlayerWeekDao;


    @Autowired
    private OfflineZoneProtectionManager offlineZoneProtectionManager;

    @Autowired
    private ProtectionWithStatusExtMapper offlineZoneProtectionWithStatusExtMapper;

    @Autowired
    private UserCommonService userService;

    @Autowired
    private OfflineZoneFamilyConfig offlineZoneFamilyConfig;

    /**
     * 获取主播数据列表
     *
     * @param queryDTO 查询DTO
     * @return 分页主播数据列表
     */
    public PageBean<OfflineZonePlayerDataBean> getPlayerDataList(GetPlayerDataListParam queryDTO) {
        // 判断是否需要保护状态过滤
        boolean needProtectionFilter = queryDTO.getProtectionStatus() != null || queryDTO.getProtection() != null;

        if (needProtectionFilter) {
            return getPlayerDataListWithProtectionFilter(queryDTO);
        } else {
            return getPlayerDataListOriginal(queryDTO);
        }
    }

    /**
     * 原有的查询逻辑（不包含保护状态过滤）
     *
     * @param queryDTO 查询DTO
     * @return 分页主播数据列表
     */
    private PageBean<OfflineZonePlayerDataBean> getPlayerDataListOriginal(GetPlayerDataListParam queryDTO) {
        // 查询数据
        PageList<OfflineZoneDataPlayerWeek> pageList = offlineZoneDataPlayerWeekDao.getPlayerDataList(queryDTO);

        // 转换数据
        List<OfflineZonePlayerDataBean> list = OfflineZonePlayerDataConvert.INSTANCE.entityListToBeanList(pageList);

        PageBean<OfflineZonePlayerDataBean> pageBean = PageBean.of(pageList.getTotal(), list);

        // 填充用户信息
        fillUserInfo(queryDTO.getAppId(), pageBean);

        // 填充保护状态信息
        fillProtectionInfo(queryDTO, pageBean);

        return pageBean;
    }

    /**
     * 带保护状态过滤的查询逻辑
     *
     * @param queryDTO 查询DTO
     * @return 分页主播数据列表
     */
    private PageBean<OfflineZonePlayerDataBean> getPlayerDataListWithProtectionFilter(GetPlayerDataListParam queryDTO) {
        // 设置部署环境
        queryDTO.setDeployEnv(ConfigUtils.getEnvRequired().name());

        // 构建排序子句（使用"player."前缀，因为联表查询中主播表别名为player）
        String orderByClause = offlineZoneDataPlayerWeekDao.buildOrderByClauseWithPrefix(
                queryDTO.getOrderMetrics(), queryDTO.getOrderType(), "player.");

        // 使用联表查询获取数据
        PageList<OfflineZonePlayerDataWithProtectionDTO> pageList =
            offlineZoneProtectionWithStatusExtMapper.selectPlayerDataWithProtection(
                    queryDTO.getPageNo(), queryDTO.getPageSize(), queryDTO, orderByClause);

        // 转换为目标Bean
        List<OfflineZonePlayerDataBean> list = OfflineZonePlayerDataConvert.INSTANCE.withProtectionDTOListToBeanList(pageList);

        PageBean<OfflineZonePlayerDataBean> pageBean = PageBean.of(pageList.getTotal(), list);

        // 填充用户信息（保护状态信息已经在查询中获取，不需要再次填充）
        fillUserInfo(queryDTO.getAppId(), pageBean);

        return pageBean;
    }



    /**
     * 填充用户信息
     *
     * @param appId 应用ID
     * @param pageBean 分页数据
     */
    private void fillUserInfo(Integer appId, PageBean<OfflineZonePlayerDataBean> pageBean) {
        if (CollectionUtils.isEmpty(pageBean.getList())) {
            return;
        }

        // 收集所有用户ID（主播ID和厅主ID）
        Set<Long> userIds = new HashSet<>();
        pageBean.getList().forEach(playerDataBean -> {
            if (playerDataBean.getUserId() != null) {
                userIds.add(playerDataBean.getUserId());
            }
            if (playerDataBean.getNjId() != null) {
                userIds.add(playerDataBean.getNjId());
            }
        });

        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        try {
            // 批量获取用户信息
            Map<Long, UserBean> userMap = getUserMap(appId, userIds);

            // 填充用户信息
            pageBean.getList().forEach(playerDataBean -> {
                // 填充主播信息
                UserBean userBean = userMap.get(playerDataBean.getUserId());
                if (userBean != null) {
                    playerDataBean.setPlayerInfo(userBean);
                }

                // 填充厅主信息
                UserBean njBean = userMap.get(playerDataBean.getNjId());
                if (njBean != null) {
                    playerDataBean.setNjInfo(njBean);
                }
            });
        } catch (Exception e) {
            log.warn("获取用户信息失败, appId={}, userIds={}", appId, userIds, e);
        }
    }

    /**
     * 填充保护状态信息
     *
     * @param param 查询参数
     * @param pageBean 分页数据
     */
    private void fillProtectionInfo(GetPlayerDataListParam param, PageBean<OfflineZonePlayerDataBean> pageBean) {
        if (CollectionUtils.isEmpty(pageBean.getList())) {
            return;
        }

        // 收集所有主播ID
        Set<Long> playerIds = pageBean.getList().stream()
                .map(OfflineZonePlayerDataBean::getUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(playerIds)) {
            return;
        }

        try {
            // 按厅分组主播，因为不同厅的主播需要分别查询保护状态
            Map<Long, Set<Long>> njPlayerMap = pageBean.getList().stream()
                    .filter(bean -> bean.getUserId() != null && bean.getNjId() != null)
                    .collect(Collectors.groupingBy(
                            OfflineZonePlayerDataBean::getNjId,
                            Collectors.mapping(OfflineZonePlayerDataBean::getUserId, Collectors.toSet())
                    ));

            // 存储所有保护状态信息
            Map<Long, OfflineZoneProtectionWithStatusDTO> allProtectionStatusMap = new HashMap<>();

            // 为每个厅分别查询保护状态
            for (Map.Entry<Long, Set<Long>> entry : njPlayerMap.entrySet()) {
                Long njId = entry.getKey();
                Set<Long> njPlayerIds = entry.getValue();

                if (!njPlayerIds.isEmpty()) {
                    Map<Long, OfflineZoneProtectionWithStatusDTO> njProtectionStatusMap =
                            offlineZoneProtectionManager.batchGetProtectionStatus(param.getAppId(), param.getFamilyId(), njId, njPlayerIds);
                    allProtectionStatusMap.putAll(njProtectionStatusMap);
                }
            }

            // 填充保护状态
            pageBean.getList().forEach(playerDataBean -> {
                OfflineZoneProtectionWithStatusDTO protectionWithStatus = allProtectionStatusMap.get(playerDataBean.getUserId());
                if (protectionWithStatus != null) {
                    // 设置保护状态
                    Integer protectionStatus = protectionWithStatus.getProtectionStatus();
                    ProtectionStatusEnums statusEnum = ProtectionStatusEnums.getByCode(protectionStatus);

                    // 设置是否受保护（只有主播确认状态才算受保护）
                    OfflineZoneProtectionDTO protectionDetail = protectionWithStatus.getProtectionDetail();
                    playerDataBean.setProtection(protectionDetail != null && statusEnum == ProtectionStatusEnums.PLAYER_CONFIRMED);

                    // 设置保护状态描述
                    playerDataBean.setProtectionStatus(statusEnum.getCode());

                    // 设置协议ID
                    playerDataBean.setProtectionId(Optional.ofNullable(protectionDetail).map(OfflineZoneProtectionDTO::getId).orElse(null));
                } else {
                    // 如果没有查询到状态信息，默认为未上传
                    playerDataBean.setProtection(false);
                    playerDataBean.setProtectionStatus(ProtectionStatusEnums.NOT_UPLOADED.getCode());
                }
            });
        } catch (Exception e) {
            log.warn("获取保护状态信息失败, appId={}, familyId={}, playerIds={}",
                    param.getAppId(), param.getFamilyId(), playerIds, e);
        }
    }


    /**
     * 批量获取用户信息
     *
     * @param appId 应用ID
     * @param userIds 用户ID集合
     * @return 用户信息映射
     */
    private Map<Long, UserBean> getUserMap(Integer appId, Set<Long> userIds) {
        Result<List<UserBean>> result = userService.getUserByIds(appId, new ArrayList<>(userIds));
        if (RpcResult.isSuccess(result)) {
            return result.target().stream()
                    .collect(Collectors.toMap(UserBean::getId, userBean -> userBean, (a, b) -> a));
        }
        return Collections.emptyMap();
    }
}
