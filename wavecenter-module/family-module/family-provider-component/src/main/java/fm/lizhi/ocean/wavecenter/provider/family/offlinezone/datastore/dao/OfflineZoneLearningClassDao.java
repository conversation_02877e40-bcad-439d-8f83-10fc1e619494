package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLearningClass;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLearningClassWhiteList;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.mapper.ext.OfflineZoneLearningClassExtMapper;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.mapper.ext.OfflineZoneLearningClassWhiteListExtMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * 线下专区学习课堂 Dao
 */
@Repository
@Slf4j
public class OfflineZoneLearningClassDao {

    @Autowired
    private OfflineZoneLearningClassExtMapper offlineZoneLearningClassExtMapper;

    @Autowired
    private OfflineZoneLearningClassWhiteListExtMapper offlineZoneLearningClassWhiteListExtMapper;

    /**
     * 根据类型查询学习课堂列表
     *
     * @param appId 应用ID
     * @param type  类型
     * @return 学习课堂列表
     */
    public List<OfflineZoneLearningClass> listLearningClassesByType(Integer appId, Integer type) {
        String deployEnv = ConfigUtils.getEnvRequired().name();
        List<OfflineZoneLearningClass> list = offlineZoneLearningClassExtMapper.listByType(appId, type, deployEnv);
        log.debug("listLearningClassesByType, appId={}, type={}, deployEnv={}, list={}", appId, type, deployEnv, list);
        return list;
    }

    /**
     * 获取学习课堂白名单ID列表Map, key: 学习课堂ID, value: 白名单ID列表
     *
     * @param learningIds 学习课堂ID列表
     * @return 学习课堂白名单ID列表Map
     */
    public ListValuedMap<Long, Long> getLearningClassWhiteListIdsMap(List<Long> learningIds) {
        ArrayListValuedHashMap<Long, Long> classWhiteListIdsMap = new ArrayListValuedHashMap<>();
        List<OfflineZoneLearningClassWhiteList> whiteLists = getWhiteListsByLearningIds(learningIds);
        for (OfflineZoneLearningClassWhiteList whiteList : whiteLists) {
            classWhiteListIdsMap.put(whiteList.getLearningId(), whiteList.getWhiteId());
        }
        log.debug("getLearningClassWhiteListIdsMap, learningIds={}, classWhiteListIdsMap={}", learningIds, classWhiteListIdsMap);
        return classWhiteListIdsMap;
    }

    private List<OfflineZoneLearningClassWhiteList> getWhiteListsByLearningIds(List<Long> learningIds) {
        if (CollectionUtils.isEmpty(learningIds)) {
            return Collections.emptyList();
        }
        return offlineZoneLearningClassWhiteListExtMapper.selectByLearningIds(learningIds);
    }
}
