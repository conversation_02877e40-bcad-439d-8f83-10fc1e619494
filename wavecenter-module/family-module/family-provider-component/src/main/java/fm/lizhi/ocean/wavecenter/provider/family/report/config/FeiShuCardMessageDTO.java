package fm.lizhi.ocean.wavecenter.provider.family.report.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class FeiShuCardMessageDTO {

    /**
     * 消息类型
     */
    @JsonProperty("msg_type")
    private String msgType = "interactive";

    /**
     * 卡片信息
     */
    private Card card;

    @Data
    @Accessors(chain = true)
    public static class Card {
        /**
         * 模板类型
         */
        private String type = "template";

        /**
         * 模板信息
         */
        private FeiShuTemplateInfoDTO data;
    }
}
