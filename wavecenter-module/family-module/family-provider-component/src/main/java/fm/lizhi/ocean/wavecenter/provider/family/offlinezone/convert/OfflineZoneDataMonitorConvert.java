package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert;

import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.base.util.CalculateUtil;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataFamilyWeek;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataHallWeek;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.MapDataBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.MapDataCityBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.MapDataRoomBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZoneMetricsDataBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.RegionBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.OfflineZoneRoomCategoryEnums;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ResponseDataMonitorFamilySummary;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ResponseDataMonitorRoomSummary;
import org.apache.commons.collections4.MapUtils;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 线下数据监控转换器
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.ERROR,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface OfflineZoneDataMonitorConvert {

    OfflineZoneDataMonitorConvert INSTANCE = Mappers.getMapper(OfflineZoneDataMonitorConvert.class);

    /**
     * 构建指标数据Bean
     *
     * @param current 当前值
     * @param pre     上期值
     * @return 指标数据Bean
     */
    default OfflineZoneMetricsDataBean buildMetricsDataBean(Double current, Double pre) {
        OfflineZoneMetricsDataBean bean = new OfflineZoneMetricsDataBean();
        bean.setCurrent(current);
        bean.setPre(pre);

        // 使用CalculateUtil计算环比
        String ratio = CalculateUtil.relativeRatio( pre != null ? String.valueOf(pre): "0", current != null ? String.valueOf(current) : "0");
        bean.setRatio(ratio);

        return bean;
    }

    /**
     * 构建指标数据Bean（BigDecimal版本）
     */
    default OfflineZoneMetricsDataBean buildMetricsDataBean(BigDecimal current, BigDecimal pre) {
        Double currentValue = current != null ? current.doubleValue() : 0;
        Double preValue = pre != null ? pre.doubleValue() : 0;
        return buildMetricsDataBean(currentValue, preValue);
    }

    /**
     * 构建指标数据Bean（Integer版本）
     */
    default OfflineZoneMetricsDataBean buildMetricsDataBean(Integer current, Integer pre) {
        Double currentValue = current != null ? current.doubleValue() : 0;
        Double preValue = pre != null ? pre.doubleValue() : 0;
        return buildMetricsDataBean(currentValue, preValue);
    }

    /**
     * 构建指标数据Bean（Long版本）
     */
    default OfflineZoneMetricsDataBean buildMetricsDataBean(Long current, Long pre) {
        Double currentValue = current != null ? current.doubleValue() : 0;
        Double preValue = pre != null ? pre.doubleValue() : 0;
        return buildMetricsDataBean(currentValue, preValue);
    }

    /**
     * 构建家族汇总响应
     */
    default ResponseDataMonitorFamilySummary buildFamilySummaryResponse(OfflineZoneDataFamilyWeek currentWeek, OfflineZoneDataFamilyWeek previousWeek) {
        ResponseDataMonitorFamilySummary response = new ResponseDataMonitorFamilySummary();
        // 基地数
        response.setBasicCnt(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getBasicCnt(),
                previousWeek != null ? previousWeek.getBasicCnt() : 0
        ));

        // 线下厅数
        response.setOfflineHallCnt(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getOfflineHallCnt(),
                previousWeek != null ? previousWeek.getOfflineHallCnt() : 0
        ));

        // 线下主播数
        response.setOfflinePlayerCnt(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getOfflinePlayerCnt(),
                previousWeek != null ? previousWeek.getOfflinePlayerCnt() : 0
        ));

        // 线下厅数占比
        response.setOfflineHallCntRate(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getOfflineHallCntRate(),
                previousWeek != null ? previousWeek.getOfflineHallCntRate() : BigDecimal.ZERO
        ));

        // 线下主播数占比
        response.setOfflinePlayerCntRate(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getOfflinePlayerCntRate(),
                previousWeek != null ? previousWeek.getOfflinePlayerCntRate() : BigDecimal.ZERO
        ));

        // 线下厅收入
        response.setOfflineHallIncome(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getOfflineHallIncome(),
                previousWeek != null ? previousWeek.getOfflineHallIncome() : BigDecimal.ZERO
        ));

        // 线下厅收入占比
        response.setOfflineHallIncomeRate(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getOfflineHallIncomeRate(),
                previousWeek != null ? previousWeek.getOfflineHallIncomeRate() : BigDecimal.ZERO
        ));

        // 受保护主播数
        response.setProtectedPlayerCnt(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getProtectedPlayerCntRealTime(),
                previousWeek != null ? previousWeek.getProtectedPlayerCntRealTime() : 0
        ));

        // 受保护主播数占比
        response.setProtectedPlayerCntRate(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getProtectedPlayerCntRateRealTime(),
                previousWeek != null ? previousWeek.getProtectedPlayerCntRateRealTime() : BigDecimal.ZERO
        ));

        return response;
    }

    /**
     * 构建厅汇总响应
     */
    default ResponseDataMonitorRoomSummary buildRoomSummaryResponse(OfflineZoneDataHallWeek currentWeek, OfflineZoneDataHallWeek previousWeek) {
        ResponseDataMonitorRoomSummary response = new ResponseDataMonitorRoomSummary();
        if (OfflineZoneRoomCategoryEnums.OFFLINE_HALL.getCategory() == currentWeek.getCategory()){
            response.setCity(currentWeek.getCity());
            response.setProvince(currentWeek.getProvince());
        }

        // 线下主播数
        response.setOfflinePlayerCnt(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getOfflinePlayerCnt(),
                previousWeek != null ? previousWeek.getOfflinePlayerCnt() : 0
        ));

        // 线下主播数占比
        response.setOfflinePlayerCntRate(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getOfflinePlayerCntRate(),
                previousWeek != null ? previousWeek.getOfflinePlayerCntRate() : BigDecimal.ZERO
        ));

        // 线下厅收入
        response.setIncome(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getIncome(),
                previousWeek != null ? previousWeek.getIncome() : BigDecimal.ZERO
        ));

        // 受保护主播数
        response.setProtectedPlayerCnt(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getProtectedPlayerCntRealTime(),
                previousWeek != null ? previousWeek.getProtectedPlayerCntRealTime() : 0
        ));

        // 受保护主播数占比
        response.setProtectedPlayerCntRate(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getProtectedPlayerCntRateRealTime(),
                previousWeek != null ? previousWeek.getProtectedPlayerCntRateRealTime() : BigDecimal.ZERO
        ));

        // 线下主播收入
        response.setOfflinePlayerIncome(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getOfflinePlayerIncome(),
                previousWeek != null ? previousWeek.getOfflinePlayerIncome() : BigDecimal.ZERO
        ));

        // 线下主播收入占比
        response.setOfflinePlayerIncomeRate(OfflineZoneDataMonitorConvert.INSTANCE.buildMetricsDataBean(
                currentWeek.getOfflinePlayerIncomeRate(),
                previousWeek != null ? previousWeek.getOfflinePlayerIncomeRate() : BigDecimal.ZERO
        ));

        return response;
    }

    default MapDataBean buildMapDataBean(String province, List<OfflineZoneDataHallWeek> cityHalls, Map<Long, UserBean> userMap) {
        MapDataBean bean = new MapDataBean();
        bean.setProvince(province);

        // 构建城市列表
        List<MapDataCityBean> cityList = cityHalls.stream()
                .collect(Collectors.groupingBy(OfflineZoneDataHallWeek::getCity))
                .entrySet().stream()
                .map(entry -> OfflineZoneDataMonitorConvert.INSTANCE.buildMapDataCityBean(
                        entry.getKey(),
                        entry.getValue(),
                        userMap
                ))
                .collect(Collectors.toList());
        bean.setCityList(cityList);

        return bean;

    }

    /**
     * 构建地图城市数据Bean
     */
    default MapDataCityBean buildMapDataCityBean(String city, List<OfflineZoneDataHallWeek> cityHalls, Map<Long, UserBean> userMap) {

        // 构建厅列表
        List<MapDataRoomBean> roomList = cityHalls.stream()
                .map(hall -> OfflineZoneDataMonitorConvert.INSTANCE.buildMapDataRoomBean(
                        MapUtils.getObject(userMap, hall.getNjId()),
                        hall
                ))
                .collect(Collectors.toList());

        MapDataCityBean mapDataCityBean = new MapDataCityBean();
        mapDataCityBean.setCity(city);
        mapDataCityBean.setRoomList(roomList);

        return mapDataCityBean;
    }

    /**
     * 构建地图厅数据Bean
     */
    default MapDataRoomBean buildMapDataRoomBean(UserBean user, OfflineZoneDataHallWeek hall) {
        MapDataRoomBean bean = new MapDataRoomBean();
        if (user != null){
            bean.setNjId(user.getId());
            bean.setNjName(user.getName());
            bean.setNjBand(user.getBand());
            bean.setPhoto(user.getPhoto());
        }

        if (hall != null){
            bean.setOfflinePlayerCnt(hall.getOfflinePlayerCnt() != null ? hall.getOfflinePlayerCnt().longValue() : 0L);
        }
        return bean;
    }

    /**
     * 构建空的家族汇总响应
     */
    default ResponseDataMonitorFamilySummary buildEmptyFamilySummary() {
        ResponseDataMonitorFamilySummary response = new ResponseDataMonitorFamilySummary();
        OfflineZoneMetricsDataBean emptyMetrics = new OfflineZoneMetricsDataBean()
                .setCurrent(0.0)
                .setPre(0.0)
                .setRatio("--");

        response.setBasicCnt(emptyMetrics);
        response.setOfflineHallCnt(emptyMetrics);
        response.setOfflinePlayerCnt(emptyMetrics);
        response.setOfflineHallCntRate(emptyMetrics);
        response.setOfflinePlayerCntRate(emptyMetrics);
        response.setOfflineHallIncome(emptyMetrics);
        response.setOfflineHallIncomeRate(emptyMetrics);
        response.setProtectedPlayerCnt(emptyMetrics);
        response.setProtectedPlayerCntRate(emptyMetrics);

        return response;
    }

    /**
     * 构建空的厅汇总响应
     */
    default ResponseDataMonitorRoomSummary buildEmptyRoomSummary() {
        ResponseDataMonitorRoomSummary response = new ResponseDataMonitorRoomSummary();
        OfflineZoneMetricsDataBean emptyMetrics = new OfflineZoneMetricsDataBean()
                .setCurrent(0.0)
                .setPre(0.0)
                .setRatio("--");

        response.setOfflinePlayerCnt(emptyMetrics);
        response.setOfflinePlayerCntRate(emptyMetrics);
        response.setIncome(emptyMetrics);
        response.setProtectedPlayerCnt(emptyMetrics);
        response.setProtectedPlayerCntRate(emptyMetrics);
        response.setOfflinePlayerIncome(emptyMetrics);
        response.setOfflinePlayerIncomeRate(emptyMetrics);

        return response;
    }

    /**
     * 构建行政区划列表
     *
     * @param groupedData 按省份分组的城市数据
     * @return 行政区划列表
     */
    default List<RegionBean> buildRegionList(Map<String, Set<String>> groupedData) {
        List<RegionBean> regionList = new ArrayList<>();
        
        for (Map.Entry<String, Set<String>> entry : groupedData.entrySet()) {
            String province = entry.getKey();
            Set<String> cities = entry.getValue();

            // 构建省份数据
            RegionBean regionBean = new RegionBean();
            regionBean.setProvince(province);
            regionBean.setCityList(cities);
            regionList.add(regionBean);
        }
        
        return regionList;
    }
}
