package fm.lizhi.ocean.wavecenter.provider.family.report.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.ocean.wavecenter.datastore.family.report.entity.*;
import fm.lizhi.ocean.wavecenter.datastore.family.report.mapper.*;
import fm.lizhi.ocean.wavecenter.module.api.family.report.request.PageRoomViolationRecordRequest;
import fm.lizhi.ocean.wavecenter.provider.family.report.config.PlayerReportConfig;
import fm.lizhi.ocean.wavecenter.provider.family.report.convert.PlayerReportApplyInfoConvert;
import fm.lizhi.ocean.wavecenter.provider.family.report.datastore.mapper.ReportRoomViolationRecordExtMapper;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

@Slf4j
@Repository
public class ReportApplyDao {
    @Autowired
    private PlayerReportApplyInfoConvert convert;
    @Autowired
    private ReportPlayerApplyRecordMapper reportPlayerApplyRecordMapper;
    @Autowired
    private PlayerReportConfig config;
    @Autowired
    private IdManager idManager;
    @Autowired
    private ReportPlayerApplyOperateResultMapper operateResultMapper;
    @Autowired
    private ReportRoomViolationRecordMapper reportRoomViolationRecordMapper;
    @Autowired
    private ReportRoomViolationOperateResultMapper reportRoomViolationOperateResultMapper;
    @Autowired
    private ReportRoomViolationMappingMapper reportRoomViolationMappingMapper;
    @Autowired
    private ReportRoomViolationRecordExtMapper reportRoomViolationRecordExtMapper;



    public ReportPlayerApplyOperateResult getReportPlayerApplyOperateResultById(Long reportPlayerApplyOperateResultId) {
        ReportPlayerApplyOperateResult entity = new ReportPlayerApplyOperateResult();
        entity.setId(reportPlayerApplyOperateResultId);
        return operateResultMapper.selectByPrimaryKey(entity);
    }

    public ReportRoomViolationOperateResult getReportRoomViolationOperateResultById(Long reportRoomViolationOperateResultId) {
        ReportRoomViolationOperateResult entity = new ReportRoomViolationOperateResult();
        entity.setId(reportRoomViolationOperateResultId);
        return reportRoomViolationOperateResultMapper.selectByPrimaryKey(entity);
    }


    public ReportPlayerApplyRecord selectReportPlayerApplyRecordById(Long reportPlayerApplyRecordId) {
        ReportPlayerApplyRecord entity = new ReportPlayerApplyRecord();
        entity.setId(reportPlayerApplyRecordId);
        return reportPlayerApplyRecordMapper.selectByPrimaryKey(entity);
    }

    public ReportRoomViolationMapping getReportRoomViolationMappingByResultId(Long reportRoomViolationOperateResultId) {
        ReportRoomViolationMappingExample example = new ReportRoomViolationMappingExample();
        example.createCriteria()
                .andRoomViolationOperateResultIdEqualTo(reportRoomViolationOperateResultId)
                .andReachConditionEqualTo(1);
        List<ReportRoomViolationMapping> reportRoomViolationMappings = reportRoomViolationMappingMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(reportRoomViolationMappings)) {
            return null;
        }
        return reportRoomViolationMappings.get(0);
    }

    public PageList<ReportRoomViolationRecord> pageReportRoomViolationRecord(PageRoomViolationRecordRequest request) {
        ReportRoomViolationRecordExample example = new ReportRoomViolationRecordExample();
        ReportRoomViolationRecordExample.Criteria criteria = example.createCriteria()
                .andAppIdEqualTo(request.getAppId())
                .andCreateTimeGreaterThanOrEqualTo(new Date(request.getStartDate()))
                .andCreateTimeLessThan(new Date(request.getEndDate()))
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        //增加排序
        example.setOrderByClause("create_time desc");
        if(request.getAccusedFamilyId() != null) {
            criteria.andFamilyIdEqualTo(request.getAccusedFamilyId());
        }
        if(request.getAccusedRoomId() != null) {
            criteria.andAccusedRoomIdEqualTo(request.getAccusedRoomId());
        }
        return reportRoomViolationRecordMapper.pageByExample(example, request.getPageNo(), request.getPageSize());
    }
}
