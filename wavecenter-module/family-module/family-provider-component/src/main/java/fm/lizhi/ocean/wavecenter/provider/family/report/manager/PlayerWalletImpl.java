package fm.lizhi.ocean.wavecenter.provider.family.report.manager;

import fm.lizhi.ocean.wavecenter.domain.family.report.entity.TimeRange;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.PlayerWallet;
import fm.lizhi.ocean.wavecenter.service.datacenter.manager.CharmStateManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PlayerWalletImpl implements PlayerWallet {

    @Autowired
    private CharmStateManager charmStateManager;

    @Override
    public Long getViolationIncome(TimeRange timeRange, Long accusedUserId, Long accusedRoomId) {
        return charmStateManager.sumTotalIncome(accusedRoomId,accusedUserId, timeRange.getStartTime(), timeRange.getEndTime());
    }
}
