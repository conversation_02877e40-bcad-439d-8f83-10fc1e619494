package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager;

import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRight;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRightIntroduction;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListLevelRightBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestListLevelRight;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert.OfflineZoneLevelRightConvert;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneLevelRightDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListValuedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 线下专区等级权益管理器
 */
@Component
@Slf4j
public class OfflineZoneLevelRightManager {

    @Autowired
    private OfflineZoneLevelRightConvert offlineZoneLevelRightConvert;

    @Autowired
    private OfflineZoneLevelRightDao offlineZoneLevelRightDao;

    /**
     * 列出线下专区等级权益
     *
     * @param request 请求参数
     * @return 等级权益列表
     */
    public List<ListLevelRightBean> listLevelRight(RequestListLevelRight request) {
        Integer appId = request.getAppId();
        List<OfflineZoneLevelRight> rights = offlineZoneLevelRightDao.listLevelRights(appId);
        List<Long> rightIds = rights.stream().map(OfflineZoneLevelRight::getId).collect(Collectors.toList());
        ListValuedMap<Long, OfflineZoneLevelRightIntroduction> levelRightIntroductionsMap = offlineZoneLevelRightDao.getLevelRightIntroductionsMap(rightIds);
        return offlineZoneLevelRightConvert.toListLevelRightBeans(rights, levelRightIntroductionsMap);
    }
}
