package fm.lizhi.ocean.wavecenter.provider.family.report.manager;

import fm.lizhi.ocean.wavecenter.domain.family.report.repository.model.DeviceInfoTarget;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.PlayerDevice;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserDeviceDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

@Slf4j
@Getter
public class PlayerDeviceImpl implements PlayerDevice {

    private  final UserManager userManager;

    private DeviceInfoTarget  result;

    public PlayerDeviceImpl(UserManager userManager) {
        this.userManager = userManager;
    }

    public static PlayerDeviceImpl valueOf(UserManager userManager, DeviceInfoTarget result) {
        PlayerDeviceImpl playerDevice = new PlayerDeviceImpl(userManager);
        playerDevice.result = result;
        return playerDevice;
    }

    @Override
    public DeviceInfoTarget getDeviceInfo(Integer appId,Long playerId) {
        if(result != null) {
            log.info("appId={};getDeviceInfo userId={};result={}", appId, playerId, result);
            return result;
        }
        result = new DeviceInfoTarget();
        Optional<UserDeviceDto> deviceDto = userManager.getLatestUserAppDeviceDto(playerId);
        if (deviceDto.isPresent()) {
            result.setAppDeviceId(deviceDto.get().getDeviceId());
            result.setAppDeviceIp(deviceDto.get().getIp());
        }
        Optional<UserDeviceDto> latestUserPcDeviceDto = userManager.getLatestUserPcDeviceDto(appId, playerId);
        if (latestUserPcDeviceDto.isPresent()) {
            result.setPcDeviceId(latestUserPcDeviceDto.get().getDeviceId());
            result.setPcDeviceIp(latestUserPcDeviceDto.get().getIp());
        }
        log.info("create appId={};getDeviceInfo userId={};result={}", appId, playerId, result);
        return result;
    }
}
