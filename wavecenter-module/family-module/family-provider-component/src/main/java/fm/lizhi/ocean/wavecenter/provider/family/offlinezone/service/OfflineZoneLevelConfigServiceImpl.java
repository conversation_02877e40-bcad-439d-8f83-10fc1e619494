package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.service;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListLevelConfigBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestListLevelConfig;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneLevelConfigService;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager.OfflineZoneLevelConfigManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 线下专区等级配置服务实现
 */
@ServiceProvider
@Slf4j
public class OfflineZoneLevelConfigServiceImpl implements OfflineZoneLevelConfigService {

    @Autowired
    private OfflineZoneLevelConfigManager offlineZoneLevelConfigManager;

    @Override
    public Result<List<ListLevelConfigBean>> listLevelConfig(RequestListLevelConfig request) {
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));
        try {
            List<ListLevelConfigBean> list = offlineZoneLevelConfigManager.listLevelConfig(request);
            LogContext.addResLog("listSize={}", list.size());
            if (log.isDebugEnabled()) {
                log.debug("listLevelConfig, request={}, list={}", JsonUtils.toJsonString(request), JsonUtils.toJsonString(list));
            }
            return RpcResult.success(list);
        } catch (Exception e) {
            log.error("listLevelConfig error, request={}", JsonUtils.toJsonString(request), e);
            return RpcResult.fail(CommonService.INTERNAL_ERROR, "系统异常，请稍后重试");
        }
    }
}
