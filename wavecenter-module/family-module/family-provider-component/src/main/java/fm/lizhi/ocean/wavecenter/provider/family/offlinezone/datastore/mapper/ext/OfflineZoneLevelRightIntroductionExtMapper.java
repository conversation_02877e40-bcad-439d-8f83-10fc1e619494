package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRightIntroduction;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface OfflineZoneLevelRightIntroductionExtMapper {

    @Select("<script>\n" +
            "  SELECT * FROM `offline_zone_level_right_introduction`\n" +
            "  WHERE `right_id` IN\n" +
            "    <foreach item=\"rightId\" collection=\"rightIds\" open=\"(\" separator=\",\" close=\")\">\n" +
            "      #{rightId}\n" +
            "    </foreach>\n" +
            "  ORDER BY `right_id` ASC, `index` ASC\n" +
            "</script>")
    List<OfflineZoneLevelRightIntroduction> selectByRightIds(@Param("rightIds") List<Long> rightIds);
}
