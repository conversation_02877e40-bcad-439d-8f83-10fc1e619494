package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneFamilyRating;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneFamilyRatingMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 公会评级 Dao
 * <AUTHOR>
 */
@Repository
@Slf4j
public class OfflineZoneFamilyRatingDao {

    @Autowired
    private OfflineZoneFamilyRatingMapper offlineZoneFamilyRatingMapper;

    /**
     * 获取公会评级信息
     * @param appId
     * @param familyId
     * @return
     */
    public OfflineZoneFamilyRating getFamilyLevel(int appId, Long familyId) {

        OfflineZoneFamilyRating offlineZoneFamilyRating = new OfflineZoneFamilyRating();
        offlineZoneFamilyRating.setAppId(appId);
        offlineZoneFamilyRating.setFamilyId(familyId);
        offlineZoneFamilyRating.setDeployEnv(ConfigUtils.getEnvRequired().name());


        return offlineZoneFamilyRatingMapper.selectOne(offlineZoneFamilyRating);

    }
}
