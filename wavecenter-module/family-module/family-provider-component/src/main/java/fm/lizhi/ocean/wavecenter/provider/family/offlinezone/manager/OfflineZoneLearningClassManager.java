package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager;

import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLearningClass;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListLearningClassByTypeBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestListLearningClassByType;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert.OfflineZoneLearningClassConvert;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneLearningClassDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListValuedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 线下专区学习课堂管理器
 */
@Component
@Slf4j
public class OfflineZoneLearningClassManager {

    @Autowired
    private OfflineZoneLearningClassConvert offlineZoneLearningClassConvert;

    @Autowired
    private OfflineZoneLearningClassDao offlineZoneLearningClassDao;

    /**
     * 根据类型查询学习课堂列表
     *
     * @param request 请求参数
     * @return 学习课堂列表
     */
    public List<ListLearningClassByTypeBean> listLearningClassByType(RequestListLearningClassByType request) {
        Integer appId = request.getAppId();
        Integer type = request.getType();
        List<OfflineZoneLearningClass> classEntities = offlineZoneLearningClassDao.listLearningClassesByType(appId, type);
        List<Long> learningIds = classEntities.stream().map(OfflineZoneLearningClass::getId).collect(Collectors.toList());
        ListValuedMap<Long, Long> classWhiteListIdsMap = offlineZoneLearningClassDao.getLearningClassWhiteListIdsMap(learningIds);
        return offlineZoneLearningClassConvert.toListLearningClassByTypeBeans(classEntities, classWhiteListIdsMap);
    }
}
