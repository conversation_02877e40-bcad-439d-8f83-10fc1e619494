package fm.lizhi.ocean.wavecenter.provider.family.report.service;

import com.google.common.collect.Lists;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.datastore.family.report.entity.ReportRoomViolationRecord;
import fm.lizhi.ocean.wavecenter.module.api.family.report.bean.ReportRoomViolationRecordBean;
import fm.lizhi.ocean.wavecenter.module.api.family.report.request.PageRoomViolationRecordRequest;
import fm.lizhi.ocean.wavecenter.module.api.family.report.service.PlayerJobHopReportDataService;
import fm.lizhi.ocean.wavecenter.provider.family.report.convert.PlayerReportApplyInfoConvert;
import fm.lizhi.ocean.wavecenter.provider.family.report.datastore.dao.ReportApplyDao;
import fm.lizhi.ocean.wavecenter.service.user.dto.SimpleUserDto;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@ServiceProvider
@Slf4j
public class PlayerJobHopReportDataServiceImpl implements PlayerJobHopReportDataService {

    @Resource
    private ReportApplyDao reportApplyDao;
    @Resource
    private PlayerReportApplyInfoConvert convert;
    @Resource
    private UserManager userManager;


    /**
     * 获取厅违规信息
     * report_room_violation_record
     *
     * @param request 请求参数
     * @return 响应结果
     */
    @Override
    public Result<PageBean<ReportRoomViolationRecordBean>> pageRoomViolationRecord(PageRoomViolationRecordRequest request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("response={}", request);
        if(request.getAccusedRoomId() == null && request.getAccusedFamilyId() == null) {
            return RpcResult.fail(PAGE_ROOM_VIOLATION_RECORD_PARAM_ERR);
        }
        PageList<ReportRoomViolationRecord> records = reportApplyDao.pageReportRoomViolationRecord(request);
        List<ReportRoomViolationRecordBean> recordBeanList = Lists.newArrayListWithExpectedSize(records.size());
        for (ReportRoomViolationRecord record : records) {
            ReportRoomViolationRecordBean bean = convert.toReportRoomViolationRecordBean(record);
            Map<Long, SimpleUserDto> map = userManager.getSimpleUserMapByIds(Lists.newArrayList(record.getAccusedUserId(), record.getAccusedRoomId()));
            SimpleUserDto userDto = map.get(record.getAccusedUserId());
            if (userDto != null) {
                bean.setAccusedUser(new UserBean().setId(userDto.getId()).setBand(userDto.getBand()).setName(userDto.getName()));
            }
            SimpleUserDto roomDto = map.get(record.getAccusedRoomId());
            if (roomDto != null) {
                bean.setAccusedNjUser(new UserBean().setId(roomDto.getId()).setBand(roomDto.getBand()).setName(roomDto.getName()));
            }
            recordBeanList.add(bean);
        }
        return RpcResult.success(PageBean.of(records.getTotal(), recordBeanList));
    }
}
