package fm.lizhi.ocean.wavecenter.provider.family.report.job;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.provider.family.report.config.*;
import fm.lizhi.ocean.wavecenter.provider.family.report.datastore.dao.AccusedStatRankDao;
import fm.lizhi.ocean.wavecenter.provider.family.report.manager.FeiShuBotManager;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class AccusedPunishRankNoticeJob implements JobHandler {

    @Autowired
    private PlayerReportConfig playerReportConfig;

    @Autowired
    private FeiShuBotManager feiShuManager;

    @Autowired
    private AccusedStatRankDao accusedStatRankDao;


    @Override
    public void execute(JobExecuteContext context) throws Exception {
        //获取当前周的前一周开始时间
        Date lastWeekStartDay = MyDateUtil.getLastWeekStartDay();
        Date startWeekDate = DateUtil.getDayStart(lastWeekStartDay);
        //转换格式
        String startTimeStr = DateUtil.formatDateToString(lastWeekStartDay, DateUtil.date_2);
        lastWeekStartDay = DateUtil.formatStrToDate(startTimeStr, DateUtil.date_2);

        //计算周期
        String endTimeStr = DateUtil.formatDateToString(MyDateUtil.getLastWeekEndDay(), DateUtil.date_2);
        String weekRange = startTimeStr + "-" + endTimeStr;
        for (BusinessEvnEnum evnEnum : BusinessEvnEnum.values()) {
            if (evnEnum.getOnline() != 1) {
                continue;
            }

            //查询违规次数统计数据是否出来了
            boolean res = accusedStatRankDao.finishAccusedCountRankStat(evnEnum.getAppId(), lastWeekStartDay);
            if (res) {
                String punishCountRankWebUrl = playerReportConfig.getBizConfig(evnEnum.getAppId()).getPunishCountRankWebUrl();
                FeiShuCardMessageParamDTO messageParamDTO = buildMessageParam(evnEnum, punishCountRankWebUrl, weekRange, RankTypeConfigEnum.ACCUSED_COUNT_RANK, startWeekDate);
                Result<FeiShuCardMessageResultDTO> result = feiShuManager.sendCardMessage(messageParamDTO);
                if (RpcResult.isFail(result)) {
                    log.error("Send card message fail, appId: {}, msg: {}", evnEnum.getAppId(), result.getMessage());
                } else {
                    log.info("Send card message success, appId: {}, code:{}, msg:{}", evnEnum.getAppId(), result.target().getCode(), result.target().getMsg());
                }
            }

            //查询违规收入统计数据是否出来了
            boolean rankStat = accusedStatRankDao.finishAccusedIncomeRankStat(evnEnum.getAppId(), lastWeekStartDay);
            if (rankStat) {
                String punishIncomeRankWebUrl = playerReportConfig.getBizConfig(evnEnum.getAppId()).getPunishIncomeRankWebUrl();
                FeiShuCardMessageParamDTO messageParamDTO = buildMessageParam(evnEnum, punishIncomeRankWebUrl, weekRange, RankTypeConfigEnum.ACCUSED_INCOME_RANK, startWeekDate);
                Result<FeiShuCardMessageResultDTO> result = feiShuManager.sendCardMessage(messageParamDTO);
                if (RpcResult.isFail(result)) {
                    log.error("Send income rank card message fail, appId: {}, msg: {}", evnEnum.getAppId(), result.getMessage());
                } else {
                    log.info("Send income rank message success, appId: {}, code:{}, msg:{}", evnEnum.getAppId(), result.target().getCode(), result.target().getMsg());
                }
            }
        }

    }

    /**
     * 构建FeiShu卡片消息
     *
     * @param evnEnum   APP
     * @param actionUrl 动作链接
     * @return 飞书卡片消息
     */
    private FeiShuCardMessageParamDTO buildMessageParam(BusinessEvnEnum evnEnum, String actionUrl, String weekRange, RankTypeConfigEnum configEnum, Date startWeekDate) {
        JSONObject templateVariable = new JSONObject();
        templateVariable.put("title", configEnum.title);
        templateVariable.put("actionUrl", actionUrl + startWeekDate.getTime());
        templateVariable.put("content", configEnum.content);
        templateVariable.put("weekRange", weekRange);
        FeiShuCardMessageDTO feiShuCardMessageDTO = new FeiShuCardMessageDTO()
                .setCard(new FeiShuCardMessageDTO.Card()
                        .setData(
                                new FeiShuTemplateInfoDTO()
                                        .setTemplateId(playerReportConfig.getFeiShuTemplateId())
                                        .setTemplateVersionName(playerReportConfig.getFeiShuTemplateVersionName())
                                        .setTemplateVariable(templateVariable))
                );
        return new FeiShuCardMessageParamDTO()
                .setWebhook(playerReportConfig.getBizConfig(evnEnum.getAppId()).getAccusedRankWebHook())
                .setCardMessageDTO(feiShuCardMessageDTO);
    }

    @Getter
    @AllArgsConstructor
    enum RankTypeConfigEnum {

        /**
         * 跳槽次数榜
         */
        ACCUSED_COUNT_RANK("accused_count_rank", "挖槽违规厅排行榜", "快去看看上周哪些厅又挖人了！"),

        /**
         * 跳槽收入榜
         */
        ACCUSED_INCOME_RANK("accused_income_rank", "挖槽厅-挖槽主播收入", "记得在结算调账前完成跳槽主播收益调整~");;


        private String type;

        private String title;

        private String content;

    }
}
