package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager;

import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneAdPosition;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListAdPositionBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestListAdPosition;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert.OfflineZoneAdPositionConvert;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneAdPositionDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 线下专区广告展位管理器
 */
@Component
@Slf4j
public class OfflineZoneAdPositionManager {

    @Autowired
    private OfflineZoneAdPositionConvert offlineZoneAdPositionConvert;

    @Autowired
    private OfflineZoneAdPositionDao offlineZoneAdPositionDao;

    /**
     * 列出所有广告展位
     *
     * @param request 请求参数
     * @return 广告展位列表
     */
    public List<ListAdPositionBean> listAdPosition(RequestListAdPosition request) {
        Integer appId = request.getAppId();
        List<OfflineZoneAdPosition> entities = offlineZoneAdPositionDao.listAdPosition(appId);
        return offlineZoneAdPositionConvert.toListAdPositionBeans(entities);
    }
}
