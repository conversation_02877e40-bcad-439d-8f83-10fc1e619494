package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.service;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListLearningClassByTypeBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestListLearningClassByType;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneLearningClassService;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager.OfflineZoneLearningClassManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 学习课堂服务实现
 */
@ServiceProvider
@Slf4j
public class OfflineZoneLearningClassServiceImpl implements OfflineZoneLearningClassService {

    @Autowired
    private OfflineZoneLearningClassManager offlineZoneLearningClassManager;

    @Override
    public Result<List<ListLearningClassByTypeBean>> listLearningClassByType(RequestListLearningClassByType request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        try {
            List<ListLearningClassByTypeBean> list = offlineZoneLearningClassManager.listLearningClassByType(request);
            LogContext.addResLog("listSize={}", list.size());
            if (log.isDebugEnabled()) {
                log.debug("listLearningClassByType, request={}, list={}", JsonUtils.toJsonString(request), JsonUtils.toJsonString(list));
            }
            return RpcResult.success(list);
        } catch (Exception e) {
            log.error("listLearningClassByType error, request={}", JsonUtils.toJsonString(request), e);
            return RpcResult.fail(CommonService.INTERNAL_ERROR, "系统异常，请稍后重试");
        }
    }
}
