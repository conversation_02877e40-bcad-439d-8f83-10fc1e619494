package fm.lizhi.ocean.wavecenter.provider.family.report.config;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.config.AbsBizConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;


@EqualsAndHashCode(callSuper = true)
@Data
@ConfigurationProperties(prefix = "wavecenter-family.report")
public class PlayerReportConfig extends AbsBizConfig<CommonPlayerReportConfig> {

    /**
     * 举报限流，一分钟内可以提交次数
     */
    private Integer reportApplyCountLimitPerMin = 3;

    /**
     * 是否打开成功举报的提交限制
     * 限制：相同的记录只能提交成功一次(被举报者(原号/小号)&被举报厅->相同,视为相同举报记录)
     */
    private boolean openSuccessReportSubmitCountLimit = false;

    /**
     * 成功举报的提交时间限制(分钟)
     */
    private Integer reportApplyMinLimit = 60;

    /**
     * 飞书模版id
     */
    private String feiShuTemplateId = "AAq9MIyHSC3jy";

    /**
     * 模版版本号
     */
    private String feiShuTemplateVersionName = "1.0.3";

    private PpPlayerReportConfig pp;

    private XmPlayerReportConfig xm;

    private HyPlayerReportConfig hy;

    /**
     * 跳槽判断是否开启同家族检测
     */
    private boolean openSameFamilyCheck = true;

    public PlayerReportConfig() {
        PpPlayerReportConfig ppConfig = new PpPlayerReportConfig();
        XmPlayerReportConfig xmConfig = new XmPlayerReportConfig();
        HyPlayerReportConfig hyConfig = new HyPlayerReportConfig();
        this.pp = ppConfig;
        this.xm = xmConfig;
        this.hy = hyConfig;
        bizConfigMap.put(BusinessEvnEnum.PP.appId(), ppConfig);
        bizConfigMap.put(BusinessEvnEnum.XIMI.appId(), xmConfig);
        bizConfigMap.put(BusinessEvnEnum.HEI_YE.appId(), hyConfig);
    }
}
