package fm.lizhi.ocean.wavecenter.provider.family.report.config;

/**
 * <AUTHOR>
 */
public class ReportKafkaTopicConstants {

    /**
     * 封禁用户操作消息topic
     */
    public static final String BAN_USER_OPERATE_TOPIC = "audit_topic_security_invoke_operation";

    /**
     * 封禁设备的操作消息
     */
    public static final String BAN_DEVICE_OPERATE_TOPIC = "lz_topic_risk_bw_account_async";

    public static final String SYNC_AUDIT_SUCCESS_REPORT_MESSAGE = "audit_topic_security_invoke_record";
}
