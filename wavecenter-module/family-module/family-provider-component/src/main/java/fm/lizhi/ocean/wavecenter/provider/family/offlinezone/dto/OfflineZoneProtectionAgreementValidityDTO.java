package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 *
 * 跳槽保护上传协议时效记录
 *
 * @date 2025-08-11 09:54:38
 */
@Data
@Accessors(chain = true)
public class OfflineZoneProtectionAgreementValidityDTO {

    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 公会ID
     */
    private Long familyId;

    /**
     * 厅ID
     */
    private Long njId;

    /**
     * 主播ID
     */
    private Long playerId;

    /**
     * 签约时间
     */
    private Date beginSignTime;

    /**
     * 过期时间
     */
    private Date expiredTime;

    /**
     * 重置次数
     */
    private Integer resetsCnt;

    /**
     * 环境：TEST/PRE/PRO
     */
    private String deployEnv;

    /**
     * 操作人
     */
    private String operator;
}