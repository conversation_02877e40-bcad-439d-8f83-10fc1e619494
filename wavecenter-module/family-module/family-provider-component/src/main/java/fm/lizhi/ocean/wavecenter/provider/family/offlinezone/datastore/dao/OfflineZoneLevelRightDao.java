package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRight;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRightIntroduction;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.mapper.ext.OfflineZoneLevelRightExtMapper;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.mapper.ext.OfflineZoneLevelRightIntroductionExtMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * 线下专区等级权益 Dao
 */
@Repository
@Slf4j
public class OfflineZoneLevelRightDao {

    @Autowired
    private OfflineZoneLevelRightExtMapper offlineZoneLevelRightExtMapper;

    @Autowired
    private OfflineZoneLevelRightIntroductionExtMapper offlineZoneLevelRightIntroductionExtMapper;

    /**
     * 列出所有等级权益, 不包含已删除的
     *
     * @param appId 应用ID
     * @return 等级权益列表
     */
    public List<OfflineZoneLevelRight> listLevelRights(Integer appId) {
        String deployEnv = ConfigUtils.getEnvRequired().name();
        List<OfflineZoneLevelRight> levelRights = offlineZoneLevelRightExtMapper.listLevelRights(appId, deployEnv);
        log.debug("list levelRights, appId={}, deployEnv={}, levelRights={}", appId, deployEnv, levelRights);
        return levelRights;
    }

    private List<OfflineZoneLevelRightIntroduction> getIntroductionsByRightIds(List<Long> rightIds) {
        if (CollectionUtils.isEmpty(rightIds)) {
            return Collections.emptyList();
        }
        return offlineZoneLevelRightIntroductionExtMapper.selectByRightIds(rightIds);
    }

    /**
     * 根据权益ID列表获取权益ID到权益介绍列表的映射, 其中权益介绍列表已经按index升序排列
     *
     * @param rightIds 权益ID列表
     * @return 权益ID到权益介绍列表的映射
     */
    public ListValuedMap<Long, OfflineZoneLevelRightIntroduction> getLevelRightIntroductionsMap(List<Long> rightIds) {
        ArrayListValuedHashMap<Long, OfflineZoneLevelRightIntroduction> levelRightIntroductionsMap = new ArrayListValuedHashMap<>();
        List<OfflineZoneLevelRightIntroduction> introductions = getIntroductionsByRightIds(rightIds);
        for (OfflineZoneLevelRightIntroduction introduction : introductions) {
            levelRightIntroductionsMap.put(introduction.getRightId(), introduction);
        }
        log.debug("get levelRightIntroductionsMap, rightIds={}, levelRightIntroductionsMap={}", rightIds, levelRightIntroductionsMap);
        return levelRightIntroductionsMap;
    }
}
