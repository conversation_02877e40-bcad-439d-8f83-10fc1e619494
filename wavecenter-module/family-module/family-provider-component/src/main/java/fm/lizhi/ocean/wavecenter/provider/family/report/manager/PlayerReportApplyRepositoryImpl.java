package fm.lizhi.ocean.wavecenter.provider.family.report.manager;

import com.alibaba.csp.sentinel.util.AssertUtil;
import com.google.common.collect.Lists;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.base.processor.ProcessorFactory;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.report.entity.*;
import fm.lizhi.ocean.wavecenter.datastore.family.report.mapper.*;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.OperateObjTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.OperateTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.ReportTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Player;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Room;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.TimeRange;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply.JudgeResult;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply.MultiAccountPlayerJobHopReportApply;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply.OriginalAccountPlayerJobHopReportApply;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.*;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.PlayerReportApplyRepository;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.model.RoomGradientOperateRule;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.model.RoomViolationRecordValue;
import fm.lizhi.ocean.wavecenter.provider.family.report.config.PlayerReportConfig;
import fm.lizhi.ocean.wavecenter.provider.family.report.convert.PlayerReportApplyInfoConvert;
import fm.lizhi.ocean.wavecenter.provider.family.report.datastore.mapper.ReportRoomViolationRecordExtMapper;
import fm.lizhi.ocean.wavecenter.provider.family.report.process.ReportOperateProcessor;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PlayerReportApplyRepositoryImpl implements PlayerReportApplyRepository {

    @Autowired
    private ProcessorFactory factory;
    @Autowired
    private PlayerReportApplyInfoConvert convert;
    @Autowired
    private ReportPlayerApplyRecordMapper reportPlayerApplyRecordMapper;
    @Autowired
    private PlayerReportConfig config;
    @Autowired
    private IdManager idManager;
    @Autowired
    private ReportPlayerApplyOperateResultMapper operateResultMapper;
    @Autowired
    private ReportRoomViolationRecordMapper reportRoomViolationRecordMapper;
    @Autowired
    private ReportRoomViolationOperateResultMapper reportRoomViolationOperateResultMapper;
    @Autowired
    private ReportRoomViolationMappingMapper reportRoomViolationMappingMapper;
    @Autowired
    private ReportRoomViolationRecordExtMapper reportRoomViolationRecordExtMapper;

    /**
     * 因为domain没有businessEnv,迫不得已把这个重要业务丢出来外面..
     * @param appId
     * @param subPlayer
     * @param mainPlayer
     * @param accusedRoom
     * @return
     */
    @Override
    public List<ReportPlayerOperateItem> createPlayerReportOperate(Integer appId, Player subPlayer, Player mainPlayer, Room accusedRoom) {
        // 设置业务环境上下文
        BusinessEvnEnum businessEvnEnum = BusinessEvnEnum.from(appId);
        ContextUtils.reSetBusinessEvnEnum(businessEvnEnum);
        ReportOperateProcessor processor = factory.getProcessor(ReportOperateProcessor.class);
        //创建罚单
        List<ReportPlayerOperateItem> operateAccountItems = processor.createOperateAccountItems(subPlayer, mainPlayer, accusedRoom);
        boolean auto = config.getBizConfig(appId).isAutoBanDeviceId();
        operateAccountItems.add(processor.createOperateDeviceIdItem(idManager.genId(), subPlayer, auto));
        return operateAccountItems;
    }

    /**
     * 因为domain没有businessEnv,迫不得已把这个重要业务丢出来外面..
     * @param appId
     * @param originalPlayer
     * @param accusedRoom
     * @return
     */
    @Override
    public List<ReportPlayerOperateItem> createPlayerReportOperate(Integer appId, Player originalPlayer, Room accusedRoom) {
        // 设置业务环境上下文
//        BusinessEvnEnum businessEvnEnum = BusinessEvnEnum.from(appId);
//        ContextUtils.reSetBusinessEvnEnum(businessEvnEnum);
        List<ReportPlayerOperateItem> result = new ArrayList<>();
        //自动封禁原号
        result.add(ReportPlayerOperateItem.create(idManager.genId(), originalPlayer,
                Operate.createAutoPersistentOperate(OperateTypeEnum.BAN, OperateObjTypeEnum.ACCOUNT)));

        ReportOperateProcessor processor = factory.getProcessor(ReportOperateProcessor.class);
        boolean auto = config.getBizConfig(appId).isAutoBanDeviceId();
        result.add(processor.createOperateDeviceIdItem(idManager.genId(), originalPlayer, auto));
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOperateOrder(Integer appId, long applyId, ReportTypeEnum reportTypeEnum, List<ReportPlayerOperateItem> playerOperateItems, Date operateTime, RoomViolation roomViolation) {
        //保存主播惩罚项
        batchStoreRoomOperate(applyId, reportTypeEnum, playerOperateItems, operateTime);
        //保存厅违规记录
        storeRoomViolation(applyId, reportTypeEnum.getCode(), operateTime, appId, roomViolation);
    }


    /**
     * 批量保存主播违规项
     */
    public void batchStoreRoomOperate(long applyId, ReportTypeEnum reportTypeEnum, List<ReportPlayerOperateItem> playerOperateItems, Date operateTime) {
        //打印日志 将batchStoreRoomOperate方法入参全部打印出来
        log.info("batchStoreRoomOperate method called. apply: {}, playerOperateItems: {}", applyId, JsonUtils.toJsonString(playerOperateItems));
        List<ReportPlayerApplyOperateResult> results = new ArrayList<>();
        for (ReportPlayerOperateItem playerOperateItem : playerOperateItems) {
            results.add(convert.toReportPlayerApplyOperateResult(playerOperateItem, applyId, reportTypeEnum.getCode(), operateTime));
        }
        if(CollectionUtils.isEmpty(results)) {
            return;
        }
        AssertUtil.assertState(operateResultMapper.batchInsert(results) == results.size(), "插入举报主播操作失败");
    }

    /**
     * 保存厅违规记录
     */
    public void storeRoomViolation(long playerReportApplyRecordId, Integer reportType, Date operateTime, Integer appId, RoomViolation roomViolation) {
        ReportRoomViolationRecord record = convert.toReportRoomViolationRecord(roomViolation, playerReportApplyRecordId, reportType, appId, operateTime);
        AssertUtil.assertState(reportRoomViolationRecordMapper.insert(record) == 1, "插入举报厅违规记录失败");
        if(roomViolation.isRoomHasOperate()) {
            ReportRoomViolationOperateResult result = convert.toReportRoomViolationOperateResult(roomViolation, operateTime);
            AssertUtil.assertState(reportRoomViolationOperateResultMapper.insert(result) == 1, "插入举报厅操作失败");
            //更新关联的厅违规记录的punished为true
            List<Long> ids = roomViolation.getOtherRoomViolationRecord().stream().map(RoomViolationRecordValue::getId).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(ids)) {
                log.info("更新厅违规记录;appId={};playerReportApplyRecordId={};ids={}", appId, playerReportApplyRecordId, ids);
                AssertUtil.assertState(
                        reportRoomViolationRecordExtMapper.batchUpdateRoomViolationRecordPunished(ids, roomViolation.isRoomHasOperate() ? 1 : 0) == ids.size(),
                        "更新厅违规记录失败");
            }
            //插入mapping reachCondition = false
            List<ReportRoomViolationMapping> mappingList = Lists.newArrayList();
            for (RoomViolationRecordValue roomViolationRecord : roomViolation.getOtherRoomViolationRecord()) {
                ReportRoomViolationMapping reportRoomViolationMapping =
                        convert.toReportRoomViolationMapping(roomViolationRecord, roomViolation.getOperateItem().getId(), playerReportApplyRecordId, operateTime);
                mappingList.add(reportRoomViolationMapping);
            }
            //mapping的reachCondition = true
            ReportRoomViolationMapping reportRoomViolationMapping =
                    convert.toReportRoomViolationMapping(record, roomViolation.getOperateItem().getId(), playerReportApplyRecordId, operateTime);
            mappingList.add(reportRoomViolationMapping);
            AssertUtil.assertState(
                    reportRoomViolationMappingMapper.batchInsert(mappingList) == mappingList.size(), "插入举报厅违规记录映射失败");
        }
    }

    @Override
    public void updateOperateStatus(ReportPlayerOperateItem item, String operator) {
        ReportPlayerApplyOperateResult update = new ReportPlayerApplyOperateResult();
        update.setId(item.getId());
        update.setOperator(operator);
        update.setStatus(item.getStatus().getCode());
        AssertUtil.assertState(operateResultMapper.updateByPrimaryKey(update) == 1, "更新主播操作状态失败");
    }


    @Override
    public void updateRoomViolation(ReportRoomOperateItem item, String operator) {
        ReportRoomViolationOperateResult operateResult = new ReportRoomViolationOperateResult();
        operateResult.setId(item.getId());
        operateResult.setStatus(item.getStatus().getCode());
        operateResult.setOperateDuration(item.getOperate().getOperateDuration());
        operateResult.setOperator(operator);
        AssertUtil.assertState(
                reportRoomViolationOperateResultMapper.updateByPrimaryKey(operateResult) == 1, "更新举报厅自动操作状态失败");
    }


    /**
     * 获取指定时间的厅违规信息
     * Params:
     * accusedRoom – 违规厅 period – 时间段
     * Returns:
     * 违规信息
     */
    @Override
    public List<RoomViolationRecordValue> findAllRoomInfoByPeriod(Room accusedRoom, TimeRange period) {
        ReportRoomViolationRecordExample example = new ReportRoomViolationRecordExample();
        example.createCriteria()
                .andAccusedRoomIdEqualTo(accusedRoom.getId())
                .andCreateTimeGreaterThanOrEqualTo(period.getStartTime())
                .andCreateTimeLessThan(period.getEndTime())
                .andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        List<ReportRoomViolationRecord> reportRoomViolationRecords =
                reportRoomViolationRecordMapper.selectByExample(example);
        return convert.toRoomViolationRecordList(reportRoomViolationRecords);
    }

    @Override
    public Long getReportRoomViolationConditionCount(Integer appId) {
        return config.getBizConfig(appId).getRoomOperateConditionCount();
    }




    @Override
    public List<RoomGradientOperateRule> getRoomGradientOperateRules(Integer appId) {
        String json  = config.getBizConfig(appId).getRoomGradientOperateRuleListJson();
        return JsonUtil.loadsArray(json, RoomGradientOperateRule.class);
    }

    @Override
    public Integer getMaxOperateSeqByRoom(Room accusedRoom, TimeRange timeRange) {
        Integer max = reportRoomViolationRecordExtMapper.getMaxOperateSeqByRoom(
                accusedRoom.getId(), timeRange.getStartTime(), timeRange.getEndTime(), ConfigUtils.getEnvRequired().name());
        log.info("getMaxOperateSeqByRoom accusedRoom={};timeRange={};max: {}",accusedRoom.getId(), timeRange, max);
        return max == null ? 0 : max;
    }

    @Override
    public boolean insert(MultiAccountPlayerJobHopReportApply apply) {
        ReportPlayerApplyRecord record = convert.toJobHopReportPlayerApplyRecord(apply);
        AssertUtil.assertState(reportPlayerApplyRecordMapper.insert(record) == 1, "插入大小号跳槽记录失败");
        return true;
    }

    @Override
    public boolean insert(OriginalAccountPlayerJobHopReportApply apply) {
        ReportPlayerApplyRecord record = convert.toOriginalJobHopReportPlayerApplyRecord(apply);
        AssertUtil.assertState(reportPlayerApplyRecordMapper.insert(record) == 1, "插入原号跳槽记录失败");
        return true;
    }

    @Override
    public void updateJudgeResult(Long id, JudgeResult judgeResult) {
        ReportPlayerApplyRecord record = new ReportPlayerApplyRecord();
        record.setId(id);
        record.setReportResultStatus(judgeResult.getJudgmentStatus().getCode());
        record.setReportResultReason(judgeResult.getReason());
        AssertUtil.assertState(reportPlayerApplyRecordMapper.updateByPrimaryKey(record) == 1, "更新举报判定结果失败");
        log.info("更新举报判定结果成功. id: {}, judgeResult: {}", id, judgeResult);
    }

    @Override
    public boolean isMainPlayerRoomInBlackList(int appId, Room signRoom) {
        Set<Long> accusedFamilyBlackList = config.getBizConfig(appId).getAccusedFamilyBlackList();
        Set<Long> accusedRoomBlackList = config.getBizConfig(appId).getAccusedRoomBlackList();
        return accusedRoomBlackList.contains(signRoom.getId())
                || accusedFamilyBlackList.contains(signRoom.getFamilyId());
    }

    @Override
    public Integer getReportViolationIncomeConfig(ReportTypeEnum reportTypeEnum, Integer appId) {
        if(reportTypeEnum == ReportTypeEnum.JOB_HOPPING) {
            return config.getBizConfig(appId).getReportViolationIncome();
        }
        return config.getBizConfig(appId).getOriginJobHopReportViolationIncome();
    }

    @Override
    public boolean isOpenSameFamilyCheck() {
        return config.isOpenSameFamilyCheck();
    }


    @Override
    public boolean isAccusedRoomInWhiteList(Integer appId, Room accusedRoom) {
        Set<Long> accusedFamilyWhiteList = config.getBizConfig(appId).getPlayerJobHoppingFamilyWhiteList();
        Set<Long> accusedRoomWhiteList = config.getBizConfig(appId).getPlayerJobHoppingRoomWhiteList();
        return accusedRoomWhiteList.contains(accusedRoom.getId())
                || accusedFamilyWhiteList.contains(accusedRoom.getFamilyId());
    }


}
