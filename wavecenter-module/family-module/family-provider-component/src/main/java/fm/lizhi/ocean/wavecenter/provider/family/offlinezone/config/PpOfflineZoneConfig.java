package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.config;

import lombok.Data;

import java.awt.*;

/**
 * <AUTHOR>
 */
@Data
public class PpOfflineZoneConfig implements CommonOfflineZoneConfig {


    /**
     * 跳槽保护协议告示
     */
    private String protectionNotice = "为确保线下相关权益有效实施，请务必保证上述信息与上传协议文本内容一致";

    @Override
    public String getProtectionNotice() {
        return protectionNotice;
    }
}