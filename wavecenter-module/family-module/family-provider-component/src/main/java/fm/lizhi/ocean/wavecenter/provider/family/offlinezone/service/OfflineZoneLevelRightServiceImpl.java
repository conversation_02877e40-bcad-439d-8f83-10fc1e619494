package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.service;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListLevelRightBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestListLevelRight;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneLevelRightService;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager.OfflineZoneLevelRightManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 线下专区等级权益服务实现
 */
@ServiceProvider
@Slf4j
public class OfflineZoneLevelRightServiceImpl implements OfflineZoneLevelRightService {

    @Autowired
    private OfflineZoneLevelRightManager offlineZoneLevelRightManager;

    @Override
    public Result<List<ListLevelRightBean>> listLevelRight(RequestListLevelRight request) {
        LogContext.addResLog("request={}", request);
        try {
            List<ListLevelRightBean> list = offlineZoneLevelRightManager.listLevelRight(request);
            LogContext.addResLog("listSize={}", list.size());
            if (log.isDebugEnabled()) {
                log.debug("listLevelRight, request={}, list={}", JsonUtils.toJsonString(request), JsonUtils.toJsonString(list));
            }
            return RpcResult.success(list);
        } catch (Exception e) {
            log.error("listLevelRight error, request={}", JsonUtils.toJsonString(request), e);
            return RpcResult.fail(CommonService.INTERNAL_ERROR, "系统异常，请稍后重试");
        }
    }
}
