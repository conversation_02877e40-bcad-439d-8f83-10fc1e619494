package fm.lizhi.ocean.wavecenter.provider.family.report.manager;


import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.provider.family.report.config.FeiShuCardMessageParamDTO;
import fm.lizhi.ocean.wavecenter.provider.family.report.config.FeiShuCardMessageResultDTO;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 创作者飞书应用 api操作应用
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class FeiShuBotManager {

    @Autowired
    private OkHttpClient okHttpClient;

    /**
     * 发送卡片消息. 参考
     * <a href="https://open.feishu.cn/document/feishu-cards/send-feishu-card">
     * 使用自定义机器人发送卡片消息
     * </a>
     *
     * @param param 消息参数
     * @return 发送结果
     */
    public Result<FeiShuCardMessageResultDTO> sendCardMessage(FeiShuCardMessageParamDTO param) {
        try {
            String requestBody = JsonUtils.toJsonString(param.getCardMessageDTO());

            //设置apiKey
            Request request = new Request.Builder()
                    .url(param.getWebhook())
                    .post(RequestBody.create(MediaType.parse("application/json"),
                            requestBody))
                    .build();

            Response response = okHttpClient.newCall(request).execute();
            if (response.body() == null) {
                return RpcResult.fail(GeneralRCode.GENERAL_RCODE_UNKNOWN_ERROR, "empty body");
            }
            if (response.isSuccessful()) {
                return RpcResult.success(new FeiShuCardMessageResultDTO().setCode(0));
            }
            String msgJson = JsonUtil.dumps(response.body());
            log.info("FeiShuBotManager.sendCardMessage code={}, json={}", response.code(), msgJson);
            return RpcResult.fail(GeneralRCode.GENERAL_RCODE_UNKNOWN_ERROR, msgJson);
        } catch (Exception e) {
            log.error("Send card message error, param: {}", param, e);
            return RpcResult.fail(GeneralRCode.GENERAL_RCODE_UNKNOWN_ERROR, e.getMessage());
        }
    }

}
