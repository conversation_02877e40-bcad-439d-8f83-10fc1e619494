package fm.lizhi.ocean.wavecenter.provider.family.report.kafka.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 举报同步审核处罚DTO
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportSyncAuditPunishMessage {

    /**
     * 产品名
     */
    private String appName;

    /**
     * 业务类型
     */
    private Integer type;

    /**
     * 被举报人ID
     */
    private Long fromUserId;

    /**
     * 举报人 ID
     */
    private Long toUserId;

    /**
     * 处罚厅时候必填（op = 14 封禁直播间） 所属业务 id - 厅id
     */
    private Long belongId;

    /**
     * 业务方给的内容id
     */
    private String contentId;

    /**
     *  op = 1 通过 , op = 2 不通过,op = 14 封禁直播间, op = 151 封禁账号
     */
    private String op;

    /**
     * 原因
     */
    private String reason;

    /**
     *
     审核备注 :
     举报大小号关键信息不一致，(实名/设备/IP均不符)
     举报大小号(实名/设备/IP)一致,且大小号签约不同公会,大号签约早于小号
     原号与原签约厅解约超14天
     使用签约号去非同工会跳槽厅,获取收益达处罚标准
     */
    private String note;

    /**
     * 处罚时效: 1小时 、 1天、 3天、1年、永久
     */
    private String punishTime;


    /**
     *  大小号跳槽 或 原号跳槽
     */
    private String content;

    /**
     *  被举报人 跳槽厅厅主ID
     */
    private Long recJobHoppingLiveUserId;

    /**
     *  大号ID
     */
    private Long bigUserId;

}
