package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 线下专区主播数据（包含保护状态）DTO
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class OfflineZonePlayerDataWithProtectionDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 周开始日期，格式YYYY-MM-DD
     */
    private Date startWeekDate;

    /**
     * 周结束日期，格式YYYY-MM-DD
     */
    private Date endWeekDate;

    /**
     * 主播ID
     */
    private Long userId;

    /**
     * 主播名称
     */
    private String userName;

    /**
     * 实名证件号码
     */
    private String idCardNumber;

    /**
     * 实名姓名
     */
    private String idName;

    /**
     * 公会ID
     */
    private Long familyId;

    /**
     * 公会名称
     */
    private String familyName;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 厅主名称
     */
    private String njName;

    /**
     * 签约时间
     */
    private Date beginSignTime;

    /**
     * 主播分类：0 线下，1 线上
     */
    private Integer category;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 主播收入
     */
    private BigDecimal income;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    // ========== 保护状态相关字段 ==========

    /**
     * 保护协议ID
     */
    private Long protectionId;

    /**
     * 主播同意状态：-1-未处理，0-不同意，1-同意
     */
    private Integer playerAgree;

    /**
     * 是否归档：0-否，1-是
     */
    private Boolean archived;

    /**
     * 协议过期时间
     */
    private Date expiredTime;

    /**
     * 计算出的保护状态值 (0-5)
     */
    private Integer protectionStatus;

    /**
     * 计算出的是否受保护
     */
    private Boolean isProtected;
}
