package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.base.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.common.config.CommonConfig;
import fm.lizhi.ocean.wavecenter.common.utils.UrlUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLearningClass;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListLearningClassByTypeBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.OfflineZoneLearningClassTypeEnum;
import org.apache.commons.collections4.ListValuedMap;
import org.apache.commons.lang3.Validate;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 线下专区学习课堂转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {
                ConfigUtils.class,
                Date.class,
        },
        uses = {
                CommonConvert.class,
        }
)
public abstract class OfflineZoneLearningClassConvert {

    @Autowired
    protected CommonConfig commonConfig;

    /**
     * 转换为按类型列出学习课堂的bean列表
     *
     * @param classEntities        学习课堂实体列表
     * @param classWhiteListIdsMap 学习课堂白名单ID列表Map, key: 学习课堂ID, value: 白名单ID列表
     * @return 按类型列出的学习课堂bean列表
     */
    public abstract List<ListLearningClassByTypeBean> toListLearningClassByTypeBeans(List<OfflineZoneLearningClass> classEntities, @Context ListValuedMap<Long, Long> classWhiteListIdsMap);

    @Mapping(target = "fileUrl", source = "classEntity", qualifiedByName = "addFileUrlHostIfRequired")
    @Mapping(target = "fileCover", source = "classEntity.fileCover", qualifiedByName = "addWaveCdnHost")
    @Mapping(target = "whiteIds", source = "classEntity.id", qualifiedByName = "getWhiteListIds")
    protected abstract ListLearningClassByTypeBean toListLearningClassByTypeBean(OfflineZoneLearningClass classEntity, @Context ListValuedMap<Long, Long> classWhiteListIdsMap);

    @Named("addFileUrlHostIfRequired")
    protected String addFileUrlHostIfRequired(OfflineZoneLearningClass classEntity) {
        Integer type = classEntity.getType();
        String fileUrl = classEntity.getFileUrl();
        OfflineZoneLearningClassTypeEnum typeEnum = OfflineZoneLearningClassTypeEnum.fromValue(type);
        Validate.notNull(typeEnum);
        return typeEnum.isRomeFs() ? UrlUtils.addHostOrEmpty(fileUrl, commonConfig.getRomeFsDownloadCdn()) : fileUrl;
    }

    @Named("addWaveCdnHost")
    protected String addWaveCdnHost(String path) {
        return UrlUtils.addHostOrEmpty(path, commonConfig.getRomeFsDownloadCdn());
    }

    @Named("getWhiteListIds")
    protected List<Long> getWhiteListIds(Long learningId, @Context ListValuedMap<Long, Long> classWhiteListIdsMap) {
        if (learningId == null || classWhiteListIdsMap == null) {
            return Collections.emptyList();
        }
        return classWhiteListIdsMap.get(learningId);
    }
}
