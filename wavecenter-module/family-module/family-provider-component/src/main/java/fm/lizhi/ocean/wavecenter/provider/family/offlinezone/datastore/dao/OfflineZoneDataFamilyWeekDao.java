package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao;

import cn.hutool.core.date.DateUtil;
import fm.lizhi.ocean.wavecenter.common.utils.DateTimeUtils;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataFamilyWeek;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataFamilyWeekExample;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneDataFamilyWeekMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 线下专区-公会明细表-周 Dao
 * <AUTHOR>
 */
@Component
@Slf4j
public class OfflineZoneDataFamilyWeekDao {

    @Autowired
    private OfflineZoneDataFamilyWeekMapper offlineZoneDataFamilyWeekMapper;

    /**
     * 根据家族ID查询最新的家族数据
     *
     * @param appId    应用ID
     * @param familyId 家族ID
     * @return 最新的家族周数据
     */
    public OfflineZoneDataFamilyWeek getLastWeekByFamilyId(Integer appId, Long familyId) {
        OfflineZoneDataFamilyWeekExample example = new OfflineZoneDataFamilyWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andFamilyIdEqualTo(familyId)
                .andStartWeekDateEqualTo(MyDateUtil.getLastWeekStartDay())
        ;
        example.setOrderByClause("start_week_date DESC LIMIT 1");

        List<OfflineZoneDataFamilyWeek> list = offlineZoneDataFamilyWeekMapper.selectByExample(example);
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 根据家族ID查询指定时间上一周的家族数据
     *
     * @param appId         应用ID
     * @param familyId      家族ID
     * @param currentStartWeekDate 当前周开始日期
     * @return 上一周的家族数据
     */
    public OfflineZoneDataFamilyWeek getPreviousWeekByFamilyId(Integer appId, Long familyId, Date currentStartWeekDate) {
        OfflineZoneDataFamilyWeekExample example = new OfflineZoneDataFamilyWeekExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andFamilyIdEqualTo(familyId)
                .andStartWeekDateEqualTo(MyDateUtil.getLastWeekStart(currentStartWeekDate));
        example.setOrderByClause("start_week_date DESC LIMIT 1");
        
        List<OfflineZoneDataFamilyWeek> list = offlineZoneDataFamilyWeekMapper.selectByExample(example);
        return list.isEmpty() ? null : list.get(0);
    }
}
