package fm.lizhi.ocean.wavecenter.provider.family.report.kafka.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 审核Kafka消息结构
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportRiskKafkaMessage {
    /**
     * 请求ID
     */
    private String requestId;
    /**
     * 来源
     */
    private String source;
    /**
     * 请求时间
     * 2021-09-01T09:09:09.999Z
     */
    private String requestTime;
    /**
     * 事件时间
     * 2021-09-01T09:09:09.999Z
     */
    private String eventTime;
    /**
     * 应用ID
     */
    private String appId;
    /**
     * 事件
     */
    private String event;
    /**
     * 名单类型 (1-白名单, 2-黑名单, 3-解封黑名单)
     */
    private Integer color;
    /**
     * 维度类型 (did-设备ID, uid-用户ID, ip-IP地址)
     */
    private String dimension;
    /**
     * 名单值
     */
    private String value;
    /**
     * 封禁时长(秒)，-1:表示永久 0 :默认值
     */
    private Integer duration;
    /**
     * 操作者 (system_auto-系统自动, admin_001-管理员等)
     */
    private String operator;
    /**
     * 备注
     */
    private String remark;

}
