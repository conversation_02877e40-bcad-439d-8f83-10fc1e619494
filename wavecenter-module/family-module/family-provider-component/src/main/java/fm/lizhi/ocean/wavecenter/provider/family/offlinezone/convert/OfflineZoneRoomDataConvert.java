package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert;

import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataHallWeek;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZoneRoomDataBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestGetRoomDataList;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.GetRoomDataListParam;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 线下专区厅数据转换器
 * <AUTHOR>
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
)
public interface OfflineZoneRoomDataConvert {

    OfflineZoneRoomDataConvert INSTANCE = Mappers.getMapper(OfflineZoneRoomDataConvert.class);

    /**
     * Request 转换为 DTO
     *
     * @param request 请求对象
     * @return DTO对象
     */
    GetRoomDataListParam requestToDTO(RequestGetRoomDataList request);

    /**
     * Entity 列表转换为 Bean 列表
     *
     * @param entities 实体列表
     * @return Bean列表
     */
    List<OfflineZoneRoomDataBean> entityListToBeanList(List<OfflineZoneDataHallWeek> entities);
}
