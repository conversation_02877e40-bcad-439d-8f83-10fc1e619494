package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneAdPosition;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.mapper.ext.OfflineZoneAdPositionExtMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 线下专区广告展位 Dao
 */
@Repository
@Slf4j
public class OfflineZoneAdPositionDao {

    @Autowired
    private OfflineZoneAdPositionExtMapper offlineZoneAdPositionExtMapper;

    /**
     * 列出所有广告展位, 按权重降序排列
     *
     * @param appId 应用ID
     * @return 广告展位列表
     */
    public List<OfflineZoneAdPosition> listAdPosition(Integer appId) {
        String deployEnv = ConfigUtils.getEnvRequired().name();
        List<OfflineZoneAdPosition> list = offlineZoneAdPositionExtMapper.listAdPosition(appId, deployEnv);
        log.debug("listAdPosition, appId={}, deployEnv={}, list={}", appId, deployEnv, list);
        return list;
    }
}
