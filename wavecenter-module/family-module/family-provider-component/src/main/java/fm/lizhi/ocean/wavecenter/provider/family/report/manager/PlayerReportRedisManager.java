package fm.lizhi.ocean.wavecenter.provider.family.report.manager;

import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.ocean.wavecenter.base.constants.TimeConstant;
import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.datastore.family.report.rediskey.PlayerReportRedisKey;
import fm.lizhi.ocean.wavecenter.provider.family.report.config.PlayerReportConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
public class PlayerReportRedisManager {

    @Resource(name = "redis_ocean_wavecenter")
    private RedisClient redisClient;

    @Resource
    private PlayerReportConfig playerReportConfig;
    
    /**
     * 获取举报者成功举报的key
     *
     * @param appId 应用ID
     * @param reporterId 举报者ID
     * @return Redis key
     */
    public String getReporterPassApplyKey(Integer appId, Long reporterId) {
        //获取当前时间，计算出分钟数
        long currentTime = System.currentTimeMillis();
        long minute = currentTime / 1000 / 60;
        return PlayerReportRedisKey.REPORTER_PASS_APPLY.getKey(appId, reporterId, minute);
    }
    
    /**
     * 获取被成功举报的挖槽厅key
     *
     * @param appId 应用ID
     * @param reporterId 举报者ID
     * @param accusedUserId 被举报者ID
     * @param accusedRoomId 被举报的挖槽厅ID
     * @return Redis key
     */
    public String getAccusedRoomKey(Integer appId, Long reporterId, Long accusedUserId, Long accusedRoomId) {
        return PlayerReportRedisKey.ACCUSED_ROOM.getKey(appId, reporterId, accusedUserId, accusedRoomId);
    }
    
    /**
     * 读取举报者成功举报的信息
     * 
     * @param appId 应用ID
     * @param reporterId 举报者ID
     * @return 举报信息，如果不存在返回null
     */
    public boolean triggerReporterPassApplyLimit(Integer appId, Long reporterId) {
        String reporterKey = getReporterPassApplyKey(appId, reporterId);
        String count = redisClient.get(reporterKey);
        if(count == null) {
            return false;
        }
        return Long.parseLong(count) >= playerReportConfig.getReportApplyCountLimitPerMin();
    }
    
    /**
     * 读取被成功举报的挖槽厅信息
     * 
     * @param appId 应用ID
     * @param reporterId 举报者ID
     * @param accusedUserId 被举报者ID
     * @param accusedRoomId 被举报的挖槽厅ID
     * @return 举报信息，如果不存在返回null
     */
    public boolean existAccusedRoom(Integer appId, Long reporterId, Long accusedUserId, Long accusedRoomId) {
        String accusedKey = getAccusedRoomKey(appId, reporterId, accusedUserId, accusedRoomId);
        return redisClient.get(accusedKey) != null;
    }
    
    /**
     * 记录举报者成功举报的信息
     * 
     * @param appId 应用ID
     * @param reporterId 举报者ID
     * @param accusedUserId 被举报者ID
     * @param accusedRoomId 被举报的挖槽厅ID
     */
    public void recordReporterPassApply(Integer appId, Long reporterId, Long accusedUserId, Long accusedRoomId) {
        // 1. 存储举报者成功举报的时间，过期时间为1分钟，key中包含appId, 举报者id, value为1
        String reporterKey = getReporterPassApplyKey(appId, reporterId);
        redisClient.incr(reporterKey);
        redisClient.expire(reporterKey, 120); // 60秒 = 1分钟
        
        // 2. 存储被成功举报的挖槽厅, key中包含appId,举报者id,被举报者,挖槽厅ID value为1 过期时间为1小时
        String accusedKey = getAccusedRoomKey(appId, reporterId, accusedUserId, accusedRoomId);
        redisClient.setex(accusedKey, playerReportConfig.getReportApplyMinLimit() * 60, "1"); // 3600秒 = 1小时
    }


    public RedisLock tryGetReportPlayerApplyLock(int appId, long user1, long user2) {
        //获取key
        long min = Math.min(user1, user2);
        long max = Math.max(user1, user2);
        String key = PlayerReportRedisKey.REPORTER_APPLY_LOCK.getKey(appId, min, max);
        int timeout = TimeConstant.ONE_MINUTE * 2;
        return new RedisLock(redisClient, key, timeout, timeout);
    }

    public RedisLock tryGetReportRoomOperateLock(int appId, long roomId) {
        //获取key
        String key = PlayerReportRedisKey.REPORTER_APPLY_LOCK.getKey(appId, roomId);
        int timeout = TimeConstant.ONE_MINUTE * 2;
        return new RedisLock(redisClient, key, timeout, timeout);
    }

}