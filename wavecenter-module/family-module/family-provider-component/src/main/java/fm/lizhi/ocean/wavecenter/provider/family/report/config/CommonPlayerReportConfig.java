package fm.lizhi.ocean.wavecenter.provider.family.report.config;

import java.util.Set;

/**
 * <AUTHOR>
 */
public interface CommonPlayerReportConfig {

    /**
     * 获取跳槽厅白名单
     *
     * @return 跳槽厅白名单
     */
    Set<Long> getPlayerJobHoppingRoomWhiteList();
    Set<Long> getPlayerJobHoppingFamilyWhiteList();

    Set<Long> getAccusedFamilyBlackList();

    Set<Long> getAccusedRoomBlackList();

    /**
     * 获取跳槽违规榜单发送的飞书webhook
     */
    String getAccusedRankWebHook();

    /**
     * 违规次数榜单后台跳转url
     */
    String getPunishCountRankWebUrl();

    /**
     * 违规收入榜单后台跳转url
     */
    String getPunishIncomeRankWebUrl();

    /**
     * 大小号-违规收入对比数值
     * @return
     */
    Integer getReportViolationIncome();
    /**
     * 原号-违规收入对比数值
     * @return
     */
    Integer getOriginJobHopReportViolationIncome();

    /**
     * 发送私信官方号用户ID
     */
    Long getSendMessageOfficialUserId();

    /**
     * 厅违规梯度处罚规则
     * @return
     */
    String getRoomGradientOperateRuleListJson();

    /**
     * 跳槽举报同步审核信息中的审核类型
     * @return
     */
    Integer getSyncAuditType();

    /**
     * 达到厅处罚的条件数量
     * @return
     */
    Long getRoomOperateConditionCount();

    /**
     * 是否自动封禁设备
     */
    boolean isAutoBanDeviceId();

    /**
     * 支持的举报类型
     */
    Set<Integer> getSupportReportType();
}