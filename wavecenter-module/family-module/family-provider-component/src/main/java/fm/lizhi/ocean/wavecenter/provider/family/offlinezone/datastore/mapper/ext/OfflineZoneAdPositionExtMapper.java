package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneAdPosition;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface OfflineZoneAdPositionExtMapper {

    @Select("SELECT * FROM `offline_zone_ad_position`\n" +
            "WHERE `app_id` = #{appId}\n" +
            "  AND `deploy_env` = #{deployEnv}\n" +
            "  AND `status` = 1\n" +
            "  AND `deleted` = 0\n" +
            "ORDER BY `weight` DESC, `id` DESC")
    List<OfflineZoneAdPosition> listAdPosition(@Param("appId") Integer appId, @Param("deployEnv") String deployEnv);
}
