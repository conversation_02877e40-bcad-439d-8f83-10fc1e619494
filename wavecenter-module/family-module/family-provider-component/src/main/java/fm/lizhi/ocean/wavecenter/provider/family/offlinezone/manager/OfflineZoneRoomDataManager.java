package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager;

import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataHallWeek;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZoneRoomDataBean;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert.OfflineZoneRoomDataConvert;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneDataHallWeekDao;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.GetRoomDataListParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 线下专区厅数据管理器
 * <AUTHOR>
 */
@Component
@Slf4j
public class OfflineZoneRoomDataManager {

    @Autowired
    private OfflineZoneDataHallWeekDao offlineZoneDataHallWeekDao;

     @Autowired
     private UserCommonService userService;

    /**
     * 获取厅数据列表
     *
     * @param queryDTO 查询DTO
     * @return 分页厅数据列表
     */
    public PageBean<OfflineZoneRoomDataBean> getRoomDataList(GetRoomDataListParam queryDTO) {
        // 查询数据
        PageList<OfflineZoneDataHallWeek> pageList = offlineZoneDataHallWeekDao.getRoomDataList(queryDTO);

        // 转换数据
        List<OfflineZoneRoomDataBean> list = OfflineZoneRoomDataConvert.INSTANCE.entityListToBeanList(pageList);


        PageBean<OfflineZoneRoomDataBean> pageBean = PageBean.of(pageList.getTotal(), list);
        // 填充用户信息
        fillUserInfo(queryDTO.getAppId(), pageBean);
        return pageBean;
    }



    /**
     * 填充用户信息
     *
     * @param appId 应用ID
     * @param roomDataBeans 厅数据列表
     */
    private void fillUserInfo(Integer appId, PageBean<OfflineZoneRoomDataBean> roomDataBeans) {
        if (CollectionUtils.isEmpty(roomDataBeans.getList())) {
            return;
        }

        // 收集所有厅主ID
        Set<Long> njIds = roomDataBeans.getList().stream()
                .map(OfflineZoneRoomDataBean::getNjId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(njIds)) {
            return;
        }

        try {
            // 批量获取用户信息
            Map<Long, UserBean> userMap = getUserMap(appId, njIds);

            // 填充用户信息
            roomDataBeans.getList().forEach(roomDataBean -> {
                UserBean userBean = userMap.get(roomDataBean.getNjId());
                if (userBean != null) {
                    roomDataBean.setNjInfo(userBean);
                }
            });
        } catch (Exception e) {
            log.warn("获取用户信息失败, appId={}, njIds={}", appId, njIds, e);
        }
    }

    /**
     * 批量获取用户信息
     *
     * @param appId 应用ID
     * @param njIds 用户ID集合
     * @return 用户信息映射
     */
    private Map<Long, UserBean> getUserMap(Integer appId, Set<Long> njIds) {
        Result<List<UserBean>> result = userService.getUserByIds(appId, new ArrayList<>(njIds));
        if (RpcResult.isSuccess(result)) {
            return result.target().stream()
                    .collect(Collectors.toMap(UserBean::getId, userBean -> userBean, (a, b) -> a));
        }
        return Collections.emptyMap();
    }
}
