package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.datastore.mysql.mybatis.PageList;
import fm.lizhi.common.job.dispatcher.executor.handler.JobExecuteContext;
import fm.lizhi.common.job.dispatcher.executor.handler.JobHandler;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataPlayerWeek;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataPlayerWeekExample;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtectionAgreementValidity;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneDataPlayerWeekMapper;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneProtectionAgreementValidityMapper;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.config.OfflineZoneFamilyConfig;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneProtectionDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 定时同步刷新跳槽保护上传协议时效表数据Job
 * 从offline_zone_data_player_week表查询上周数据，同步到offline_zone_protection_agreement_validity表
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class SyncProtectionAgreementValidityJob implements JobHandler {

    @Autowired
    private OfflineZoneDataPlayerWeekMapper offlineZoneDataPlayerWeekMapper;

    @Autowired
    private OfflineZoneProtectionDao offlineZoneProtectionDao;

    @Autowired
    private OfflineZoneProtectionAgreementValidityMapper validityMapper;

    @Autowired
    private OfflineZoneFamilyConfig offlineZoneFamilyConfig;


    private static final int PAGE_SIZE = 100;
    private static final int BATCH_INSERT_SIZE = 50;

    @Override
    public void execute(JobExecuteContext context) {
        log.info("开始执行定时同步刷新跳槽保护上传协议时效表数据Job");
        
        long startTime = System.currentTimeMillis();
        int totalProcessed = 0;
        int totalInserted = 0;
        int totalSkipped = 0;
        
        try {
            // 获取上周时间范围
            Date lastWeekStart = MyDateUtil.getLastWeekStartDay();
            Date lastWeekEnd = MyDateUtil.getLastWeekEndDay();
            
            log.info("处理上周数据，时间范围：{} 到 {}", 
                DateUtil.formatDateTime(lastWeekStart), DateUtil.formatDateTime(lastWeekEnd));
            
            Integer validityDays = offlineZoneFamilyConfig.getProtectionValidityDay();


            // 分页查询上周数据
            int pageNumber = 1;
            PageList<OfflineZoneDataPlayerWeek> pageList = new PageList<>();
            
            do {
                try {
                    // 构建查询条件
                    OfflineZoneDataPlayerWeekExample example = new OfflineZoneDataPlayerWeekExample();
                    example.createCriteria()
                        .andStartWeekDateEqualTo(lastWeekStart)
                        .andEndWeekDateEqualTo(lastWeekEnd);
                    example.setOrderByClause("id ASC");
                    
                    // 分页查询
                    pageList = offlineZoneDataPlayerWeekMapper.pageByExample(example, pageNumber, PAGE_SIZE);
                    
                    if (CollUtil.isEmpty(pageList)) {
                        log.info("第{}页无数据，结束处理", pageNumber);
                        break;
                    }
                    
                    log.info("处理第{}页数据，共{}条", pageNumber, pageList.size());
                    
                    // 处理当前页数据
                    ProcessResult result = processPageData(pageList, validityDays);
                    totalProcessed += result.processed;
                    totalInserted += result.inserted;
                    totalSkipped += result.skipped;
                    
                    pageNumber++;
                    
                } catch (Exception e) {
                    log.error("处理第{}页数据时发生异常", pageNumber, e);
                    pageNumber++; // 继续处理下一页
                }
                
            } while (pageList.isHasNextPage());
            
        } catch (Exception e) {
            log.error("执行定时同步刷新跳槽保护上传协议时效表数据Job失败", e);
            throw e;
        } finally {
            long endTime = System.currentTimeMillis();
            log.info("定时同步刷新跳槽保护上传协议时效表数据Job执行完成，" +
                "总处理：{}条，新增：{}条，跳过：{}条，耗时：{}ms", 
                totalProcessed, totalInserted, totalSkipped, (endTime - startTime));
        }
    }
    
    /**
     * 处理单页数据
     */
    private ProcessResult processPageData(List<OfflineZoneDataPlayerWeek> playerWeekList, Integer validityDays) {
        ProcessResult result = new ProcessResult();
        
        if (CollUtil.isEmpty(playerWeekList)) {
            return result;
        }
        
        List<OfflineZoneProtectionAgreementValidity> toInsertList = new ArrayList<>();
        
        for (OfflineZoneDataPlayerWeek playerWeek : playerWeekList) {
            result.processed++;
            
            try {
                // 检查是否已存在记录
                OfflineZoneProtectionAgreementValidity existing = offlineZoneProtectionDao
                    .getProtectionAgreementValidity(
                        playerWeek.getAppId(), 
                        playerWeek.getFamilyId(), 
                        playerWeek.getNjId(), 
                        playerWeek.getUserId());

                // 如果已存在记录且不强制更新，则跳过
                if (existing != null) {
                    // 强制更新 && 过期时间小于当前签约时间
                    if (offlineZoneFamilyConfig.getFullUpdateValidityPeriod() && existing.getExpiredTime().before(playerWeek.getBeginSignTime())){
                        existing.setModifyTime(new Date());
                        // 计算过期时间：签约时间 + 有效期天数，设置到当天的23:59:59
                        Date expiredTime = DateUtil.offsetDay(playerWeek.getBeginSignTime(), validityDays);
                        existing.setExpiredTime(DateUtil.endOfDay(expiredTime));
                        validityMapper.updateByPrimaryKey(existing);
                        log.info("强制更新协议有效期，familyId: {}, njId: {}, playerId: {}, expiredTime: {}",
                                playerWeek.getFamilyId(), playerWeek.getNjId(), playerWeek.getUserId(), expiredTime);
                    }else {
                        result.skipped++;
                    }
                    continue;
                }
                
                // 构建新记录
                OfflineZoneProtectionAgreementValidity validity = buildValidityRecord(playerWeek, validityDays);
                toInsertList.add(validity);
                
                // 批量插入
                if (toInsertList.size() >= BATCH_INSERT_SIZE) {
                    int insertCount = batchInsertValidityRecords(toInsertList);
                    result.inserted += insertCount;
                    toInsertList.clear();
                }
                
            } catch (Exception e) {
                log.error("处理主播数据失败，playerId：{}，familyId：{}，njId：{}", 
                    playerWeek.getUserId(), playerWeek.getFamilyId(), playerWeek.getNjId(), e);
            }
        }
        
        // 处理剩余数据
        if (!toInsertList.isEmpty()) {
            int insertCount = batchInsertValidityRecords(toInsertList);
            result.inserted += insertCount;
        }
        
        return result;
    }
    
    /**
     * 构建协议时效记录
     */
    private OfflineZoneProtectionAgreementValidity buildValidityRecord(
            OfflineZoneDataPlayerWeek playerWeek, Integer validityDays) {
        
        // 计算过期时间：签约时间 + 有效期天数，设置到当天的23:59:59
        Date expiredTime = DateUtil.offsetDay(playerWeek.getBeginSignTime(), validityDays);
        expiredTime = DateUtil.endOfDay(expiredTime);
        
        return OfflineZoneProtectionAgreementValidity.builder()
            .appId(playerWeek.getAppId())
            .familyId(playerWeek.getFamilyId())
            .njId(playerWeek.getNjId())
            .playerId(playerWeek.getUserId())
            .beginSignTime(playerWeek.getBeginSignTime())
            .expiredTime(expiredTime)
            .resetsCnt(0)
            .deployEnv(ConfigUtils.getEnvRequired().name())
            .operator("SYSTEM")
            .createTime(new Date())
            .modifyTime(new Date())
            .build();
    }
    
    /**
     * 批量插入协议时效记录
     */
    private int batchInsertValidityRecords(List<OfflineZoneProtectionAgreementValidity> validityList) {
        if (CollUtil.isEmpty(validityList)) {
            return 0;
        }
        
        try {
            int insertCount = validityMapper.batchInsert(validityList);
            log.debug("批量插入协议时效记录成功，插入{}条", insertCount);
            return insertCount;
        } catch (Exception e) {
            log.error("批量插入协议时效记录失败，数据量：{}", validityList.size(), e);
            return 0;
        }
    }
    
    /**
     * 处理结果统计
     */
    private static class ProcessResult {
        int processed = 0;  // 总处理数量
        int inserted = 0;   // 新增数量
        int skipped = 0;    // 跳过数量
    }
}