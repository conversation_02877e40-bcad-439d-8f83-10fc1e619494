package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.common.jsonparser.JsonUtils;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wave.server.common.util.ContextUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataPlayerWeek;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtection;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtectionAgreementValidity;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtectionHistory;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneProtectionAgreementValidityMapper;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.NeedResetPlayer;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.OfflineZonePlayerCategoryEnums;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.OfflineZoneRoomCategoryEnums;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.ProtectionAgreeStatusEnums;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants.ProtectionStatusEnums;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestAddAgreementValidity;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestBatchResetAgreementValidity;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestPlayerHandleAgreement;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestSubmitAgreement;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.config.OfflineZoneFamilyConfig;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert.OfflineZoneProtectionConvert;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneDataPlayerWeekDao;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneProtectionDao;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.OfflineZoneDataPlayerWeekDTO;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.OfflineZoneProtectionDTO;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.OfflineZoneProtectionWithStatusDTO;
import fm.lizhi.ocean.wavecenter.service.sign.manager.NonContractManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneProtectionService.*;

/**
 * 线下专区主播跳槽保护协议管理器
 * <AUTHOR>
 */
@Component
@Slf4j
public class OfflineZoneProtectionManager {

    @Autowired
    private OfflineZoneProtectionDao offlineZoneProtectionDao;

    @Autowired
    private GuidGenerator idGenerator;

    @Autowired
    private OfflineZoneDataPlayerWeekDao offlineZoneDataPlayerWeekDao;

    @Autowired
    private OfflineZoneFamilyConfig offlineZoneFamilyConfig;

    @Autowired
    private OfflineZoneChatManager offlineZoneChatManager;

    @Autowired
    private OfflineZoneProtectionAgreementValidityMapper offlineZoneProtectionAgreementValidityMapper;

    @Autowired
    private NonContractManager nonContractManager;

    /**
     * 查询用户上传协议时效
     */
    public Date getProtectionValidityDate(int appId, Long familyId, Long njId, Long playerId){

        if (ContextUtils.getContext() == null || null == ContextUtils.getBusinessEvnEnum()){
            ContextUtils.setBusinessEvnEnum(BusinessEvnEnum.from(appId));
        }

        Date validityDate = null;
        OfflineZoneProtectionAgreementValidity validity = offlineZoneProtectionDao.getProtectionAgreementValidity(appId, familyId, njId, playerId);
        if (validity != null){
            // 先从平台的时效库里面查询
            validityDate = validity.getExpiredTime();
        }else {
            // 再从大数据表中查询签约时间 + 30 天
            OfflineZoneDataPlayerWeek latestPlayerData = offlineZoneDataPlayerWeekDao.getLatestPlayerData(appId, familyId, njId, playerId);
            // 设置到当天的 23：59：59
            validityDate = latestPlayerData == null ? null : DateUtil.offsetDay(latestPlayerData.getBeginSignTime(), offlineZoneFamilyConfig.getProtectionValidityDay());
        }

        // 还是为空，代表这个主播是本周签约的，查实时的签约时间
        if (validityDate == null){
            validityDate = nonContractManager.getPlayerSignTime(playerId, njId);
            if (validityDate == null){
                log.info("get player sign time from non contract manager is null, appId={}, familyId={}, njId={}, playerId={}",
                        appId, familyId, njId, playerId);
                return null;
            }
            validityDate = DateUtil.offsetDay(validityDate, offlineZoneFamilyConfig.getProtectionValidityDay());
        }

        return validityDate == null ? null : DateUtil.endOfDay(validityDate);
    }


    /**
     * 创建协议
     */
    public Result<Void> createProtection(RequestSubmitAgreement request) {

        // 先检查协议是否已经受保护
        Boolean isExist = offlineZoneProtectionDao.existProtection(request.getAppId(), request.getFamilyId(), request.getNjId(), request.getPlayerId());
        if (isExist) {
            log.info("协议已存在, familyId={}, njId={}, playerId={}", request.getFamilyId(), request.getNjId(), request.getPlayerId());
            return new Result<>(SUBMIT_AGREEMENT_EXIST, null);
        }


        Long id = idGenerator.genId();

        // 构建协议记录
        OfflineZoneProtection protection = OfflineZoneProtectionConvert.INSTANCE.buildProtection(request, id);

        // 构建协议历史记录
        OfflineZoneProtectionHistory protectionHistory = OfflineZoneProtectionConvert.INSTANCE.buildProtectionHistory(protection, id);

        try {
            // 保存协议和历史记录
            offlineZoneProtectionDao.saveProtection(protection, protectionHistory);

            // 发送消息给主播
            offlineZoneChatManager.sendProtectionSubmitMsg(request.getAppId(), request.getPlayerId(), id);

        }catch (Exception e) {
            log.error("保存协议失败, familyId={}, njId={}, playerId={}", request.getFamilyId(), request.getNjId(), request.getPlayerId(), e);
            return new Result<>(SUBMIT_AGREEMENT_FAIL, null);
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    /**
     * 更新协议
     */
    public Result<Void> updateProtection(RequestSubmitAgreement request) {

        if (request == null || request.getId() == null) {
            return new Result<>(SUBMIT_AGREEMENT_PARAM_ERROR, null);
        }

        OfflineZoneProtection protection = offlineZoneProtectionDao.getProtectionById(request.getId());
        if (protection == null) {
            log.info("协议不存在, id={}", request.getId());
            return new Result<>(SUBMIT_AGREEMENT_NOT_EXIST, null);
        }

        protection = OfflineZoneProtectionConvert.INSTANCE.buildUpdateProtection(protection, request);

        // 构建协议历史记录
        OfflineZoneProtectionHistory protectionHistory = OfflineZoneProtectionConvert.INSTANCE.buildProtectionHistory(protection, protection.getId());

        try {
            offlineZoneProtectionDao.updateProtection(protection, protectionHistory);
        } catch (Exception e) {
            log.error("更新协议失败, familyId={}, njId={}, playerId={}", request.getFamilyId(), request.getNjId(), request.getPlayerId(), e);
            return new Result<>(SUBMIT_AGREEMENT_FAIL, null);
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    /**
     * 主播处理协议（同意或拒绝）
     */
    public Result<Void> playerHandleProtection(RequestPlayerHandleAgreement request) {

        ProtectionAgreeStatusEnums agreeStatusEnum = ProtectionAgreeStatusEnums.getByCode(request.getAgreeStatus());

        // 2. 查询协议是否存在
        OfflineZoneProtection protection = offlineZoneProtectionDao.getProtectionById(request.getProtectionId());
        if (protection == null) {
            log.info("主播处理协议失败, 协议不存在, protectionId={}", request.getProtectionId());
            return new Result<>(PLAYER_HANDLE_PROTECTION_NOT_FOUND, null);
        }

        // 3. 校验协议是否属于该主播
        if (!protection.getPlayerId().equals(request.getPlayerId())) {
            log.info("主播处理协议失败, 协议不属于该主播, protectionId={}, playerId={}, protectionPlayerId={}",
                    request.getProtectionId(), request.getPlayerId(), protection.getPlayerId());
            return new Result<>(PLAYER_HANDLE_PROTECTION_NOT_FOUND, null);
        }


        // 4. 校验协议当前状态是否允许处理（必须是未处理状态）
        if (!ProtectionAgreeStatusEnums.NOT_PROCESSED.getCode().equals(protection.getPlayerAgree())) {
            log.info("主播处理协议失败, 协议已处理, protectionId={}, currentStatus={}",
                    request.getProtectionId(), protection.getPlayerAgree());
            return new Result<>(PLAYER_HANDLE_ALREADY_PROCESSED, null);
        }

        if (request.getVerifyValidityDate()){
            // 5.校验是否在时效内
            Date validDate = getProtectionValidityDate(request.getAppId(), protection.getFamilyId(), protection.getNjId(), request.getPlayerId());
            if (validDate == null || validDate.before(new Date())) {
                log.info("协议已过期, familyId={}, njId={}, playerId={}", protection.getFamilyId(), protection.getNjId(), request.getPlayerId());
                return new Result<>(PLAYER_HANDLE_EXPIRED, null);
            }
        }


        try {

            // 6. 更新协议状态
            OfflineZoneDataPlayerWeek latestPlayerData = offlineZoneDataPlayerWeekDao.getLatestPlayerData(request.getAppId(), protection.getFamilyId(), protection.getNjId(), request.getPlayerId());
            boolean canUpdateRealTimeDate = latestPlayerData != null && OfflineZonePlayerCategoryEnums.OFFLINE_PLAYER.getCategory() == latestPlayerData.getCategory();
            offlineZoneProtectionDao.updateProtectionPlayerAgree(request.getProtectionId(), request.getAgreeStatus(), protection, canUpdateRealTimeDate);

            // 7. 发送消息给相关方
            offlineZoneChatManager.batchSendProtectionPlayerHandleMsg(request.getAppId(), request.getPlayerId(), request.getProtectionId(), agreeStatusEnum);

        } catch (Exception e) {
            log.error("主播处理协议失败, protectionId={}, playerId={}, agreeStatus={}",
                    request.getProtectionId(), request.getPlayerId(), request.getAgreeStatus(), e);
            return new Result<>(PLAYER_HANDLE_FAIL, null);
        }

        return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
    }

    /**
     * 根据协议ID获取协议详情
     * @param id 协议ID
     * @return 协议详情
     */
    public OfflineZoneProtectionDTO getProtectionDetail(Long id) {
        OfflineZoneProtection protection = offlineZoneProtectionDao.getProtectionById(id);
        return OfflineZoneProtectionConvert.INSTANCE.convertToProtectionDTO(protection);
    }

    /**
     * 获取主播身份信息
     *
     * @param playerId 主播ID
     * @param njId
     * @param familyId
     * @return 主播身份信息
     */
    public OfflineZoneDataPlayerWeekDTO getPlayerIdentityInfo(int appId, Long familyId, Long njId, Long playerId) {
        OfflineZoneDataPlayerWeek latestPlayerData = offlineZoneDataPlayerWeekDao.getLatestPlayerData(appId, familyId, njId, playerId);

        return OfflineZoneProtectionConvert.INSTANCE.convertOfflineZoneDataPlayerWeekDto(latestPlayerData);
    }

    /**
     * 获取用户的跳槽保护状态
     *
     * @param appId 应用ID
     * @param familyId 公会ID
     * @param njId 厅ID
     * @param playerId 主播ID
     * @return 保护状态DTO
     */
    public OfflineZoneProtectionWithStatusDTO getProtectionStatus(Integer appId, Long familyId, Long njId, Long playerId) {
        Map<Long, OfflineZoneProtectionWithStatusDTO> map = batchGetProtectionStatus(appId, familyId, njId, Collections.singleton(playerId));
        return map.get(playerId);
    }


    /**
     * 批量查询用户的跳槽保护状态
     *
     * @param appId 应用ID
     * @param familyId 公会ID
     * @param njId 厅ID
     * @param playerIds 主播ID集合
     * @return 主播ID -> 保护协议与状态组合DTO的映射
     */
    public Map<Long, OfflineZoneProtectionWithStatusDTO> batchGetProtectionStatus(Integer appId, Long familyId, Long njId, Set<Long> playerIds) {
        if (playerIds == null || playerIds.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<Long, OfflineZoneProtectionWithStatusDTO> resultMap = new HashMap<>();

        // 批量查询保护协议
        Map<Long, OfflineZoneProtection> protectionMap = offlineZoneProtectionDao.getProtectionMapByPlayerIds(appId, familyId, njId, playerIds);

        // 为每个主播计算保护状态
        for (Long playerId : playerIds) {
            OfflineZoneProtection protection = protectionMap.get(playerId);
            ProtectionStatusEnums protectionStatusEnum = calculateProtectionStatusValue(appId, familyId, njId, playerId, protection);
            Integer protectionStatus = protectionStatusEnum.getCode();

            OfflineZoneProtectionWithStatusDTO withStatusDTO = new OfflineZoneProtectionWithStatusDTO()
                    .setPlayerId(playerId)
                    .setProtectionStatus(protectionStatus)
                    .setProtection(protectionStatusEnum == ProtectionStatusEnums.PLAYER_CONFIRMED);

            if (protection != null) {
                // 如果存在保护协议
                OfflineZoneProtectionDTO protectionDTO = OfflineZoneProtectionConvert.INSTANCE.convertToProtectionDTO(protection);
                withStatusDTO.setProtectionDetail(protectionDTO);
            }
            // 如果不存在保护协议，protectionDetail 保持为 null

            resultMap.put(playerId, withStatusDTO);
        }

        return resultMap;
    }


    /**
     * 计算保护状态值的核心逻辑
     * <p>
     * !!! 注意，修改这里的逻辑，也需要同步修改 OfflineZoneProtectionWithStatusExtMapper.xml 中的 SQL 逻辑
     *
     * @param appId 应用ID
     * @param familyId 公会ID
     * @param njId 厅ID
     * @param playerId 主播ID
     * @param protection 保护协议（可能为null）
     * @return 保护状态枚举
     */
    private ProtectionStatusEnums calculateProtectionStatusValue(Integer appId, Long familyId, Long njId, Long playerId, OfflineZoneProtection protection) {

        // 状态5: 上传逾期 - offline_zone_protection_agreement_validity.expired_time <= 当前时间
        Date validityDate = getProtectionValidityDate(appId, familyId, njId, playerId);
        // 逾期了，再细分为什么逾期
        if (validityDate != null && validityDate.before(new Date())
                // 1. 没传协议
                // 2. 传了协议但是主播拒绝了
                && (protection == null || ProtectionAgreeStatusEnums.REJECTED.getCode().equals(protection.getPlayerAgree()))) {
            return ProtectionStatusEnums.UPLOAD_EXPIRED;
        }

        // 状态0: 未上传 - 跳槽保护表不存在数据
        if (protection == null) {
            return ProtectionStatusEnums.NOT_UPLOADED;
        }

        // 根据 player_agree 和 archived 字段判断状态
        Integer playerAgree = protection.getPlayerAgree();
        Boolean archived = protection.getArchived();

        // 状态1: 已上传，待主播确认 - player_agree = -1 && archived = false
        if (ProtectionAgreeStatusEnums.NOT_PROCESSED.getCode().equals(playerAgree) && Boolean.FALSE.equals(archived)) {
            return ProtectionStatusEnums.UPLOADED_PENDING_CONFIRMATION;
        }

        // 状态2: 已上传，主播逾期未确认 - player_agree = -1 && archived = true
        if (ProtectionAgreeStatusEnums.NOT_PROCESSED.getCode().equals(playerAgree) && Boolean.TRUE.equals(archived)) {
            return ProtectionStatusEnums.UPLOADED_OVERDUE_CONFIRMATION;
        }

        // 状态3: 主播确认 - player_agree = 1 && archived = true
        if (ProtectionAgreeStatusEnums.AGREED.getCode().equals(playerAgree) && Boolean.TRUE.equals(archived)) {
            return ProtectionStatusEnums.PLAYER_CONFIRMED;
        }

        // 状态4: 主播拒绝 - player_agree = 0 && archived = true
        if (ProtectionAgreeStatusEnums.REJECTED.getCode().equals(playerAgree) && Boolean.TRUE.equals(archived)) {
            return ProtectionStatusEnums.PLAYER_REJECTED;
        }

        // 默认返回未上传状态
        return ProtectionStatusEnums.NOT_UPLOADED;
    }


    /**
     * 批量重置协议有效期
     * @param request
     * @return
     */
    public Boolean batchResetAgreementValidity(RequestBatchResetAgreementValidity request) {

        try {

            // 在事务外先把已存在的查出来。
            List<OfflineZoneProtectionAgreementValidity> existList = offlineZoneProtectionDao.getExistAgreementValidity(request.getAppId(), request.getPlayerList());
            
            // 过滤出 request.getPlayerList 中不存在于 existList 中的元素
            List<NeedResetPlayer> notExistList = request.getPlayerList().stream()
                .filter(player -> existList.stream()
                    .noneMatch(exist -> exist != null && 
                        Objects.equals(exist.getPlayerId(), player.getPlayerId()) &&
                        Objects.equals(exist.getFamilyId(), player.getFamilyId()) &&
                        Objects.equals(exist.getNjId(), player.getNjId())))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            // 构建需要新增的数据
            List<OfflineZoneProtectionAgreementValidity> instertList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(notExistList)) {
                Date expiredTime = DateUtil.offsetDay(new Date(), offlineZoneFamilyConfig.getProtectionResetDay());
                instertList = notExistList.stream().map(e -> {
                    OfflineZoneDataPlayerWeek playerData = offlineZoneDataPlayerWeekDao.getLatestPlayerData(request.getAppId(), e.getFamilyId(), e.getNjId(), e.getPlayerId());
                    return OfflineZoneProtectionConvert.INSTANCE.buildAgreementValidity(e, playerData.getBeginSignTime(), DateUtil.endOfDay(expiredTime), request);
                }).collect(Collectors.toList());
            }

            return offlineZoneProtectionDao.batchUpdateAgreementValidity(request.getOperator(), existList, instertList, request.getPlayerList().size());
        } catch (Exception e) {
            log.error("批量重置协议有效期失败, request={}", JsonUtils.toJsonString(request), e);
            return false;
        }

    }

    /**
     * 新增协议时效记录
     * 用于签约成功时自动创建协议时效数据
     */
    public Result<Void> addAgreementValidity(RequestAddAgreementValidity request) {
        try {
            // 检查是否已存在记录
            OfflineZoneProtectionAgreementValidity existing = offlineZoneProtectionDao
                .getProtectionAgreementValidity(
                    request.getAppId(), 
                    request.getFamilyId(), 
                    request.getNjId(), 
                    request.getPlayerId());
            
            if (existing != null) {
                log.info("协议时效记录已存在, appId={}, familyId={}, njId={}, playerId={}", 
                    request.getAppId(), request.getFamilyId(), request.getNjId(), request.getPlayerId());
                return new Result<>(ADD_AGREEMENT_VALIDITY_EXIST, null);
            }
            
            // 计算过期时间：签约时间 + 有效期天数，设置到当天的23:59:59
            Date expiredTime = DateUtil.offsetDay(request.getBeginSignTime(), offlineZoneFamilyConfig.getProtectionValidityDay());
            expiredTime = DateUtil.endOfDay(expiredTime);
            
            // 构建协议时效记录
            OfflineZoneProtectionAgreementValidity validity = OfflineZoneProtectionAgreementValidity.builder()
                .appId(request.getAppId())
                .familyId(request.getFamilyId())
                .njId(request.getNjId())
                .playerId(request.getPlayerId())
                .beginSignTime(request.getBeginSignTime())
                .expiredTime(expiredTime)
                .resetsCnt(0)
                .deployEnv(ConfigUtils.getEnvRequired().name())
                .operator("CONSUMER")
                .createTime(new Date())
                .modifyTime(new Date())
                .build();
            
            // 插入记录
            int insertCount = offlineZoneProtectionAgreementValidityMapper.insert(validity);
            if (insertCount <= 0) {
                log.error("新增协议时效记录失败, appId={}, familyId={}, njId={}, playerId={}", 
                    request.getAppId(), request.getFamilyId(), request.getNjId(), request.getPlayerId());
                return new Result<>(ADD_AGREEMENT_VALIDITY_FAIL, null);
            }
            
            log.info("新增协议时效记录成功, appId={}, familyId={}, njId={}, playerId={}, expiredTime={}", 
                request.getAppId(), request.getFamilyId(), request.getNjId(), request.getPlayerId(), expiredTime);
            
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, null);
            
        } catch (Exception e) {
            log.error("新增协议时效记录异常, appId={}, familyId={}, njId={}, playerId={}", 
                request.getAppId(), request.getFamilyId(), request.getNjId(), request.getPlayerId(), e);
            return new Result<>(ADD_AGREEMENT_VALIDITY_FAIL, null);
        }
    }

}
