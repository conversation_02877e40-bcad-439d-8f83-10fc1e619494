package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.service;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.MapDataBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.RegionBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestDataMonitorFamilySummary;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestDataMonitorMapData;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestDataMonitorRoomSummary;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestRegion;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ResponseDataMonitorFamilySummary;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ResponseDataMonitorRoomSummary;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneDataMonitorService;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager.OfflineZoneDataMonitorManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 线下数据监控
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class OfflineZoneDataMonitorServiceImpl implements OfflineZoneDataMonitorService {

    @Autowired
    private OfflineZoneDataMonitorManager offlineZoneDataMonitorManager;

    @Override
    public Result<ResponseDataMonitorFamilySummary> getFamilySummary(RequestDataMonitorFamilySummary request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));
        ResponseDataMonitorFamilySummary response = offlineZoneDataMonitorManager.getFamilySummary(
                request.getAppId(),
                request.getFamilyId()
        );

        return RpcResult.success(response);
    }

    @Override
    public Result<ResponseDataMonitorRoomSummary> getRoomSummary(RequestDataMonitorRoomSummary request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));

        ResponseDataMonitorRoomSummary response = offlineZoneDataMonitorManager.getRoomSummary(
                request.getAppId(),
                request.getFamilyId(),
                request.getRoomId()
        );

        return RpcResult.success(response);
    }

    @Override
    public Result<List<MapDataBean>> getMapData(RequestDataMonitorMapData request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));

        List<MapDataBean> response = offlineZoneDataMonitorManager.getMapData(
                request.getAppId(),
                request.getFamilyId()
        );

        return RpcResult.success(response);
    }

    @Override
    public Result<List<RegionBean>> getRegionList(RequestRegion request) {
        LogContext.addReqLog("request={}", JsonUtils.toJsonString(request));
        LogContext.addResLog("request={}", JsonUtils.toJsonString(request));

        return RpcResult.success(offlineZoneDataMonitorManager.getRegionList(request.getAppId()));

    }
}
