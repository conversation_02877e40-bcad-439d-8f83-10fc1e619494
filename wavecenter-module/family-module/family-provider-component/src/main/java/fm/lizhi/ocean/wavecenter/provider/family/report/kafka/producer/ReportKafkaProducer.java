package fm.lizhi.ocean.wavecenter.provider.family.report.kafka.producer;

import fm.lizhi.common.kafka.common.SendResult;
import fm.lizhi.common.kafka.ioc.api.KafkaTemplate;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.content.review.constant.OperationMarkEnum;
import fm.lizhi.ocean.wavecenter.provider.family.report.config.ReportKafkaTopicConstants;
import fm.lizhi.ocean.wavecenter.provider.family.report.convert.ReportAuditMessageConvert;
import fm.lizhi.ocean.wavecenter.provider.family.report.dto.ReportBanDeviceAuditDTO;
import fm.lizhi.ocean.wavecenter.provider.family.report.dto.ReportBanLiveAuditDTO;
import fm.lizhi.ocean.wavecenter.provider.family.report.dto.ReportBanUserAuditDTO;
import fm.lizhi.ocean.wavecenter.provider.family.report.kafka.message.ReportBanAuditKafkaMessage;
import fm.lizhi.ocean.wavecenter.provider.family.report.kafka.message.ReportRiskKafkaMessage;
import fm.lizhi.ocean.wavecenter.provider.family.report.kafka.message.ReportSyncAuditPunishMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ReportKafkaProducer {

    @Autowired
    private KafkaTemplate publicKafkaTemplate;

    /**
     * 发送消息
     *
     * @param topic 主题
     * @param key   键
     * @param msg   消息
     */
    public boolean send(String topic, String key, String msg) {
        SendResult sendResult = publicKafkaTemplate.send(topic, key, msg);
        if (sendResult.isSuccess()) {
            log.info("ReportKafkaProducer send success - topic:{}, key:{}, msg:{}", topic, key, msg);
        } else {
            log.error("ReportKafkaProducer send fail - topic:{}, key:{}, msg:{}", topic, key, msg);
        }
        return sendResult.isSuccess();
    }

    /**
     * 发送封禁用户操作消息
     *
     * @param auditDTO 参数
     * @return 发送结果
     */
    public boolean sendBanUserOperateMessage(ReportBanUserAuditDTO auditDTO) {
        try {
            ReportBanAuditKafkaMessage kafkaMessage = ReportAuditMessageConvert.INSTANCE.toReportBanAuditKafkaMessage(auditDTO);
            kafkaMessage.setOp(OperationMarkEnum.BLOCK_USER.getOp());
            return send(ReportKafkaTopicConstants.BAN_USER_OPERATE_TOPIC, String.valueOf(auditDTO.getUserId()), JsonUtil.dumps(kafkaMessage));
        } catch (Exception e) {
            log.error("ReportKafkaProducer sendBanUserOperateMessage failed - unexpected error, auditMessage:{}", JsonUtil.dumps(auditDTO), e);
            return false;
        }
    }

    /**
     * 发送封禁用户操作消息
     *
     * @param auditDTO 参数
     * @return 发送结果
     */
    public boolean sendBanLiveOperateMessage(ReportBanLiveAuditDTO auditDTO) {
        try {
            ReportBanAuditKafkaMessage kafkaMessage = ReportAuditMessageConvert.INSTANCE.toReportBanAuditKafkaMessage(auditDTO);
            kafkaMessage.setOp(OperationMarkEnum.LIVE_BAN.getOp());
            return send(ReportKafkaTopicConstants.BAN_USER_OPERATE_TOPIC, String.valueOf(auditDTO.getUserId()), JsonUtil.dumps(kafkaMessage));
        } catch (Exception e) {
            log.error("ReportKafkaProducer sendAuditMessage failed - unexpected error, auditMessage:{}", JsonUtil.dumps(auditDTO), e);
            return false;
        }
    }

    /**
     * 发送封禁设备的操作消息
     */
    public boolean sendBanDeviceOperateMessage(ReportBanDeviceAuditDTO operateDTO) {
        try {
            ReportRiskKafkaMessage riskKafkaMessage = ReportAuditMessageConvert.INSTANCE.toReportRiskKafkaMessage(operateDTO);
            return send(ReportKafkaTopicConstants.BAN_DEVICE_OPERATE_TOPIC, operateDTO.getDeviceId(), JsonUtil.dumps(riskKafkaMessage));
        } catch (Exception e) {
            log.error("ReportKafkaProducer sendBanDeviceOperateMessage failed - unexpected error, auditMessage:{}", JsonUtil.dumps(operateDTO), e);
            return false;
        }
    }

    public boolean sendReportSyncAuditPunishMessage(ReportSyncAuditPunishMessage message) {
        try {
            return send(ReportKafkaTopicConstants.SYNC_AUDIT_SUCCESS_REPORT_MESSAGE, message.getContentId(), JsonUtil.dumps(message));
        } catch (Exception e) {
            log.error("ReportKafkaProducer sendBanDeviceOperateMessage failed - unexpected error, auditMessage:{}", JsonUtil.dumps(message), e);
            return false;
        }
    }
}
