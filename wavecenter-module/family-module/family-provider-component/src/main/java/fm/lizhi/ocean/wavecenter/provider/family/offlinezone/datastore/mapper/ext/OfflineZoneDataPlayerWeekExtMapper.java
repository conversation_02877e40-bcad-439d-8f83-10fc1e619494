package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.RegionAggregateDTO;

import java.util.Date;
import java.util.List;

/**
 * 线下区域主播周数据扩展Mapper
 * 用于自定义SQL查询
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@DataStore(namespace = "mysql_ocean_wavecenter")
public interface OfflineZoneDataPlayerWeekExtMapper {

    /**
     * 查询省市聚合数据
     * @param startWeekDate 开始周日期
     * @param endWeekDate 结束周日期
     * @return 省市聚合数据列表
     */
    @Select("SELECT province, city " +
            "FROM offline_zone_data_player_week " +
            "WHERE start_week_date = #{startWeekDate} AND end_week_date = #{endWeekDate} " +
            "AND app_id = #{appId} " +
            "AND province IS NOT NULL AND city IS NOT NULL " +
            "GROUP BY province, city " +
            "ORDER BY province, city")
    List<RegionAggregateDTO> selectRegionAggregateData(@Param("appId") Integer appId, @Param("startWeekDate") Date startWeekDate, @Param("endWeekDate") Date endWeekDate);

}