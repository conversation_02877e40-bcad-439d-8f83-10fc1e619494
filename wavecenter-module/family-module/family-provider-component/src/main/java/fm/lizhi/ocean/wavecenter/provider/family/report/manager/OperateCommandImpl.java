package fm.lizhi.ocean.wavecenter.provider.family.report.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.message.constant.MessageTypeEnum;
import fm.lizhi.ocean.wavecenter.api.message.request.RequestSendMessage;
import fm.lizhi.ocean.wavecenter.api.user.bean.FamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.common.constant.AuditLiveTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.OperateObjTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.OperateTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.model.DeviceInfoTarget;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Player;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Room;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.Operate;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.ReportPlayerOperateItem;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.ReportRoomOperateItem;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.OperateCommand;
import fm.lizhi.ocean.wavecenter.provider.family.report.config.PlayerReportConfig;
import fm.lizhi.ocean.wavecenter.provider.family.report.constant.PlayerReportConstant;
import fm.lizhi.ocean.wavecenter.provider.family.report.dto.ReportBanDeviceAuditDTO;
import fm.lizhi.ocean.wavecenter.provider.family.report.dto.ReportBanLiveAuditDTO;
import fm.lizhi.ocean.wavecenter.provider.family.report.dto.ReportBanUserAuditDTO;
import fm.lizhi.ocean.wavecenter.provider.family.report.kafka.producer.ReportKafkaProducer;
import fm.lizhi.ocean.wavecenter.service.activitycenter.dto.GetRoomInfoByNjIdDTO;
import fm.lizhi.ocean.wavecenter.service.common.manager.ChatManager;
import fm.lizhi.ocean.wavecenter.service.live.manager.LiveManager;
import fm.lizhi.ocean.wavecenter.service.message.manager.WaveCenterMessageManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import fm.lizhi.ocean.wavecenter.service.user.dto.UserBanDetailBean;
import fm.lizhi.ocean.wavecenter.service.user.manager.UserManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Optional;

@Slf4j
@Component
public class OperateCommandImpl implements OperateCommand {

    @Autowired
    private WaveCenterMessageManager waveCenterMessageManager;

    @Autowired
    private PlayerReportConfig playerReportConfig;

    @Autowired
    private ChatManager chatManager;

    @Autowired
    private FamilyManager familyManager;

    @Autowired
    private ReportKafkaProducer reportKafkaProducer;

    @Autowired
    private LiveManager liveManager;

    @Autowired
    private UserManager userManager;

    @Override
    public boolean markUserBanStatus(Integer appId, ReportPlayerOperateItem operateItem, String operator, Long accusedRoomId, String operateReason) {
        Player operatedPlayer = operateItem.getOperatedPlayer();
        Operate item = operateItem.getOperate();
        if (item.getOperateType() != OperateTypeEnum.BAN) {
            log.info("markUserBanStatus.operate type is not ban. appId={}; userId={}; item:{}", appId, operatedPlayer.getId(), JsonUtils.toJsonString(item));
            return true;
        }
        //计算过期时间
        Integer operateDuration = item.getOperateDuration();
        long invalidTimestamp = 0;
        UserBanDetailBean userBanDetail = userManager.getUserBanDetail(operatedPlayer.getId());
        //已经被封禁了，且不是永久封禁，在此基础上累加封禁时长
        Date unBlockTime = userBanDetail.isBan() && userBanDetail.getUnBlockTime() != null && userBanDetail.getUnBlockTime() > 0 ? new Date(userBanDetail.getUnBlockTime()) : new Date();
        invalidTimestamp = operateDuration == -1 ? 0 : DateUtils.addMinutes(unBlockTime, operateDuration).getTime();
        if (item.getOperateObjType() == OperateObjTypeEnum.ACCOUNT) {
            long liveId = 0L;
            long liveRoomId = 0L;
            Result<GetRoomInfoByNjIdDTO> roomInfoRes = liveManager.getRoomInfoByNjId(accusedRoomId);
            if (RpcResult.isSuccess(roomInfoRes)) {
                log.info("markUserBanStatus.getRoomInfoByNjId，appId:{}, njId:{}, code:{}", appId, accusedRoomId, roomInfoRes.target());
                liveRoomId = roomInfoRes.target().getId();
                Optional<Long> latestLiveIdOption = liveManager.getLatestLiveIdByUserId(roomInfoRes.target().getUserId());
                liveId = latestLiveIdOption.orElse(0L);
            }
            ReportBanUserAuditDTO auditDTO = ReportBanUserAuditDTO.builder()
                    .appEnv(BusinessEvnEnum.from(appId))
                    .opUser(operator)
                    .invalidTimestamp(invalidTimestamp)
                    .reason(PlayerReportConstant.PLAYER_REPORT_SUBMIT_AUDIT_REASON)
                    .transactionId(String.valueOf(operateItem.getId()))
                    .userId(operatedPlayer.getId())
                    .liveId(liveId)
                    .liveType(AuditLiveTypeEnum.getByAppId(appId).getLiveType())
                    .liveRoomId(liveRoomId)
                    .remarks(operateReason)
                    .build();
            //封禁账号
            return reportKafkaProducer.sendBanUserOperateMessage(auditDTO);
        } else if (item.getOperateObjType() == OperateObjTypeEnum.DEVICE_ID) {
            DeviceInfoTarget deviceInfo = operatedPlayer.getDeviceInfo();
            boolean pcRes = true;
            boolean appRes = true;
            if (operatedPlayer.hasPcDeviceInfo()) {
                ReportBanDeviceAuditDTO pcAuditDTO = ReportBanDeviceAuditDTO.builder()
                        .appEnv(BusinessEvnEnum.from(appId))
                        .duration(operateDuration == -1 ? -1 : (int) item.getInvalidDate().getTime())
                        .remark(PlayerReportConstant.PLAYER_REPORT_SUBMIT_AUDIT_REASON)
                        .color(2)
                        .transactionId(operateItem.getId() + "pc")
                        .operator(operator)
                        .deviceId(deviceInfo.getPcDeviceId())
                        .build();
                pcRes = reportKafkaProducer.sendBanDeviceOperateMessage(pcAuditDTO);
            }

            if (operatedPlayer.hasAppDeviceInfo()) {
                ReportBanDeviceAuditDTO appAuditDTO = ReportBanDeviceAuditDTO.builder()
                        .appEnv(BusinessEvnEnum.from(appId))
                        .duration(operateDuration == -1 ? -1 : (int) item.getInvalidDate().getTime())
                        .remark(PlayerReportConstant.PLAYER_REPORT_SUBMIT_AUDIT_REASON)
                        .color(2)
                        .transactionId(operateItem.getId() + "app")
                        .operator(operator)
                        .deviceId(deviceInfo.getAppDeviceId())
                        .build();

                appRes = reportKafkaProducer.sendBanDeviceOperateMessage(appAuditDTO);
            }

            return appRes && pcRes;
        }
        return true;
    }

    @Override
    public boolean markRoomBanStatus(Integer appId, ReportRoomOperateItem operateItem, String operator) {
        Room operatedRoom = operateItem.getOperatedRoom();
        Operate item = operateItem.getOperate();
        if (item.getOperateType() != OperateTypeEnum.BAN && item.getOperateObjType() != OperateObjTypeEnum.ROOM_OPEN_LIVE) {
            log.info("markRoomBanStatus.operate room type is not ban. appId={}; njId={}; item:{}", appId, operatedRoom.getId(), JsonUtils.toJsonString(item));
            return true;
        }
        Result<GetRoomInfoByNjIdDTO> roomInfoRes = liveManager.getRoomInfoByNjId(operatedRoom.getId());
        if (RpcResult.isFail(roomInfoRes)) {
            log.warn("markRoomBanStatus.getRoomInfoByNjId fail，appId:{}, njId:{}, code:{}", appId, operatedRoom.getId(), roomInfoRes.target());
            return false;
        }
        Optional<Long> latestLiveIdOption = liveManager.getLatestLiveIdByUserId(roomInfoRes.target().getUserId());
        Integer operateDuration = item.getOperateDuration();
        //计算过期时间
        ReportBanLiveAuditDTO auditDTO = ReportBanLiveAuditDTO.builder()
                .appEnv(BusinessEvnEnum.from(appId))
                .opUser(operator)
                .invalidTimestamp(operateDuration == -1 ? 0 : item.getInvalidDate().getTime())
                .reason(PlayerReportConstant.PLAYER_REPORT_SUBMIT_AUDIT_REASON)
                .transactionId(String.valueOf(operateItem.getId()))
                .liveId(latestLiveIdOption.orElse(0L))
                .liveType(AuditLiveTypeEnum.getByAppId(appId).getLiveType())
                .liveRoomId(roomInfoRes.target().getId())
                .userId(operatedRoom.getId())
                .build();
        //封禁账号
        return reportKafkaProducer.sendBanLiveOperateMessage(auditDTO);
    }

    @Override
    public boolean sendWebSystemMessage(Integer appId, Long userId, String title, String content) {
        if (StringUtils.isEmpty(content)) {
            log.warn("sendWebSystemMessage fail, content is empty, appId={},userId={},title={},content={}", appId, userId, title, content);
            return false;
        }
        Long sendMessageOfficialUserId = playerReportConfig.getBizConfig(appId).getSendMessageOfficialUserId();
        RequestSendMessage message = new RequestSendMessage()
                .setAppId(appId)
                .setTitle(title)
                .setContent(content)
                .setTargetUserId(userId)
                .setType(MessageTypeEnum.SYSTEM.getType())
                .setSendUserId(sendMessageOfficialUserId);
        Long messageId = waveCenterMessageManager.sendMessage(message);
        log.info("sendWebMessage finish，userId:{}, content:{}, title:{}, messageId:{}", userId, content, title, messageId);
        return messageId != null;
    }

    @Override
    public void sendUserMessage(Integer appId, Long receiverId, String content) {
        Long sendMessageOfficialUserId = playerReportConfig.getBizConfig(appId).getSendMessageOfficialUserId();
        chatManager.sendChatAsync(receiverId, sendMessageOfficialUserId, content);
    }

    @Override
    public void sendFamilyMessage(Integer appId, Long familyId, String title, String content) {
        Optional<FamilyBean> familyByCache = familyManager.getFamilyByCache(familyId);
        if (!familyByCache.isPresent()) {
            log.error("Failed to send family message, appId={}, familyId={}", appId, familyId);
            return;
        }
        this.sendWebSystemMessage(appId, familyByCache.get().getUserId(), title, content);
        this.sendUserMessage(appId, familyByCache.get().getUserId(), content);
    }

}
