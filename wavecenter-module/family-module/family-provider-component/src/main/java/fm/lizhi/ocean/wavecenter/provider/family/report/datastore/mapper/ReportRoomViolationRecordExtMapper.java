package fm.lizhi.ocean.wavecenter.provider.family.report.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface ReportRoomViolationRecordExtMapper {



    @Update("<script>\n" +
            "  UPDATE `report_room_violation_record`\n" +
            "  set `punished` = #{punished}, `modify_time` = NOW()\n" +
            "  WHERE `id` IN\n" +
            "    <foreach collection=\"ids\" item=\"id\" open=\"(\" separator=\",\" close=\")\">\n" +
            "      #{id}\n" +
            "    </foreach>\n" +
            "</script>")
    int batchUpdateRoomViolationRecordPunished( @Param("ids") List<Long> ids, @Param("punished") Integer punished);

    @Select("SELECT MAX(operate_seq) FROM `report_room_violation_operate_result`\n" +
            "WHERE `accused_room_id` = #{roomId}\n" +
            "  AND `create_time` >= #{startTime}\n" +
            "  AND `create_time` < #{endTime} AND `deploy_env` = #{env}")
    Integer getMaxOperateSeqByRoom(@Param("roomId")Long roomId, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("env") String env);
}
