package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert;

import fm.lizhi.ocean.wavecenter.base.convert.CommonConvert;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelConfig;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRightRelation;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListLevelConfigBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.SimpleLevelBean;
import org.apache.commons.collections4.ListValuedMap;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;

/**
 * 线下专区等级配置转换器
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR,
        uses = {
                CommonConvert.class,
        }
)
public interface OfflineZoneLevelConfigConvert {

    /**
     * 转换为列出等级配置的bean列表
     *
     * @param entities               等级配置实体列表
     * @param levelRightRelationsMap 等级配置权益关联列表Map, key: 等级配置ID, value: 权益关联列表
     * @return 列出等级配置的bean列表
     */
    List<ListLevelConfigBean> toListLevelConfigBeans(List<OfflineZoneLevelConfig> entities, @Context ListValuedMap<Long, OfflineZoneLevelRightRelation> levelRightRelationsMap);

    @Mapping(target = "rights", source = "entity.id", qualifiedByName = "getListLevelConfigBeanRights")
    ListLevelConfigBean toListLevelConfigBean(OfflineZoneLevelConfig entity, @Context ListValuedMap<Long, OfflineZoneLevelRightRelation> levelRightRelationsMap);

    @Named("getListLevelConfigBeanRights")
    default List<ListLevelConfigBean.Right> getListLevelConfigBeanRights(Long levelId, @Context ListValuedMap<Long, OfflineZoneLevelRightRelation> levelRightRelationsMap) {
        if (levelId == null || levelRightRelationsMap == null) {
            return Collections.emptyList();
        }
        List<OfflineZoneLevelRightRelation> relations = levelRightRelationsMap.get(levelId);
        return toListLevelConfigBeanRights(relations);
    }

    List<ListLevelConfigBean.Right> toListLevelConfigBeanRights(List<OfflineZoneLevelRightRelation> entities);

    @Mapping(target = "unlockedLevelId", expression = "java(entity.getUnlockedLevelId() == 0L ? null : entity.getUnlockedLevelId())")
    ListLevelConfigBean.Right toListLevelConfigBeanRight(OfflineZoneLevelRightRelation entity);

    SimpleLevelBean convertToSimpleLevelBean(OfflineZoneLevelConfig defaultLevel);

}
