package fm.lizhi.ocean.wavecenter.provider.family.report.listener;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.common.validation.BeanValidator;
import fm.lizhi.ocean.wavecenter.common.validation.ValidateResult;
import fm.lizhi.ocean.wavecenter.datastore.family.report.entity.ReportPlayerApplyRecord;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.OperateObjTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.ReportTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.ReportPlayerOperateItem;
import fm.lizhi.ocean.wavecenter.domain.family.report.event.PlayerFinishOperateEvent;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.OperateCommand;
import fm.lizhi.ocean.wavecenter.provider.family.report.config.PlayerReportConfig;
import fm.lizhi.ocean.wavecenter.provider.family.report.convert.PlayerReportApplyInfoConvert;
import fm.lizhi.ocean.wavecenter.provider.family.report.datastore.dao.ReportApplyDao;
import fm.lizhi.ocean.wavecenter.provider.family.report.kafka.message.ReportSyncAuditPunishMessage;
import fm.lizhi.ocean.wavecenter.provider.family.report.kafka.producer.ReportKafkaProducer;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import static fm.lizhi.ocean.wavecenter.domain.family.report.constant.JudgeReason.ORIGINAL_ACCOUNT_REPORT_RESULT_BANNED_FOREVER;

/**
 * 举报-完成主播惩罚
 */
@Component
@Slf4j
public class ReportPlayerFinishOperateEventListener implements ApplicationListener<PlayerFinishOperateEvent> {

    @Autowired
    private BeanValidator beanValidator;
    @Autowired
    private ReportKafkaProducer reportKafkaProducer;
    @Autowired
    private PlayerReportConfig playerReportConfig;
    @Autowired
    private PlayerReportApplyInfoConvert convert;
    @Autowired
    private ReportApplyDao reportApplyDao;
    @Autowired
    private OperateCommand command;

    @Override
    public void onApplicationEvent(@NotNull PlayerFinishOperateEvent event) {
        Integer appId = event.getAppId();
        Long applyId = event.getApplyId();
        try {
            log.info("ReportPlayerFinishOperateEventListener, applyId={};appId={};event={}", applyId, appId, JsonUtils.toJsonString(event));
            ValidateResult validateResult = beanValidator.validate(event);
            if (validateResult.isInvalid()) {
                log.error("ReportRoomFinishOperateEventListener, event invalid, event={}, validateResult={}", event, validateResult);
                return;
            }
            ReportPlayerOperateItem item = event.getOperateItem();
            if(item.getOperate().getOperateObjType() == OperateObjTypeEnum.DEVICE_ID) {
                log.info("ReportPlayerFinishOperateEventListener, applyId={}, operateItem={} is DEVICE_ID", applyId, item);
                return;
            }
            ReportPlayerApplyRecord record = reportApplyDao.selectReportPlayerApplyRecordById(applyId);
            if(record == null) {
                log.info("ReportPlayerFinishOperateEventListener, applyId={} not exist", applyId);
                return;
            }
            ReportTypeEnum reportType = ReportTypeEnum.findType(record.getReportType());
            Integer type = playerReportConfig.getBizConfig(record.getAppId()).getSyncAuditType();
            BusinessEvnEnum appEnum = BusinessEvnEnum.from(record.getAppId());
            if(!item.isSuccess()) {
                log.info("ReportPlayerFinishOperateEventListener, applyId={}, operateItem={} is not success", applyId, item);
                return;
            }

            ReportSyncAuditPunishMessage playerMsg = convert.toReportSyncAuditPunishMessage(record, reportType, item.getOperate(), type, appEnum, "151", "0");
            reportKafkaProducer.sendReportSyncAuditPunishMessage(playerMsg);
            if(item.isAutoOperate()) {
                command.sendUserMessage(appId, item.getOperatedPlayer().getId(), ORIGINAL_ACCOUNT_REPORT_RESULT_BANNED_FOREVER);
                command.sendWebSystemMessage(appId, item.getOperatedPlayer().getId(), "封禁通知", ORIGINAL_ACCOUNT_REPORT_RESULT_BANNED_FOREVER);
            }
        } catch (RuntimeException e) {
            log.error("MultiAccountPlayerReportApplyPunishedEventListener failed, event={}", event, e);
        }
    }
}
