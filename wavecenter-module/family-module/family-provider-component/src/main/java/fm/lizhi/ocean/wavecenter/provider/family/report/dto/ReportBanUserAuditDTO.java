package fm.lizhi.ocean.wavecenter.provider.family.report.dto;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 审核Kafka消息结构
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReportBanUserAuditDTO {

    private BusinessEvnEnum appEnv;

    /**
     * 一次调用过程的唯一标识
     */
    private String transactionId;

    /**
     * 操作人
     */
    private String opUser;

    /**
     * 操作原因
     */
    private String reason;

    /**
     * 封禁效果失效的时间戳，毫秒值，永久封禁传0
     */
    private Long invalidTimestamp;

    /**
     * 操作的目标用户id
     */
    private Long userId;


    /**
     * 直播业务类型，参考审核系统枚举类
     */
    private Integer liveType;

    /**
     * 直播id
     */
    private Long liveId;

    /**
     * 直播间id
     */
    private Long liveRoomId;

    /**
     * 备注
     */
    private String remarks;
}
