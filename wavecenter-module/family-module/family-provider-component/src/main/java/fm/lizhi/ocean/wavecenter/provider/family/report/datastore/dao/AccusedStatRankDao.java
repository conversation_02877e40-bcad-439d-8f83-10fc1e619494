package fm.lizhi.ocean.wavecenter.provider.family.report.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.report.entity.ReportAccusedPlayerIncomeStatWeekExample;
import fm.lizhi.ocean.wavecenter.datastore.family.report.entity.ReportAccusedRoomPunishStatWeekExample;
import fm.lizhi.ocean.wavecenter.datastore.family.report.entity.ReportAccusedSinglePlayerIncomeStatWeekExample;
import fm.lizhi.ocean.wavecenter.datastore.family.report.mapper.ReportAccusedPlayerIncomeStatWeekMapper;
import fm.lizhi.ocean.wavecenter.datastore.family.report.mapper.ReportAccusedRoomPunishStatWeekMapper;
import fm.lizhi.ocean.wavecenter.datastore.family.report.mapper.ReportAccusedSinglePlayerIncomeStatWeekMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 厅违规流水合计榜单查询DAO
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
public class AccusedStatRankDao {

    @Resource
    private ReportAccusedPlayerIncomeStatWeekMapper reportAccusedPlayerIncomeStatWeekMapper;

    @Resource
    private ReportAccusedRoomPunishStatWeekMapper reportAccusedRoomPunishStatWeekMapper;

    @Resource
    private ReportAccusedSinglePlayerIncomeStatWeekMapper reportAccusedSinglePlayerIncomeStatWeekMapper;

    /**
     * 检测某周的违规次数榜单统计是否完成
     *
     * @param appId         应用ID
     * @param startWeekDate 周开始时间
     * @return 结果：true-完成，false-未完成
     */
    public boolean finishAccusedCountRankStat(int appId, Date startWeekDate) {
        ReportAccusedRoomPunishStatWeekExample punishStatWeekExample = new ReportAccusedRoomPunishStatWeekExample();
        punishStatWeekExample.createCriteria().andAppIdEqualTo(appId).andStartWeekDateEqualTo(startWeekDate).andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        long punishCount = reportAccusedRoomPunishStatWeekMapper.countByExample(punishStatWeekExample);
        return punishCount > 0;
    }

    /**
     * 检测制定周的违规流水统计是否完成
     *
     * @param appId         应用ID
     * @param startWeekDate 周开始时间
     * @return 结果：true-完成，false-未完成
     */
    public boolean finishAccusedIncomeRankStat(int appId, Date startWeekDate) {
        ReportAccusedPlayerIncomeStatWeekExample incomeStatWeekExample = new ReportAccusedPlayerIncomeStatWeekExample();
        incomeStatWeekExample.createCriteria().andAppIdEqualTo(appId).andStartWeekDateEqualTo(startWeekDate).andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());

        long count = reportAccusedPlayerIncomeStatWeekMapper.countByExample(incomeStatWeekExample);
        ReportAccusedSinglePlayerIncomeStatWeekExample singlePlayerIncomeStatWeekExample = new ReportAccusedSinglePlayerIncomeStatWeekExample();
        singlePlayerIncomeStatWeekExample.createCriteria().andAppIdEqualTo(appId).andStartWeekDateEqualTo(startWeekDate).andDeployEnvEqualTo(ConfigUtils.getEnvRequired().name());
        long singleCount = reportAccusedSinglePlayerIncomeStatWeekMapper.countByExample(singlePlayerIncomeStatWeekExample);
        return count > 0 && singleCount > 0;
    }
}
