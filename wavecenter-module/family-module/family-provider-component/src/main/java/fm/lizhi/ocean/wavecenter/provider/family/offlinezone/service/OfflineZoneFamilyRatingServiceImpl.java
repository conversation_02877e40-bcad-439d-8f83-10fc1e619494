package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.service;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.SimpleLevelBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service.OfflineZoneFamilyRatingService;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneFamilyRatingDao;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager.OfflineZoneFamilyRatingManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */
@ServiceProvider
@Slf4j
public class OfflineZoneFamilyRatingServiceImpl implements OfflineZoneFamilyRatingService {

    @Autowired
    private OfflineZoneFamilyRatingManager offlineZoneFamilyRatingManager;


    /**
     * 获取公会评级信息 - 带默认值
     */
    @Override
    public Result<SimpleLevelBean> getFamilyLevelWithDefault(int appId, Long familyId) {
        return offlineZoneFamilyRatingManager.getFamilyLevelWithDefault(appId, familyId);
    }
}
