package fm.lizhi.ocean.wavecenter.provider.family.report.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

import java.beans.Transient;

/**
 * 飞书卡片消息发送结果
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class FeiShuCardMessageResultDTO {

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 消息
     */
    private String msg;

    /**
     * 是否成功
     */
    @Transient
    public boolean isSuccess() {
        return code != null && code.equals(0);
    }

    /**
     * 是否失败
     */
    @Transient
    public boolean isFailure() {
        return !isSuccess();
    }
}
