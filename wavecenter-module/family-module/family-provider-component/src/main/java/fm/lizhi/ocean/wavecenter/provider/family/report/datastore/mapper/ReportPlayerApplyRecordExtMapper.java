package fm.lizhi.ocean.wavecenter.provider.family.report.datastore.mapper;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.family.report.entity.ReportPlayerApplyOperateResult;
import fm.lizhi.ocean.wavecenter.datastore.family.report.entity.ReportPlayerApplyRecord;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface ReportPlayerApplyRecordExtMapper {

    @Select("SELECT * FROM `report_player_apply_operate_result`\n" +
            "WHERE `app_id` = #{appId}\n" +
            "  AND `deploy_env` = #{deployEnv}\n" +
            "  AND `status` = 1\n" +
            "  AND `operated_player_id` = #{playerId} limit 1")
    ReportPlayerApplyOperateResult queryReportPlayerApplyOperateResult(@Param("appId") Integer appId, @Param("playerId") Long playerId, @Param("deployEnv") String deployEnv);




}
