package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLearningClassWhiteList;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@DataStore(namespace = "mysql_ocean_wavecenter")
public interface OfflineZoneLearningClassWhiteListExtMapper {

    @Select("<script>\n" +
            "  SELECT * FROM `offline_zone_learning_class_white_list`\n" +
            "  WHERE `learning_id` IN\n" +
            "    <foreach item=\"learningId\" collection=\"learningIds\" open=\"(\" separator=\",\" close=\")\">\n" +
            "      #{learningId}\n" +
            "    </foreach>\n" +
            "</script>")
    List<OfflineZoneLearningClassWhiteList> selectByLearningIds(@Param("learningIds") List<Long> learningIds);
}
