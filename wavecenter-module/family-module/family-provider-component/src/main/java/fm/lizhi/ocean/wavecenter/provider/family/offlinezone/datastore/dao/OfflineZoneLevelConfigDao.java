package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelConfig;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneLevelRightRelation;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.mapper.OfflineZoneLevelConfigMapper;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.mapper.ext.OfflineZoneLevelConfigExtMapper;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.mapper.ext.OfflineZoneLevelRightRelationExtMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListValuedMap;
import org.apache.commons.collections4.multimap.ArrayListValuedHashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * 线下专区等级配置 Dao
 */
@Repository
@Slf4j
public class OfflineZoneLevelConfigDao {

    @Autowired
    private OfflineZoneLevelConfigExtMapper offlineZoneLevelConfigExtMapper;

    @Autowired
    private OfflineZoneLevelRightRelationExtMapper offlineZoneLevelRightRelationExtMapper;

    @Autowired
    private OfflineZoneLevelConfigMapper offlineZoneLevelConfigMapper;

    /**
     * 获取当前环境下的所有等级配置, 不包含已删除的, 按levelOrder升序排列
     *
     * @param appId 应用ID
     * @return 等级配置列表
     */
    public List<OfflineZoneLevelConfig> listLevelConfig(Integer appId) {
        String deployEnv = ConfigUtils.getEnvRequired().name();
        List<OfflineZoneLevelConfig> levelConfigs = offlineZoneLevelConfigExtMapper.listLevelConfig(appId, deployEnv);
        log.debug("listLevelConfig, appId={}, deployEnv={}, levelConfigs={}", appId, deployEnv, levelConfigs);
        return levelConfigs;
    }

    private List<OfflineZoneLevelRightRelation> getLevelRightRelationsByLevelIds(List<Long> levelIds) {
        if (CollectionUtils.isEmpty(levelIds)) {
            return Collections.emptyList();
        }
        return offlineZoneLevelRightRelationExtMapper.selectByLevelIds(levelIds);
    }

    /**
     * 根据等级ID列表获取等级ID到关联的等级权益列表的映射, 其中权益列表已经按index升序排列
     *
     * @param levelIds 等级ID列表
     * @return 等级ID到关联的等级权益列表的映射
     */
    public ListValuedMap<Long, OfflineZoneLevelRightRelation> getLevelRightRelationsMapByLevelIds(List<Long> levelIds) {
        ArrayListValuedHashMap<Long, OfflineZoneLevelRightRelation> levelRightRelationsMap = new ArrayListValuedHashMap<>();
        List<OfflineZoneLevelRightRelation> relations = getLevelRightRelationsByLevelIds(levelIds);
        for (OfflineZoneLevelRightRelation relation : relations) {
            levelRightRelationsMap.put(relation.getLevelId(), relation);
        }
        log.debug("getLevelRightRelationsMapByLevelIds, levelIds={}, levelRightRelationsMap={}", levelIds, levelRightRelationsMap);
        return levelRightRelationsMap;
    }

    /**
     * 根据等级key获取等级配置
     *
     * @param appId
     * @param levelKey
     * @return
     */
    public OfflineZoneLevelConfig getLevelConfigByLevelKey(int appId, String levelKey) {
        OfflineZoneLevelConfig offlineZoneLevelConfig = new OfflineZoneLevelConfig();
        offlineZoneLevelConfig.setAppId(appId);
        offlineZoneLevelConfig.setLevelKey(levelKey);
        offlineZoneLevelConfig.setDeployEnv(ConfigUtils.getEnvRequired().name());
        return offlineZoneLevelConfigMapper.selectOne(offlineZoneLevelConfig);

    }


    /**
     * 根据等级ID获取等级配置
     *
     * @param appId
     * @param levelId
     * @return
     */
    public OfflineZoneLevelConfig getLevelConfigByLevelId(int appId, Long levelId) {
        OfflineZoneLevelConfig offlineZoneLevelConfig = new OfflineZoneLevelConfig();
        offlineZoneLevelConfig.setAppId(appId);
        offlineZoneLevelConfig.setId(levelId);
        offlineZoneLevelConfig.setDeployEnv(ConfigUtils.getEnvRequired().name());
        return offlineZoneLevelConfigMapper.selectByPrimaryKey(offlineZoneLevelConfig);
    }
}
