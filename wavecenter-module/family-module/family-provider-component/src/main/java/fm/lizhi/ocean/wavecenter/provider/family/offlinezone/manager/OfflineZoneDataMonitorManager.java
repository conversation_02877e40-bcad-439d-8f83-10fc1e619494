package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.manager;

import cn.hutool.core.util.StrUtil;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import fm.lizhi.ocean.wavecenter.api.user.service.UserCommonService;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.common.utils.MyDateUtil;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataFamilyWeek;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataHallWeek;
import fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataPlayerWeek;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.MapDataBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ResponseDataMonitorFamilySummary;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ResponseDataMonitorRoomSummary;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.convert.OfflineZoneDataMonitorConvert;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneDataFamilyWeekDao;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.dao.OfflineZoneDataHallWeekDao;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.RegionBean;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.config.OfflineZoneFamilyConfig;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.datastore.mapper.ext.OfflineZoneDataPlayerWeekExtMapper;
import fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto.RegionAggregateDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 线下数据监控Manager
 * <AUTHOR>
 */
@Component
@Slf4j
public class OfflineZoneDataMonitorManager {

    @Autowired
    private OfflineZoneDataFamilyWeekDao offlineZoneDataFamilyWeekDao;

    @Autowired
    private OfflineZoneDataHallWeekDao offlineZoneDataHallWeekDao;

    @Autowired
    private UserCommonService userService;

    @Autowired
    private OfflineZoneDataPlayerWeekExtMapper offlineZoneDataPlayerWeekExtMapper;

    @Autowired
    private OfflineZoneFamilyConfig offlineZoneFamilyConfig;

    @Autowired
    private OfflineZoneRedisManager offlineZoneRedisManager;

    /**
     * 获取家族数据监控汇总
     *
     * @param appId    应用ID
     * @param familyId 家族ID
     * @return 家族数据监控汇总
     */
    public ResponseDataMonitorFamilySummary getFamilySummary(Integer appId, Long familyId) {
        // 获取当前周和上周的家族数据
        OfflineZoneDataFamilyWeek currentWeek = offlineZoneDataFamilyWeekDao.getLastWeekByFamilyId(appId, familyId);
        if (currentWeek == null) {
            log.warn("未找到家族数据, appId:{}, familyId:{}", appId, familyId);
            return OfflineZoneDataMonitorConvert.INSTANCE.buildEmptyFamilySummary();
        }

        OfflineZoneDataFamilyWeek previousWeek = offlineZoneDataFamilyWeekDao.getPreviousWeekByFamilyId(appId, familyId, currentWeek.getStartWeekDate());
        // 构建响应
        return OfflineZoneDataMonitorConvert.INSTANCE.buildFamilySummaryResponse(currentWeek, previousWeek);

    }

    /**
     * 获取线下厅数据监控汇总
     *
     * @param appId    应用ID
     * @param familyId 家族ID
     * @param roomId   线下厅ID
     * @return 线下厅数据监控汇总
     */
    public ResponseDataMonitorRoomSummary getRoomSummary(Integer appId, Long familyId, Long roomId) {
        // 获取当前周和上周的厅数据
        OfflineZoneDataHallWeek currentWeek = offlineZoneDataHallWeekDao.getLastWeekByNjId(appId,familyId, roomId);
        if (currentWeek == null) {
            log.warn("未找到厅数据, appId:{}, familyId:{}, roomId:{}", appId, familyId, roomId);
            return OfflineZoneDataMonitorConvert.INSTANCE.buildEmptyRoomSummary();
        }

        OfflineZoneDataHallWeek previousWeek = offlineZoneDataHallWeekDao.getPreviousWeekByNjId(appId, roomId, currentWeek.getStartWeekDate());

        // 构建响应
        return OfflineZoneDataMonitorConvert.INSTANCE.buildRoomSummaryResponse(currentWeek, previousWeek);
    }

    /**
     * 获取地图数据
     *
     * @param appId    应用ID
     * @param familyId 家族ID
     * @return 地图数据列表
     */
    public List<MapDataBean> getMapData(Integer appId, Long familyId) {
        // 尝试从Medis缓存获取数据
        try {
            List<MapDataBean> cachedResult = offlineZoneRedisManager.getMapData(appId, familyId);
            if (cachedResult != null) {
                log.info("从Redis缓存获取地图数据成功, appId:{}, familyId:{}, size:{}", appId, familyId, cachedResult.size());
                return cachedResult;
            }
        } catch (Exception e) {
            log.warn("从Redis缓存获取地图数据失败, appId:{}, familyId:{}, error:{}", appId, familyId, e.getMessage());
        }
        
        // 缓存未命中，从数据库查询
        // 获取最新周期的家族数据
        OfflineZoneDataFamilyWeek familyWeek = offlineZoneDataFamilyWeekDao.getLastWeekByFamilyId(appId, familyId);
        if (familyWeek == null) {
            log.warn("未找到家族数据, appId:{}, familyId:{}", appId, familyId);
            return Collections.emptyList();
        }

        // 获取该周期的所有线下厅数据
        List<OfflineZoneDataHallWeek> hallWeeks = offlineZoneDataHallWeekDao.getOfflineHallsByFamilyIdAndWeek(
                appId, familyId, familyWeek.getStartWeekDate(), familyWeek.getEndWeekDate());

        if (hallWeeks.isEmpty()) {
            return Collections.emptyList();
        }

        Map<Long, UserBean> userMap = getUserMap(appId, hallWeeks.stream().map(OfflineZoneDataHallWeek::getNjId).collect(Collectors.toSet()));


        // 按省份和城市分组
        Map<String, Map<String, List<OfflineZoneDataHallWeek>>> groupedData = hallWeeks.stream()
                .filter(hall -> StrUtil.isNotEmpty(hall.getProvince()) && StrUtil.isNotEmpty(hall.getCity()))
                .collect(Collectors.groupingBy(
                        OfflineZoneDataHallWeek::getProvince,
                        Collectors.groupingBy(OfflineZoneDataHallWeek::getCity)
                ));

        // 构建地图数据
        List<MapDataBean> mapDataList = new ArrayList<>();
        for (Map.Entry<String, Map<String, List<OfflineZoneDataHallWeek>>> provinceEntry : groupedData.entrySet()) {
            String province = provinceEntry.getKey();
            for (Map.Entry<String, List<OfflineZoneDataHallWeek>> cityEntry : provinceEntry.getValue().entrySet()) {
                List<OfflineZoneDataHallWeek> cityHalls = cityEntry.getValue();
                MapDataBean mapDataBean = OfflineZoneDataMonitorConvert.INSTANCE.buildMapDataBean(province, cityHalls, userMap);
                mapDataList.add(mapDataBean);
            }
        }
        
        // 将结果存入Redis缓存
        try {
            offlineZoneRedisManager.setMapData(appId, familyId, mapDataList, offlineZoneFamilyConfig.getMapDataCacheExpireSeconds());
            log.info("地图数据存入Redis缓存成功, appId:{}, familyId:{}, size:{}", appId, familyId, mapDataList.size());
        } catch (Exception e) {
            log.warn("地图数据存入Redis缓存失败, appId:{}, familyId:{}, error:{}", appId, familyId, e.getMessage());
        }
        
        return mapDataList;
    }


    /**
     * 获取行政区划列表
     *
     * @param appId 应用ID
     * @return 行政区划列表
     */
    public List<RegionBean> getRegionList(Integer appId) {
        // 尝试从Redis缓存获取数据
        try {
            List<RegionBean> cachedResult = offlineZoneRedisManager.getRegionList(appId);
            if (cachedResult != null) {
                log.info("从Redis缓存获取行政区划列表成功, appId:{}, size:{}", appId, cachedResult.size());
                return cachedResult;
            }
        } catch (Exception e) {
            log.warn("从Redis缓存获取行政区划列表失败, appId:{}, error:{}", appId, e.getMessage());
        }
        
        // 缓存未命中，从数据库查询聚合数据
        Date lastWeekStartDay = MyDateUtil.getLastWeekStartDay();
        Date lastWeekEndDay = MyDateUtil.getLastWeekEndDay();
        List<RegionAggregateDTO> aggregateData = offlineZoneDataPlayerWeekExtMapper.selectRegionAggregateData(appId,
                DateUtil.formatStrToDate(DateUtil.formatDateToString(lastWeekStartDay, DateUtil.date_2), DateUtil.date_2),
                DateUtil.formatStrToDate(DateUtil.formatDateToString(lastWeekEndDay, DateUtil.date_2), DateUtil.date_2));
        
        if (aggregateData.isEmpty()) {
            log.warn("未找到聚合的行政区划数据, appId:{}", appId);
            return Collections.emptyList();
        }

        // 按省份和城市分组
        Map<String, Set<String>> groupedData = aggregateData.stream()
                .filter(data -> StrUtil.isNotEmpty(data.getProvince()) && StrUtil.isNotEmpty(data.getCity()))
                .collect(Collectors.groupingBy(
                        RegionAggregateDTO::getProvince,
                        Collectors.mapping(RegionAggregateDTO::getCity, Collectors.toSet())
                ));

        // 构建行政区划数据
        List<RegionBean> result = OfflineZoneDataMonitorConvert.INSTANCE.buildRegionList(groupedData);
        
        // 将结果存入Redis缓存，设置1小时过期时间
        try {
            offlineZoneRedisManager.setRegionList(appId, result, offlineZoneFamilyConfig.getRegionListCacheExpireSeconds());
            log.info("行政区划列表存入Redis缓存成功, appId:{}, size:{}", appId, result.size());
        } catch (Exception e) {
            log.warn("行政区划列表存入Redis缓存失败, appId:{}, error:{}", appId, e.getMessage());
        }
        
        return result;
    }

    /**
     * 批量获取用户信息
     *
     * @param appId 应用ID
     * @param userIds 用户ID集合
     * @return 用户信息映射
     */
    private Map<Long, UserBean> getUserMap(Integer appId, Set<Long> userIds) {
        Result<List<UserBean>> result = userService.getUserByIds(appId, new ArrayList<>(userIds));
        if (RpcResult.isSuccess(result)) {
            return result.target().stream()
                    .collect(Collectors.toMap(UserBean::getId, userBean -> userBean, (a, b) -> a));
        }
        return Collections.emptyMap();
    }


}
