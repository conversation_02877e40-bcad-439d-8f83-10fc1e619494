package fm.lizhi.ocean.wavecenter.provider.family.report.config;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class FeiShuTemplateInfoDTO {

    /**
     * 模版ID
     */
    @JsonProperty("template_id")
    private String templateId;

    /**
     * 版本号
     */
    @JsonProperty("template_version_name")
    private String templateVersionName;

    /**
     * 扩展参数
     */
    @JsonProperty("template_variable")
    private JSONObject templateVariable;

}
