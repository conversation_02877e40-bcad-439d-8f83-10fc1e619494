package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.config;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.config.AbsBizConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ConfigurationProperties(prefix = "wavecenter-family.offlinezone")
public class OfflineZoneFamilyConfig extends AbsBizConfig<CommonOfflineZoneConfig> {

    /**
     * 跳槽保护协议上传有效期-签约 30天内（天）
     */
    private Integer protectionValidityDay = 30;

    /**
     * 跳槽保护协议重置天数
     */
    private Integer protectionResetDay = 30;

    /**
     * 行政区划列表Redis缓存过期时间（秒）
     * 默认1天
     */
    private int regionListCacheExpireSeconds = 86400;

    /**
     * 地图数据Redis缓存过期时间（秒）
     * 默认1天
     */
    private int mapDataCacheExpireSeconds = 86400;

    /**
     * 默认等级key
     */
    private String defaultLevelKey = "LEVEL_1";

    /**
     * 是否全量更新协议有效期
     */
    private Boolean fullUpdateValidityPeriod = false;


    private PpOfflineZoneConfig pp;

    private XmOfflineZoneConfig xm;

    private HyOfflineZoneConfig hy;

    public OfflineZoneFamilyConfig() {
        PpOfflineZoneConfig ppConfig = new PpOfflineZoneConfig();
        XmOfflineZoneConfig xmConfig = new XmOfflineZoneConfig();
        HyOfflineZoneConfig hyConfig = new HyOfflineZoneConfig();
        this.pp = ppConfig;
        this.xm = xmConfig;
        this.hy = hyConfig;
        bizConfigMap.put(BusinessEvnEnum.PP.appId(), ppConfig);
        bizConfigMap.put(BusinessEvnEnum.XIMI.appId(), xmConfig);
        bizConfigMap.put(BusinessEvnEnum.HEI_YE.appId(), hyConfig);
    }

}
