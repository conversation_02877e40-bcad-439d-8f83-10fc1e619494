package fm.lizhi.ocean.wavecenter.provider.family.report.process.pp;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.OperateObjTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.OperateTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Player;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Room;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.Operate;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.ReportPlayerOperateItem;
import fm.lizhi.ocean.wavecenter.provider.family.report.process.ReportOperateProcessor;
import fm.lizhi.ocean.wavecenter.service.common.manager.IdManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class PpReportOperateProcessor implements ReportOperateProcessor {

    @Autowired
    private IdManager idManager;

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.PP;
    }

    @Override
    public List<ReportPlayerOperateItem> createOperateAccountItems(Player subPlayer, Player mainPlayer, Room accusedRoom) {
        List<ReportPlayerOperateItem> result = new ArrayList<>();
        //自动封禁小号
        result.add(ReportPlayerOperateItem.create(idManager.genId(), subPlayer,
                Operate.createAutoPersistentOperate(OperateTypeEnum.BAN, OperateObjTypeEnum.ACCOUNT)));
        return result;
    }
}
