package fm.lizhi.ocean.wavecenter.provider.family;

import fm.lizhi.ocean.wavecenter.base.constants.TimeConstant;
import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/7/9 19:14
 */
@Configuration
@ComponentScan(basePackages = "fm.lizhi.ocean.wavecenter.provider.family")
public class WavecenterProviderFamilyAutoConfiguration {


    @Bean
    public OkHttpClient okHttpClient() {
        int maxRequestsPerHost = 5;
        //创建客户端
        OkHttpClient.Builder client = new OkHttpClient.Builder();
        //设置连接池超时时间
        client.connectTimeout(TimeConstant.FIVE_SECOND_MILLISECOND, TimeUnit.MILLISECONDS);
        client.writeTimeout(TimeConstant.FIVE_SECOND_MILLISECOND, TimeUnit.SECONDS);
        client.readTimeout(TimeConstant.FIVE_SECOND_MILLISECOND, TimeUnit.SECONDS);
        OkHttpClient okHttpClient = client.build();
        okHttpClient.dispatcher().setMaxRequestsPerHost(maxRequestsPerHost);
        return okHttpClient;
    }

}