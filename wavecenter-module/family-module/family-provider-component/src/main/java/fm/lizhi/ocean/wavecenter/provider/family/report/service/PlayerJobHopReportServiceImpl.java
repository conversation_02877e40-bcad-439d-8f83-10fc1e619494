package fm.lizhi.ocean.wavecenter.provider.family.report.service;

import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.user.bean.UserInFamilyBean;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.common.utils.RedisLock;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.ReportTypeEnum;
import fm.lizhi.ocean.wavecenter.module.api.family.report.bean.ManualPlayerOperateResponse;
import fm.lizhi.ocean.wavecenter.module.api.family.report.bean.ManualRoomOperateResponse;
import fm.lizhi.ocean.wavecenter.module.api.family.report.bean.SubmitReportApplyResponse;
import fm.lizhi.ocean.wavecenter.module.api.family.report.request.ManualPlayerOperateRequest;
import fm.lizhi.ocean.wavecenter.module.api.family.report.request.ManualRoomOperateRequest;
import fm.lizhi.ocean.wavecenter.module.api.family.report.request.SubmitReportApplyRequest;
import fm.lizhi.ocean.wavecenter.module.api.family.report.service.PlayerJobHopReportService;
import fm.lizhi.ocean.wavecenter.provider.family.report.config.PlayerReportConfig;
import fm.lizhi.ocean.wavecenter.provider.family.report.manager.PlayerReportApplyManager;
import fm.lizhi.ocean.wavecenter.provider.family.report.manager.PlayerReportRedisManager;
import fm.lizhi.ocean.wavecenter.service.sign.manager.FamilyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@ServiceProvider
@Slf4j
public class PlayerJobHopReportServiceImpl implements PlayerJobHopReportService {

    @Autowired
    private FamilyManager familyManager;
    @Autowired
    private PlayerReportApplyManager playerReportApplyManager;
    @Autowired
    private PlayerReportRedisManager playerReportRedisManager;
    @Autowired
    private PlayerReportConfig config;

    
    /**
     * 提交跳槽举报
     */
    @Override
    public Result<SubmitReportApplyResponse> submitReportApply(SubmitReportApplyRequest request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("response={}", request);
        //1min内 只能提交成功X次 X配置为3
        Integer appId = request.getAppId();
        Long accusedUserId = request.getAccusedUserId();
        Long accusedRoomId = request.getAccusedRoomId();
        Long reportUserId = request.getReportUserId();
        Long accusedMainUserId = request.getAccusedMainUserId();

        if(playerReportRedisManager.triggerReporterPassApplyLimit(appId, reportUserId)) {
            return RpcResult.fail(PlayerJobHopReportService.REPORT_ALREADY_LIMIT, "提交太频繁了");
        }
        //1h内 相同的记录只能提交成功一次(被举报者(原号/小号)&被举报厅->相同,视为相同举报记录)
        if(config.isOpenSuccessReportSubmitCountLimit() && playerReportRedisManager.existAccusedRoom(appId,
                reportUserId, accusedUserId, accusedRoomId)) {
            return RpcResult.fail(PlayerJobHopReportService.REPORT_ROOM_REPEAT, "该主播已被违规封禁，请勿重复提交了");
        }

        ReportTypeEnum reportType = ReportTypeEnum.findType(request.getReportType());
        if(reportType == null) {
            return RpcResult.fail(PlayerJobHopReportService.REPORT_TYPE_NO_EXIST, "不支持的举报类型");
        }
        if(!config.getBizConfig(appId).getSupportReportType().contains(request.getReportType())) {
            return RpcResult.fail(PlayerJobHopReportService.REPORT_TYPE_NO_EXIST, "不支持该举报类型");
        }
        UserInFamilyBean userInFamily = familyManager.getUserInFamily(accusedRoomId);
        if(userInFamily == null || userInFamily.getFamilyId() == null) {
            //该厅已解约 toast提示
            return RpcResult.fail(PlayerJobHopReportService.ACCUSED_ROOM_UN_SIGN, "跳槽厅已经跟公会解约了");
        }
        if(!userInFamily.isRoom()) {
            //不是一个厅的波段号 toast提示
            return RpcResult.fail(PlayerJobHopReportService.ACCUSED_ROOM_BAND_ILLEGAL, "输入正确的跳槽厅ID");
        }
        //按被举报人加锁 大小号按id大小的顺序拼key
        try (RedisLock lock = playerReportRedisManager.tryGetReportPlayerApplyLock(appId, accusedUserId, accusedMainUserId == null ? 0 : accusedMainUserId)) {
            if (!lock.tryLock()) {
                return RpcResult.fail(PlayerJobHopReportService.SUBMIT_REPORT_APPLY_LOCK_FAIL, "提交太频繁了");
            }
            SubmitReportApplyResponse resp = playerReportApplyManager.submitPlayerJobHopReportApply(request, reportType);
            return RpcResult.success(resp);
        } catch (Exception e) {
            log.error("submitPlayerJobHopReportApply failed. request:{}", JsonUtils.toJsonString(request), e);
            return RpcResult.fail(PlayerJobHopReportService.REPORT_ROOM_REPEAT, "请重新尝试");
        }
    }

    @Override
    public Result<ManualPlayerOperateResponse> manualPlayerOperate(ManualPlayerOperateRequest request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);
        ManualPlayerOperateResponse response =
                playerReportApplyManager.manualPlayerOperate(request.getReportPlayerApplyOperateResultId(), request.getAppId(), request.getOperator());
        return RpcResult.success(response);
    }

    @Override
    public Result<ManualRoomOperateResponse> manualRoomOperate(ManualRoomOperateRequest request) {
        LogContext.addReqLog("request={}", request);
        LogContext.addResLog("request={}", request);
        ManualRoomOperateResponse response =
                playerReportApplyManager.manualRoomOperate(request.getReportRoomViolationOperateResultId(), request.getAppId(), request.getOperator(), request.getOperateDuration().getMinute());
        return RpcResult.success(response);
    }
}