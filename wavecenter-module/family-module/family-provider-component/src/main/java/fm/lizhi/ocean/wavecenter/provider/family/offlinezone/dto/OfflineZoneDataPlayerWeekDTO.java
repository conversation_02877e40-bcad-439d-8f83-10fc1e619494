package fm.lizhi.ocean.wavecenter.provider.family.offlinezone.dto;

import lombok.*;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 线下专区-主播明细-周
 *
 * @date 2025-08-08 02:38:52
 */
@Data
@Accessors(chain = true)
public class OfflineZoneDataPlayerWeekDTO {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 周开始日期，格式YYYY-MM-DD
     */
    private Date startWeekDate;

    /**
     * 周结束日期，格式YYYY-MM-DD
     */
    private Date endWeekDate;

    /**
     * 主播ID
     */
    private Long userId;

    /**
     * 主播名称
     */
    private String userName;

    /**
     * 实名证件号码
     */
    private String idCardNumber;

    /**
     * 实名姓名
     */
    private String idName;

    /**
     * 公会ID
     */
    private Long familyId;

    /**
     * 公会名称
     */
    private String familyName;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 厅主名称
     */
    private String njName;

    /**
     * 签约时间
     */
    private Date beginSignTime;

    /**
     * 主播分类：0 线下，1 线上
     */
    private Integer category;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 主播收入
     */
    private BigDecimal income;
}