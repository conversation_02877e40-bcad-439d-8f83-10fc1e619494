package fm.lizhi.ocean.wavecenter.provider.family.report.convert;

import com.lizhi.commons.config.core.util.ConfigUtils;
import fm.lizhi.commons.util.DateUtil;
import fm.lizhi.ocean.wavecenter.provider.family.report.dto.ReportBanDeviceAuditDTO;
import fm.lizhi.ocean.wavecenter.provider.family.report.dto.ReportBanLiveAuditDTO;
import fm.lizhi.ocean.wavecenter.provider.family.report.dto.ReportBanUserAuditDTO;
import fm.lizhi.ocean.wavecenter.provider.family.report.kafka.message.ReportBanAuditKafkaMessage;
import fm.lizhi.ocean.wavecenter.provider.family.report.kafka.message.ReportRiskKafkaMessage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {Date.class, ConfigUtils.class, DateUtil.class}
)
public interface ReportAuditMessageConvert {

    ReportAuditMessageConvert INSTANCE = Mappers.getMapper(ReportAuditMessageConvert.class);

    /**
     * 转换为Kafka消息
     *
     * @param auditDTO 参数
     * @return Kafka消息
     */
    @Mapping(target = "op", ignore = true)
    @Mapping(target = "extra", ignore = true)
    @Mapping(target = "requestId", source = "transactionId")
    @Mapping(target = "modifyTime", expression = "java(new Date().getTime())")
    @Mapping(target = "appName", expression = "java(auditDTO.getAppEnv().getName())")
    @Mapping(target = "source", expression = "java(auditDTO.getAppEnv().getName())")
    ReportBanAuditKafkaMessage toReportBanAuditKafkaMessage(ReportBanUserAuditDTO auditDTO);

    /**
     * 转换为Kafka消息
     *
     * @param auditDTO 参数
     * @return Kafka消息
     */
    @Mapping(target = "op", ignore = true)
    @Mapping(target = "remarks", ignore = true)
    @Mapping(target = "extra", ignore = true)
    @Mapping(target = "requestId", source = "transactionId")
    @Mapping(target = "modifyTime", expression = "java(new Date().getTime())")
    @Mapping(target = "appName", expression = "java(auditDTO.getAppEnv().getName())")
    @Mapping(target = "source", expression = "java(auditDTO.getAppEnv().getName())")
    ReportBanAuditKafkaMessage toReportBanAuditKafkaMessage(ReportBanLiveAuditDTO auditDTO);

    @Mapping(target = "requestId", source = "transactionId")
    @Mapping(target = "requestTime", expression = "java(DateUtil.formatCurrentTime(DateUtil.datetime))")
    @Mapping(target = "appId", expression = "java(auditDTO.getAppEnv().getName())")
    @Mapping(target = "source", constant = "1")
    @Mapping(target = "event", constant = "default")
    @Mapping(target = "eventTime", expression = "java(DateUtil.formatCurrentTime(DateUtil.datetime))")
    @Mapping(target = "dimension", constant = "did")
    @Mapping(target = "value", source = "deviceId")
    ReportRiskKafkaMessage toReportRiskKafkaMessage(ReportBanDeviceAuditDTO auditDTO);


}
