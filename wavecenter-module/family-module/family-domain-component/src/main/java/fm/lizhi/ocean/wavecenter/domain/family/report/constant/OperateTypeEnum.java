package fm.lizhi.ocean.wavecenter.domain.family.report.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum OperateTypeEnum {

    /**
     * 封禁
     */
    BAN(1),

    /**
     * 解封
     */
    UN_BAN(2),

    ;
    private final Integer code;

    public static OperateTypeEnum getByCode(Integer code) {
        for (OperateTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }


}
