package fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply;

import fm.lizhi.ocean.wavecenter.domain.family.report.constant.JudgeReason;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.ReportTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Player;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Reporter;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Room;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.OperateOrder;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.ReportPlayerOperateItem;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.RoomViolation;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.OperateCommand;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.PlayerReportApplyRepository;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 原号跳槽举报申请
 */
@Data
public class OriginalAccountPlayerJobHopReportApply extends PlayerJobHopReportApply {


    /**
     * 工厂方法 - 创建新对象 (完整参数版本)
     *
     * @param id             举报ID
     * @param appId          应用ID
     * @param reportUser     举报者
     * @param accusedRoom    挖槽厅
     * @param originalPlayer 被举报的原号
     * @return 新创建的MultiAccountPlayerJobHopReportApply实例
     */
    public static OriginalAccountPlayerJobHopReportApply create(Long id, Integer appId,
                                                                Reporter reportUser,
                                                                Player originalPlayer,
                                                                Room accusedRoom,
                                                                String source) {
        OriginalAccountPlayerJobHopReportApply apply = new OriginalAccountPlayerJobHopReportApply();
        apply.setId(id);
        apply.setAppId(appId);
        apply.setReportUser(reportUser);
        //被举报者的原厅就是被挖厅
        apply.setPoachedRoom(originalPlayer.getSignRoom());
        apply.setAccusedRoom(accusedRoom);
        apply.setAccusedUser(originalPlayer);
        apply.setJudgeResult(JudgeResult.create());
        apply.setSource(source);
        apply.setReportType(ReportTypeEnum.ORG_JOB_HOPPING);
        apply.setSubmitTime(new Date());
        return apply;
    }

    private OriginalAccountPlayerJobHopReportApply() {
    }


    @Override
    public List<ReportPlayerOperateItem> buildPlayerOperateItems(PlayerReportApplyRepository applyRepository) {
        if(!isPass()) {
            throw new IllegalStateException("原号-只有通过判定的举报单才能创建处罚单");
        }
        // 创建大小号跳槽主播处罚项
        return applyRepository.createPlayerReportOperate(this.appId, this.accusedUser, accusedRoom);
    }

    @Override
    public void sendMessageAfterOperate(OperateOrder operateOrder) {
        OperateCommand command = operateOrder.getCommand();
        //是否有自动处罚厅的行为
        boolean isRoomBan = operateOrder.hasRoomAutoOperate();
        RoomViolation roomViolation = operateOrder.getRoomViolation();
        //发私信举报者
        String toReporterContent = "";
        Room accusedRoomInfo = accusedRoom;
        if (isRoomBan) {
            //计算时间
            int day = roomViolation.getOperateDuration() / 60 / 24;
            toReporterContent = JudgeReason.buildReason(accusedUser, roomViolation.getAccusedRoom(), day, JudgeReason.ORIGINAL_ACCOUNT_REPORT_RESULT_BANNED_WITH_HALL_BAN);
        } else {
            toReporterContent = JudgeReason.buildReason(accusedUser, JudgeReason.ORIGINAL_ACCOUNT_REPORT_RESULT_BANNED);

            String toAccusedRoomContent = JudgeReason.buildReason(accusedRoomInfo, accusedUser, JudgeReason.ORIGINAL_ACCOUNT_REPORT_RESULT_BANNED_TO_ACCUSED_ROOM);
            //私信挖槽厅主
            command.sendWebSystemMessage(appId, accusedRoomInfo.getId(), "封禁结果", toAccusedRoomContent);
            command.sendUserMessage(appId, accusedUser.getId(), toAccusedRoomContent);
        }
        command.sendWebSystemMessage(appId, reportUser.getId(), String.format("「%s」举报结果", ReportTypeEnum.ORG_JOB_HOPPING.getDescription()), toReporterContent);
        command.sendUserMessage(appId, reportUser.getId(), toReporterContent);
    }

    @Override
    public void store(PlayerReportApplyRepository playerReportApplyRepository) {
        playerReportApplyRepository.insert(this);
    }


}
