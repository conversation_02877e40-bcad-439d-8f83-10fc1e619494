package fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply;

import fm.lizhi.ocean.wavecenter.domain.family.report.constant.JudgeReason;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.JudgmentStatusEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * 举报结果
 */
@Data
public class JudgeResult {

    private JudgmentStatusEnum judgmentStatus;

    private String reason;

    private Date judgeTime;

    public static JudgeResult create() {
        JudgeResult judgeResult = new JudgeResult();
        judgeResult.judgmentStatus = JudgmentStatusEnum.PENDING;
        judgeResult.reason = StringUtils.EMPTY;
        judgeResult.judgeTime = new Date();
        return judgeResult;
    }

    public static JudgeResult createInterrupt() {
        JudgeResult judgeResult = new JudgeResult();
        judgeResult.judgmentStatus = JudgmentStatusEnum.REVERT;
        judgeResult.reason = JudgeReason.EXCEPTION_REPORT_RESULT;
        judgeResult.judgeTime = new Date();
        return judgeResult;
    }
    public static JudgeResult valueOf(JudgmentStatusEnum judgmentStatus, Date judgeTime) {
        JudgeResult judgeResult = new JudgeResult();
        judgeResult.judgmentStatus = judgmentStatus;
        judgeResult.judgeTime = judgeTime;
        return judgeResult;
    }

    public boolean isPending() {
        return this.judgmentStatus == JudgmentStatusEnum.PENDING;
    }

    public boolean isPass() {
        return this.judgmentStatus == JudgmentStatusEnum.PASS;
    }

    public void resetReason(String reason) {
        this.reason = reason;
    }
    public void finish(boolean isEstablished) {
        this.judgmentStatus = isEstablished ? JudgmentStatusEnum.PASS : JudgmentStatusEnum.REVERT;
        this.judgeTime = new Date();
    }

}
