package fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate;

import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.OperateStatusEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.ReportTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Player;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply.PlayerJobHopReportApply;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.OperateCommand;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.PlayerReportApplyRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;

/**
 * 一次完整的举报处罚单
 */
@Data
@Slf4j
public class OperateOrder {

    private Integer appId;

    private Long applyId;

    private ReportTypeEnum reportTypeEnum;

    private Date operateTime;
    private String operateReason;

    /**
     * 对被举报主播的操作项
     */
    private List<ReportPlayerOperateItem> items;

    private RoomViolation roomViolation;

    /**
     * 封禁操作者
     */
    protected OperateCommand command;
    protected PlayerReportApplyRepository applyRepository;

    public static OperateOrder create(Integer appId, long roomViolationId, long roomOperateId,
                                      PlayerJobHopReportApply apply,
                                      OperateCommand operateCommand,
                                      PlayerReportApplyRepository playerReportApplyRepository) {
        OperateOrder operateOrder = new OperateOrder();
        operateOrder.appId = appId;
        operateOrder.applyId = apply.getId();
        operateOrder.reportTypeEnum = apply.getReportType();
        operateOrder.command = operateCommand;
        operateOrder.operateTime = apply.getSubmitTime();
        operateOrder.applyRepository = playerReportApplyRepository;
        //构建厅违规记录和处罚项
        operateOrder.roomViolation = apply.buildRoomViolation(roomViolationId, roomOperateId, playerReportApplyRepository);
        //构建主播惩罚项
        operateOrder.items = apply.buildPlayerOperateItems(playerReportApplyRepository);
        operateOrder.operateReason = apply.getJudgeResult().getReason();
        return operateOrder;
    }

    /**
     * 有处罚就好了 不管成不成功
     * @return
     */
    public boolean hasRoomAutoOperate() {
        return roomViolation.hasAutoOperate();
    }

    /**
     * 执行处罚
     */
    public void execute() {
        String operator = "system_auto";
        //执行主播处罚
        for (ReportPlayerOperateItem item : items) {
            Player operatedPlayer = item.getOperatedPlayer();
            if(item.isManual()) {
                log.info("operate item is manual. appId={};applyId={};accusedUser={};item:{}",
                        appId, applyId, operatedPlayer.getId(), JsonUtils.toJsonString(item));
                continue;
            }
            log.info("execute player operate. appId={};applyId={};accusedUser={};item:{}",
                    appId, applyId, operatedPlayer.getId(), JsonUtils.toJsonString(item));
            boolean success = false;
            try {
                success = command.markUserBanStatus(appId, item, operator, roomViolation.getAccusedRoom().getId(), operateReason);
            } catch (Exception e) {
                log.error("execute player operate error. appId={};applyId={};accusedUser={};item:{}",
                        applyId, applyId, operatedPlayer.getId(), JsonUtils.toJsonString(item), e);
            }
            //更新状态
            item.finishOperate(applyId, appId, success ? OperateStatusEnum.SUCCESS : OperateStatusEnum.FAIL);
            //同步落库
            applyRepository.updateOperateStatus(item, operator);
        }
        //执行厅处罚
        roomViolation.operate(applyId, applyRepository, command, operator, operateReason);
    }

    public void store() {
        //保存主播惩罚项
        //保存厅违规记录
        applyRepository.saveOperateOrder(appId, applyId, reportTypeEnum, items, operateTime, roomViolation);
    }


}
