package fm.lizhi.ocean.wavecenter.domain.family.report.entity;

import fm.lizhi.ocean.wavecenter.domain.family.report.repository.PlayerSignInfoRepository;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.model.PlayerSignTarget;
import lombok.Data;

/**
 * 举报者
 */
@Data
public class Reporter implements People {

    private Long id;
    private String name;
    private String band;


    private Integer appId;
    /**
     * 签约厅
     */
    private Room signRoom;



    public static Reporter create(Long id, Integer appId, PlayerSignInfoRepository signInfoRepository) {
        Reporter reporter = new Reporter();
        reporter.id = id;
        PlayerSignTarget signInfo = signInfoRepository.getRecentSignInfo(appId, id);
        reporter.signRoom = signInfo.getSignRoom();
        reporter.band = signInfo.getBand();
        reporter.name = signInfo.getName();
        return reporter;
    }
}
