package fm.lizhi.ocean.wavecenter.domain.family.report.repository;

import fm.lizhi.ocean.wavecenter.domain.family.report.constant.ReportTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Player;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Room;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.TimeRange;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply.JudgeResult;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply.MultiAccountPlayerJobHopReportApply;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply.OriginalAccountPlayerJobHopReportApply;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.*;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.model.RoomGradientOperateRule;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.model.RoomViolationRecordValue;

import java.util.Date;
import java.util.List;

public interface PlayerReportApplyRepository {


    /**
     * 根据appId和不同的被举报账号，创建跳槽处罚单
     */
    List<ReportPlayerOperateItem> createPlayerReportOperate(Integer appId, Player subPlayer, Player mainPlayer, Room accusedRoom);
    List<ReportPlayerOperateItem> createPlayerReportOperate(Integer appId, Player originalPlayer, Room accusedRoom);

    /**
     * 保存罚单
     */
    void saveOperateOrder(Integer appId, long applyId, ReportTypeEnum reportTypeEnum,
                          List<ReportPlayerOperateItem> playerOperateItems, Date operateTime, RoomViolation roomViolation);


    /**
     * 更新主播举报处理-操作状态
     * @param item
     */
    void updateOperateStatus(ReportPlayerOperateItem item, String operator);



    /**
     * 获取指定时间的厅违规信息
     * @param accusedRoom 违规厅
     * @param period 时间段
     * @return 违规信息
     */
    List<RoomViolationRecordValue> findAllRoomInfoByPeriod(Room accusedRoom, TimeRange period);



    /**
     * 获取appId对应条件值配置->X个自然周为一个周期
     * @return
     */
    Long getReportRoomViolationConditionCount(Integer appId);

    /**
     * 记录厅处罚
     */
    void updateRoomViolation(ReportRoomOperateItem item, String operator);

    /**
     * 获取梯度规则
     */
    List<RoomGradientOperateRule> getRoomGradientOperateRules(Integer appId);


    /**
     * 获取指定厅的当前最大操作序号
     */
    Integer getMaxOperateSeqByRoom(Room accusedRoom, TimeRange timeRange);



    boolean insert(MultiAccountPlayerJobHopReportApply apply);

    boolean insert(OriginalAccountPlayerJobHopReportApply apply);


    /**
     * 跳槽厅是否在白名单
     */
    boolean isAccusedRoomInWhiteList(Integer appId, Room accusedRoom);

    /**
     * 通知
     * @param id
     * @param judgeResult
     */
    void updateJudgeResult(Long id, JudgeResult judgeResult);

    /**
     * 判断厅是否在挖槽厅黑名单中
     */
    boolean isMainPlayerRoomInBlackList(int appId, Room signRoom);

    Integer getReportViolationIncomeConfig(ReportTypeEnum reportTypeEnum, Integer appId);

    /**
     * 测试开关，是否开启同家族检测
     * @return
     */
    boolean isOpenSameFamilyCheck();

}
