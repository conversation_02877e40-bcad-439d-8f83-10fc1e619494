package fm.lizhi.ocean.wavecenter.domain.family.report.service;

import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.OperateStatusEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.ReportPlayerOperateItem;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.ReportRoomOperateItem;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.OperateCommand;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.PlayerReportApplyRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 举报单操作服务
 */
@Component
@Slf4j
public class ReportOperateService {

    @Autowired
    private OperateCommand command;
    @Autowired
    private PlayerReportApplyRepository applyRepository;


    public void manualPlayerOperate(Long applyId, Integer appId, ReportPlayerOperateItem item, String operator, Long accusedRoomId, String operateReason) {
        if(!item.getOperate().isManual()) {
            log.error("manualPlayerOperate operate item is not manual. appId={};applyId={};accusedUser={};item:{}",
                    appId, applyId, item.getOperatedPlayer().getId(), JsonUtils.toJsonString(item));
            return;
        }
        if(item.isOperated()) {
            log.error("manualPlayerOperate operate item is already operated. appId={};applyId={};accusedUser={};item:{}",
                    appId, applyId, item.getOperatedPlayer().getId(), JsonUtils.toJsonString(item));
            return;
        }

        boolean success = false;
        try {
            success = command.markUserBanStatus(appId, item, operator, accusedRoomId, operateReason);
        } catch (Exception e) {
            log.error("manualPlayerOperate execute player operate error. appId={};applyId={};accusedUser={};item:{}",
                    appId, applyId, item.getOperatedPlayer().getId(), JsonUtils.toJsonString(item), e);
        }
        //更新状态
        item.finishOperate(applyId, appId, success ? OperateStatusEnum.SUCCESS : OperateStatusEnum.FAIL);
        //同步落库
        applyRepository.updateOperateStatus(item, operator);
    }

    /**
     * 根据举报单生成处罚单,执行处罚
     */
    public void manualRoomOperate(Long applyId, Integer appId, ReportRoomOperateItem operateItem, String operator, String operateReason) {
        if(!operateItem.getOperate().isManual()) {
            log.error("manualRoomOperate operate item is not manual. appId={};accusedRoom={};item:{}",
                    appId, operateItem.getOperatedRoom().getId(), JsonUtils.toJsonString(operateItem));
            return;
        }
        if(operateItem.isOperated()) {
            log.error("manualRoomOperate operate item is already operated. appId={};accusedRoom={};item:{}",
                    appId, operateItem.getOperatedRoom().getId(), JsonUtils.toJsonString(operateItem));
            return;
        }
        boolean operateResult = false;
        try {
            operateResult = command.markRoomBanStatus(appId, operateItem, operator);
            log.info("manualRoomOperate operate room result. appId={};accusedRoom={};operateItem:{};result:{}",
                    appId, operateItem.getOperatedRoom().getId(), JsonUtils.toJsonString(operateItem), operateResult);
        } catch (Exception e) {
            log.error("manualRoomOperate execute room operate error. appId={};accusedRoom={};operateItem:{}",
                    appId, operateItem.getOperatedRoom().getId(), JsonUtils.toJsonString(operateItem), e);
        }
        operateItem.finishOperate(applyId, appId, operateResult ? OperateStatusEnum.SUCCESS : OperateStatusEnum.FAIL);
        applyRepository.updateRoomViolation(operateItem, operator);
    }


}
