package fm.lizhi.ocean.wavecenter.domain.family.report.event;

import fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply.PlayerJobHopReportApply;
import lombok.Getter;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationEvent;

import java.util.Date;

/**
 * 举报单完成判定事件
 */
@Getter
public class PlayerReportApplyFinishJudgeEvent extends ApplicationEvent {

    private final PlayerJobHopReportApply apply;
    public PlayerReportApplyFinishJudgeEvent(@NotNull PlayerJobHopReportApply apply) {
        super(new Date());
        this.apply = apply;
    }
}
