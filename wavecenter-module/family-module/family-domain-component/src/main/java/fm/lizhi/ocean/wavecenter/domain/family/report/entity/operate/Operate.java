package fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate;

import fm.lizhi.ocean.wavecenter.domain.family.report.constant.OperateObjTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.OperateTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.time.DateUtils;

import java.util.Date;

/**
 * 举报操作
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Operate {

    /**
     * 操作类型 1-封禁 2-解禁
     */
    private OperateTypeEnum operateType;

    /**
     * 操作目标 1-账号 2-房间 3-设备ID
     */
    private OperateObjTypeEnum operateObjType;


    /**
     * 操作时长 格式为分钟
     */
    private Integer operateDuration;

    /**
     * 是否手动 true-手动 false-自动
     */
    private boolean manual;

    public static Operate createAutoPersistentOperate(OperateTypeEnum operateType, OperateObjTypeEnum operateObjType) {
        Operate operate = new Operate();
        operate.operateType = operateType;
        operate.operateObjType = operateObjType;
        operate.operateDuration = -1;
        operate.manual = false;
        return operate;
    }

    public static Operate createManualPersistentOperate(OperateTypeEnum operateType, OperateObjTypeEnum operateObjType) {
        Operate operate = new Operate();
        operate.operateType = operateType;
        operate.operateObjType = operateObjType;
        operate.operateDuration = -1;
        operate.manual = true;
        return operate;
    }

    public static Operate valueOf(OperateTypeEnum operateType, OperateObjTypeEnum operateObjType, Integer operateDuration, boolean manual) {
        return new Operate(operateType, operateObjType, operateDuration, manual);
    }

    /**
     * 计算封禁失效时间
     *
     * @return 失效时间
     */
    public Date getInvalidDate() {
        if (operateDuration < 0) {
            //永久封禁
            return DateUtils.addMinutes(new Date(), Integer.MAX_VALUE);
        }
        return DateUtils.addMinutes(new Date(), operateDuration);
    }

    public boolean isPermanent() {
        return operateDuration == -1;
    }
}
