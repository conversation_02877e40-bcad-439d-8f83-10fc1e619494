package fm.lizhi.ocean.wavecenter.domain.family.report.constant;

import cn.hutool.core.date.DateUtil;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Player;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Room;
import lombok.experimental.UtilityClass;

import java.util.Date;

@UtilityClass
public class JudgeReason {

    private static final String BAN_DAY = "封禁%d天";
    private static final String BAN_FOR_EVER = "永封";

    public static String buildReason(Player player, String reason) {
        return String.format(reason, player.getName(), player.getBand());
    }
    public static String buildReason(Player player, Room room, Integer days, String reason) {
        return String.format(reason, player.getName(), player.getBand(), room.getBand(), days);
    }

    public static String buildBanReason(Player player1, Player player2, Room room, Integer min, boolean foreverBan, String reason) {
        if(foreverBan) {
            return String.format(reason, player1.getName(), player1.getBand(), player2.getName(), player2.getBand(), room.getBand(), BAN_FOR_EVER);
        }
        return String.format(reason, player1.getName(), player1.getBand(), player2.getName(), player2.getBand(), room.getBand(), String.format(BAN_DAY, min/60/24));
    }

    public static String buildBanRoomReason(Player player1, Room room, Integer min, boolean foreverBan, String reason) {
        if(foreverBan) {
            return String.format(reason, player1.getName(), player1.getBand(), room.getBand(), BAN_FOR_EVER);
        }
        return String.format(reason, player1.getName(), player1.getBand(), room.getBand(), String.format(BAN_DAY, min/60/24));
    }


    public static String buildReason(Player player1, Player player2, String reason) {
        return String.format(reason, player1.getName(), player1.getBand(), player2.getName(), player2.getBand());
    }

    public static String buildReason(Room room, Integer min, Date date, String reason) {
        return String.format(reason, room.getBand(), min/60/24, DateUtil.format(date, "YYYY-MM-dd hh:mm:ss"));
    }

    public static String buildReason(Room room, Player player, String reason) {
        return String.format(reason, room.getBand(), player.getBand());
    }

    public static String buildReason(Room room, String reason) {
        return String.format(reason, room.getBand());
    }


    public static String buildReason(String relatedReason, String reason) {
        return String.format(reason, relatedReason);
    }

    //========================== 原号跳槽举报结果==========================
    public static final String ORIGINAL_ACCOUNT_REPORT_RESULT_REPEAT = "您举报的账号【%s】（%s）已被封禁，无需重复举报";
    public static final String ORIGINAL_ACCOUNT_REPORT_RESULT_NOT_SIGNED_OR_UNBOUND = "您举报的账号【%s】（%s）未和公会签约或解约已满14天，举报失败";
    public static final String ORIGINAL_ACCOUNT_REPORT_RESULT_NO_JUMP_ACTIVITY = "您举报的账号【%s】（%s）暂未发生跳槽行为，举报失败";
    public static final String ORIGINAL_ACCOUNT_REPORT_RESULT_SAME_FAMILY = "您举报的账号【%s】（%s）所关联跳槽厅和签约厅隶属于同一个公会，举报失败";
    public static final String ORIGINAL_ACCOUNT_REPORT_RESULT_BANNED_NO_HALL_BAN = "使用签约号去非同工会跳槽厅，获取收益达处罚标准";
    public static final String ORIGINAL_ACCOUNT_REPORT_RESULT_BANNED_WITH_HALL_BAN = "您举报的账号【%s】（%s）违规属实，已封禁账号，并且跳槽厅%s已被封禁%d天";
    public static final String ORIGINAL_ACCOUNT_REPORT_RESULT_BANNED = "您举报的账号【%s】（%s）违规属实，已封禁账号";
    public static final String ORIGINAL_ACCOUNT_REPORT_RESULT_BANNED_FOREVER = "您的账号因产生跳槽行为，已被永久封禁";


    //========================== 大小号跳槽举报结果==========================
    public static final String SMALL_ACCOUNT_REPORT_RESULT_NOT_SIGNED_OR_UNBOUND = "您举报的账号【%s】（%s）未和公会签约或解约已满14天，举报失败";
    public static final String SMALL_ACCOUNT_REPORT_RESULT_SAME_FAMILY = "您举报的账号【%s】（%s）所关联跳槽厅和大号签约厅隶属于同一个公会，举报失败";
    public static final String SMALL_ACCOUNT_REPORT_RESULT_REPEAT = "您举报的账号【%s】（%s）和【%s】（%s）已被封禁，无需重复举报";
    public static final String SMALL_ACCOUNT_REPORT_RESULT_NO_ASSOCIATION = "举报的大号【%s】（%s）、小号【%s】（%s）暂无关联，举报失败，建议持续提交举报进行尝试";
    public static final String SMALL_ACCOUNT_REPORT_RESULT_NO_JUMP_ACTIVITY = "您举报的小号【%s】（%s）暂未发生跳槽行为，举报失败";
    public static final String SMALL_ACCOUNT_REPORT_RESULT_SMALL_BANNED = "您举报的小号【%s】（%s）跳槽违规属实，已永久封禁小号账号";
    public static final String SMALL_ACCOUNT_REPORT_RESULT_BOTH_BANNED = "您举报的大号【%s】（%s）、小号【%s】（%s）跳槽违规属实，已永久封禁大小号账号";
    public static final String SMALL_ACCOUNT_REPORT_RESULT_SMALL_BANNED_WITH_HALL_BAN = "您举报的小号【%s】（%s）跳槽违规属实，已永久封禁小号账号，并且跳槽厅%s已被%s";
    public static final String SMALL_ACCOUNT_REPORT_RESULT_BOTH_BANNED_WITH_HALL_BAN = "您举报的大号【%s】（%s）、小号【%s】（%s）跳槽违规属实，已永久封禁大小号账号，并且跳槽厅%s已被%s";

    public static final String SMALL_ACCOUNT_SAME_WITH_BIG_SIGN = "举报大小号（%s）一致，且大小号签约不同公会，大号签约早于小号";

    //举报大小号（实名/设备/IP）一致，大号签约生效中，小号在非同公会跳槽厅内，获取收益达处罚标准
    public static final String SMALL_ACCOUNT_REPORT_RESULT_BANNED = "举报大小号（%s）一致，大号签约生效中，小号在非同公会跳槽厅内，获取收益达处罚标准";


    //==========================厅的私信==========================
    // 给挖槽厅的封禁私信
    public static final String ORIGINAL_ACCOUNT_REPORT_RESULT_BANNED_TO_ACCUSED_ROOM = "您的厅【%s】中,主播【%s】因违规跳槽已被封禁,系统已记录厅违规行为,若持续违规将对厅进行处罚, 可登录创作服务中心-挖槽举报-挖槽违规记录查询详细违规名单";
    //给挖槽厅的封禁私信(包含厅封禁)
    public static final String ORIGINAL_ACCOUNT_REPORT_RESULT_BANNED_TO_ACCUSED_ROOM_WITH_HALL_BAN = "您的厅【%s】有多次挖人行为,已被封禁开播权限%d天,将于%s时解封,可登录创作服务中心-挖槽举报-挖槽违规记录查询详细违规名单";
    public static final String ORIGINAL_ACCOUNT_REPORT_RESULT_FORVER_BANNED_TO_ACCUSED_ROOM_WITH_HALL_BAN = "您的厅【%s】有多次挖人行为,已被永久封禁开播权限,可登录创作服务中心-挖槽举报-挖槽违规记录查询详细违规名单";
    //给挖槽厅家族长发私信
    public static final String ROOM_BANNED_TO_ACCUSED_FAMILY = "您旗下的签约厅%s因产生挖人行为,已被封禁开播权限%s天, 将于%s时解封,可登录创作服务中心-挖槽举报-挖槽违规记录查询详细违规名单";
    public static final String ROOM_REPORT_RESULT_FORVER_BANNED_TO_ACCUSED_FAMILY = "您旗下的签约厅%s因产生挖人行为,已被永久封禁开播权限,可登录创作服务中心-挖槽举报-挖槽违规记录查询详细违规名单";


    public static final String EXCEPTION_REPORT_RESULT = "举报异常";

}