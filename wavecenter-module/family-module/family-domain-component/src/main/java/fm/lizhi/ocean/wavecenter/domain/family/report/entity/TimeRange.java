package fm.lizhi.ocean.wavecenter.domain.family.report.entity;

import fm.lizhi.commons.util.DateUtil;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
public class TimeRange {
    private Date startTime;
    private Date endTime;


    public static TimeRange create(Date reportTime) {
        TimeRange timeRange = new TimeRange();
        timeRange.setEndTime(reportTime);
        timeRange.setStartTime(DateUtil.getDayBefore(reportTime, 14));
        return timeRange;
    }


    public String toString() {
        return "TimeRange{" +
                "startTime=" + startTime.getTime() +
                ", endTime=" + endTime.getTime() +
                '}';
    }
}
