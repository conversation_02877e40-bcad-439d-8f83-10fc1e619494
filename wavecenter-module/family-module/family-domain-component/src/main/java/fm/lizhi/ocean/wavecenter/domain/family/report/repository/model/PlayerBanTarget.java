package fm.lizhi.ocean.wavecenter.domain.family.report.repository.model;

import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class PlayerBanTarget {
    /**
     * 是否被封禁
     */
    private boolean ban;

    /**
     * 解禁时间
     */
    private Long unBlockTime;

    /**
     * 是否是主播跳槽的封禁原因
     */
    private boolean banByWaveReport;

    /**
     * 是否存在举报记录
     */
    private boolean existReportRecord;

}