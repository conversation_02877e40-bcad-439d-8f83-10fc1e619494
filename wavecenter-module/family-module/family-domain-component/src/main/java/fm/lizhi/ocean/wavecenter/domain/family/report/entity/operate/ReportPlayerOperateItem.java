package fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate;

import fm.lizhi.ocean.wavecenter.domain.eventBus.EventBusHolder;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.OperateStatusEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Player;
import fm.lizhi.ocean.wavecenter.domain.family.report.event.PlayerFinishOperateEvent;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 举报主播操作项
 */
@Data
@AllArgsConstructor
public class ReportPlayerOperateItem {
    private Long id;

    private Player operatedPlayer;

    /**
     * 具体操作
     */
    private Operate operate;

    /**
     * 操作状态
     */
    private OperateStatusEnum status;

    public static ReportPlayerOperateItem create(long id, Player operatedPlayer, Operate operate) {
        return new ReportPlayerOperateItem(id, operatedPlayer, operate, OperateStatusEnum.WAIT_OPERATE);
    }

    private ReportPlayerOperateItem() {

    }

    public boolean isOperated() {
        return status != OperateStatusEnum.WAIT_OPERATE;
    }

    public boolean isManual(){
        return operate.isManual();
    }

    public boolean isAutoOperate(){
        return !isManual();
    }

    public boolean isPermanent() {
        return operate.isPermanent();
    }

    public void finishOperate(Long applyId, Integer appId, OperateStatusEnum status) {
        if(isOperated()) {
            throw new IllegalArgumentException("操作已结束");
        }
        this.status = status;
        EventBusHolder.get().publishEvent(new PlayerFinishOperateEvent(applyId, appId, this));
    }


    public boolean isSuccess() {
        return status == OperateStatusEnum.SUCCESS;
    }

}
