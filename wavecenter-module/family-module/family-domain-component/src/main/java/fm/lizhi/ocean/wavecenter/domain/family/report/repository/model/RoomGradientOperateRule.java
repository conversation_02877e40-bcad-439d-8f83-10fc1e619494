package fm.lizhi.ocean.wavecenter.domain.family.report.repository.model;

import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.Operate;
import lombok.Data;

/**
 * 房间违规操作规则-值对象
 */
@Data
public class RoomGradientOperateRule {
    /**
     * 第sequence次违规
     */
    private Integer sequence;

    /**
     * 处罚操作
     */
    private Operate operate;

    /**
     * 梯度<=当前处罚次数的
     */
    public boolean isMatch(int operateCount) {
        return operateCount >= sequence;
    }
}
