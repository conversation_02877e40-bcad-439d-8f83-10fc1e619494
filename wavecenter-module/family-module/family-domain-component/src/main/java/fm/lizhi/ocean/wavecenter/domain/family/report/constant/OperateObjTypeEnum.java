package fm.lizhi.ocean.wavecenter.domain.family.report.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum OperateObjTypeEnum {
    /**
     * 用户账号
     */
    ACCOUNT(1),


    /**
     * 厅的开播权限
     */
    ROOM_OPEN_LIVE(2),

    /**
     * 设备
     */
    DEVICE_ID(3);

    private final Integer code;

    public static OperateObjTypeEnum getByCode(Integer code) {
        for (OperateObjTypeEnum value : OperateObjTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
