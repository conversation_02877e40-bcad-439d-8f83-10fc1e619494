package fm.lizhi.ocean.wavecenter.domain.family.report.event;

import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.ReportRoomOperateItem;
import lombok.Getter;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationEvent;

import java.util.Date;

/**
 * 厅处罚完成事件
 */
@Getter
public class RoomFinishOperateEvent extends ApplicationEvent {

    @NotNull("applyId不能为空")
    private Long applyId;

    @NotNull("operateItem不能为空")
    private ReportRoomOperateItem operateItem;

    @NotNull("appId不能为空")
    private Integer appId;


    public RoomFinishOperateEvent(@NotNull Long applyId, @NotNull Integer appId, @NotNull ReportRoomOperateItem operateItem) {
        super(new Date());
        this.applyId = applyId;
        this.operateItem = operateItem;
        this.appId = appId;
    }
}
