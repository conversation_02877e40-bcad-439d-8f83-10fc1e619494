package fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.OperateStatusEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Player;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Room;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.OperateCommand;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.PlayerReportApplyRepository;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.model.RoomGradientOperateRule;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.model.RoomViolationRecordValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 厅处罚对象
 */
@Data
@Slf4j
public class RoomViolation {
    private Long id;

    private Long applyId;


    private Integer appId;

    private Player accusedUser;
    /**
     * 跳槽厅
     */
    private Room accusedRoom;

    private Room poachedRoom;


    /**
     * 当前周期 跳槽厅 处罚第currentOperateSeq次
     */
    private Integer currentOperateSeq;

    /**
     * 当前周期 跳槽厅 违规人数
     */
    private Long currentViolationUserSum;

    /**
     * 周期内 跳槽厅违规人数达到处罚的条件值
     */
    private Long totalConditionCount;



    /**
     * 关联的其他厅违规信息(同周期下，未进行惩罚的厅违规记录)
     */
    private List<RoomViolationRecordValue> otherRoomViolationRecord;


    /**
     * 最终处罚内容
     */
    private ReportRoomOperateItem operateItem;


    public static RoomViolation create(Integer appId, Long id, Long operateId, Long applyId, Player accusedUser, Room accusedRoom,
                                       Room poachedRoom, List<RoomViolationRecordValue> periodAllRoomInfos, Long conditionCountConfig, Integer currentOperateSeq,
                                       List<RoomGradientOperateRule> roomGradientOperateRules) {
        RoomViolation punishRoom = new RoomViolation();
        punishRoom.id = id;
        punishRoom.applyId = applyId;
        punishRoom.appId = appId;
        punishRoom.accusedUser = accusedUser;
        punishRoom.accusedRoom = accusedRoom;
        punishRoom.poachedRoom = poachedRoom;
        //同周期下，未进行惩罚的厅违规记录
        punishRoom.otherRoomViolationRecord = periodAllRoomInfos.stream().filter(v -> !v.getPunished()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(periodAllRoomInfos)) {
            punishRoom.totalConditionCount = conditionCountConfig;
        } else {
            //当前周期厅存在记录，则取其中的条件值
            punishRoom.totalConditionCount = periodAllRoomInfos.stream()
                    .map(RoomViolationRecordValue::getConditionCount)
                    .findAny()
                    .orElse(0L);
        }
        //当前一轮未处罚人数(未去重)
        Set<Long> noPunished = periodAllRoomInfos.stream().filter(v -> !v.getPunished()).map(RoomViolationRecordValue::getAccusedPlayerId).collect(Collectors.toSet());
        noPunished.add(accusedUser.getId());
        //当前周期所有轮次中，已处罚的人数
        Set<Long> punished = periodAllRoomInfos.stream().filter(RoomViolationRecordValue::getPunished).map(RoomViolationRecordValue::getAccusedPlayerId).collect(Collectors.toSet());
        log.info("cal room violationUserSum. appId={};applyId={};accusedUser={};accusedRoom={};noPunished={}",
                appId, applyId, accusedUser.getId(), accusedRoom.getId(), noPunished);
        //当前一轮未处罚人数 去掉 已经处罚的陪玩，目的就是去重
        punishRoom.currentViolationUserSum = noPunished.stream().filter(v -> !punished.contains(v)).count();
        // 达到 3 人及以上，执行厅封禁等额外逻辑（示例，需结合自然周、封禁升级规则）
        if(punishRoom.currentViolationUserSum >= punishRoom.totalConditionCount) {
            // 处罚次数加1
            punishRoom.currentOperateSeq = currentOperateSeq + 1;
            // 根据梯度处罚规则 获取当前命中的处罚操作
            roomGradientOperateRules.stream()
                    //按梯度倒排
                    .sorted(Comparator.comparingInt(RoomGradientOperateRule::getSequence).reversed())
                    //进行匹配
                    .filter(operateRule -> operateRule.isMatch(punishRoom.currentOperateSeq))
                    .findFirst()
                    .ifPresent(operateRule -> {
                        punishRoom.operateItem = ReportRoomOperateItem.create(operateId, operateRule.getOperate(), accusedRoom,
                                punishRoom.currentOperateSeq, punishRoom.currentViolationUserSum);
                        log.info("operate room. appId={};applyId={};accusedUser={};accusedRoom={};operate:{}",
                                punishRoom.appId, applyId, accusedUser.getId(), accusedRoom.getId(), operateRule.getOperate());
                    });
        } else {
            punishRoom.currentOperateSeq = currentOperateSeq;
        }
        log.info("punish room. appId={};applyId={};accusedUser={};accusedRoom={};totalConditionCount={};currentOperateSeq={};currentViolationUserSum={};operateItem:{};otherRoomViolationRecord={}",
                punishRoom.appId, applyId, accusedUser.getId(), accusedRoom.getId(), punishRoom.totalConditionCount,
                punishRoom.currentOperateSeq, punishRoom.currentViolationUserSum,
                punishRoom.operateItem == null ? "" : JsonUtils.toJsonString(punishRoom.operateItem), JsonUtils.toJsonString(punishRoom.otherRoomViolationRecord));
        return punishRoom;
    }

    public boolean isRoomHasOperate() {
        return operateItem != null;
    }


    public boolean isReachCondition() {
        return Objects.equals(totalConditionCount, currentViolationUserSum);
    }

    public boolean hasAutoOperate() {
        return operateItem != null && !operateItem.getOperate().isManual();
    }

    public Integer getOperateDuration() {
        return operateItem == null ? 0 : operateItem.getOperate().getOperateDuration();
    }
    public Date getOperateInvalidTime() {
        return operateItem == null ? null : operateItem.calOperateInvalidTime();
    }

    /**
     * 是否是永久封禁
     */
    public boolean isForeverBan() {
        return operateItem != null && operateItem.isPermanent();
    }



    /**
     * 厅处罚
     */
    public void operate(Long applyId, PlayerReportApplyRepository playerReportApplyRepository, OperateCommand command, String operator, String operateReason) {
        if(!isRoomHasOperate()) {
            log.info("no need to operate. appId={};applyId={};accusedUser={};accusedRoom={};currentOperateSeq={};currentViolationUserSum={}",
                    appId, applyId, accusedUser.getId(), accusedRoom.getId(), currentOperateSeq, currentViolationUserSum);
            return;
        }
        log.info("operate room. appId={};applyId={};accusedUser={};accusedRoom={};operateItem:{}",
                appId, applyId, accusedUser.getId(), accusedRoom.getId(), JsonUtils.toJsonString(operateItem));
        //非手动 执行操作
        if(!operateItem.getOperate().isManual()) {
            boolean operateResult = false;
            try {
                operateResult = command.markRoomBanStatus(appId, operateItem,"system_auto");
                log.info("operate room result. appId={};applyId={};accusedUser={};accusedRoom={};operateItem:{};result:{}",
                        appId, applyId, accusedUser.getId(), accusedRoom.getId(), JsonUtils.toJsonString(operateItem), operateResult);
            } catch (Exception e) {
                log.error("execute room operate error. appId={};applyId={};accusedUser={};accusedRoom={};operateItem:{}",
                       appId, applyId, accusedUser.getId(), accusedRoom.getId(), JsonUtils.toJsonString(operateItem), e);
            }
            this.operateItem.finishOperate(applyId, this.appId, operateResult ? OperateStatusEnum.SUCCESS : OperateStatusEnum.FAIL);
            playerReportApplyRepository.updateRoomViolation(this.operateItem, operator);
        }
    }

}
