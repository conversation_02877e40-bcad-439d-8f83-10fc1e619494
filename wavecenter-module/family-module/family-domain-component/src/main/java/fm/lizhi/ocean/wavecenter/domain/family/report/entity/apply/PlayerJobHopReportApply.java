package fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply;

import fm.lizhi.ocean.wavecenter.base.util.JsonUtils;
import fm.lizhi.ocean.wavecenter.domain.eventBus.EventBusHolder;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.JudgmentStatusEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.ReportTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Player;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Reporter;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Room;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.TimeRange;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.*;
import fm.lizhi.ocean.wavecenter.domain.family.report.event.PlayerReportApplyFinishJudgeEvent;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.OperateCommand;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.PlayerReportApplyRepository;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.model.RoomGradientOperateRule;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.model.RoomViolationRecordValue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;

/**
 * 举报单信息
 */
@Data
@Slf4j
public abstract class PlayerJobHopReportApply {

    protected Long id;

    protected Integer appId;
    /**
     * 举报者
     */
    protected Reporter reportUser;

    /**
     * 主播
     */
    protected Player accusedUser;

    /**
     * 被挖厅
     */
    protected Room poachedRoom;

    /**
     * 挖槽厅id
     */
    protected Room accusedRoom;

    /**
     * 判定的结果记录
     */
    protected JudgeResult judgeResult;

    protected String source;

    /**
     * 举报类型
     */
    protected ReportTypeEnum reportType;

    /**
     * 举报时间
     */
    protected Date submitTime;

    /**
     * 完成判定
     */
    public void finishJudgment(boolean isEstablished, PlayerReportApplyRepository applyRepository, OperateCommand operateCommand) {
        if (!judgeResult.isPending()) {
            throw new IllegalStateException("只有待处理的举报单可以完成判定");
        }
        this.judgeResult.finish(isEstablished);
        //更新状态和原因
        applyRepository.updateJudgeResult(this.getId(), this.judgeResult);
        //发送完成判定事件 做统计/私信等不依赖下面流程的动作
        EventBusHolder.get().publishEvent(new PlayerReportApplyFinishJudgeEvent(this));
    }

    /**
     * 构建厅惩罚项
     * @param id
     * @param applyRepository
     * @return
     */
    public RoomViolation buildRoomViolation(long id, long roomOperateId, PlayerReportApplyRepository applyRepository) {
        if(!isPass()) {
            throw new IllegalStateException("只有通过判定的举报单才能创建厅处罚单");
        }
        TimeRange timeRange = TimeRange.create(this.submitTime);
        Long roomViolationConditionCount = applyRepository.getReportRoomViolationConditionCount(appId);
        //获取厅梯度处理规则
        List<RoomGradientOperateRule> roomGradientOperateRulesConfig = applyRepository.getRoomGradientOperateRules(appId);
        //获取当前厅违规信息
        List<RoomViolationRecordValue> periodAllRoomInfos = applyRepository.findAllRoomInfoByPeriod(accusedRoom, timeRange);
        log.info("buildRoomViolation Current room violation: {}", JsonUtils.toJsonString(periodAllRoomInfos));
        Integer currentMaxSeq = applyRepository.getMaxOperateSeqByRoom(accusedRoom, timeRange);
        //构建厅违规信息
        return RoomViolation.create(this.appId, id, roomOperateId, this.id, accusedUser, accusedRoom, poachedRoom, periodAllRoomInfos,
                roomViolationConditionCount, currentMaxSeq, roomGradientOperateRulesConfig);
    }

    /**
     * 是否通过判定
     */
    public boolean isPass() {
        return judgeResult.getJudgmentStatus() == JudgmentStatusEnum.PASS;
    }

    /**
     * 构建主播处罚项
     */
    public abstract List<ReportPlayerOperateItem> buildPlayerOperateItems(PlayerReportApplyRepository applyRepository);

    /**
     * 根据罚单 发送系统消息 or 私信
     * @param operateOrder 先直接传进来，后面再收敛参数
     */
    public abstract void sendMessageAfterOperate(OperateOrder operateOrder);

    public abstract void store(PlayerReportApplyRepository playerReportApplyRepository);
}