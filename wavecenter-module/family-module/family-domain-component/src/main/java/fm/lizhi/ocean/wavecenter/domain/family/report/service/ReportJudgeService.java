package fm.lizhi.ocean.wavecenter.domain.family.report.service;

import fm.lizhi.ocean.wavecenter.domain.family.report.constant.JudgeReason;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.ReportTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Player;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Reporter;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Room;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.TimeRange;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply.JudgeResult;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply.MultiAccountPlayerJobHopReportApply;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply.OriginalAccountPlayerJobHopReportApply;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply.PlayerJobHopReportApply;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.OperateCommand;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.PlayerReportApplyRepository;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.PlayerSignInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static fm.lizhi.ocean.wavecenter.domain.family.report.constant.JudgeReason.*;

/**
 * 举报判定服务
 */
@Slf4j
@Component
public class ReportJudgeService {

    @Autowired
    private PlayerReportApplyRepository applyRepository;
    @Autowired
    private OperateCommand operateCommand;


    public void judgeJobHop(ReportTypeEnum reportType, PlayerJobHopReportApply apply) {
        boolean result;
        switch (reportType) {
            case JOB_HOPPING:
                result = judgeJobHop((MultiAccountPlayerJobHopReportApply) apply);
                break;
            case ORG_JOB_HOPPING:
                result = judgeJobHop((OriginalAccountPlayerJobHopReportApply) apply);
                break;
            default:
                log.error("[ReportJudgeService] judgeJobHop fail, applyId: {}, reportType: {}, reportUserId: {}, accusedUserId: {}, accusedRoomId: {}", 
                        apply.getId(), reportType, apply.getReportUser().getId(), apply.getAccusedUser().getId(), apply.getAccusedRoom().getId());
                return;
        }
        //完成判定
        apply.finishJudgment(result, applyRepository, operateCommand);
    }

    //大小号判定
    public boolean judgeJobHop(MultiAccountPlayerJobHopReportApply apply) {
        Player mainPlayer = apply.getMainPlayer();
        Player subPlayer = apply.getAccusedUser();

        Room accusedRoom = apply.getAccusedRoom();

        JudgeResult judgeResult = apply.getJudgeResult();
        //大号签约状态不是为未签约 或 解约已超过14天
        if (!(mainPlayer.isInSign() || mainPlayer.isUnSignedLessThan14Days())) {
            judgeResult.resetReason(JudgeReason.buildReason(mainPlayer, SMALL_ACCOUNT_REPORT_RESULT_NOT_SIGNED_OR_UNBOUND));
            log.warn("[ReportJudgeService] judgeJobHop fail1, applyId: {}, mainPlayerId: {}, subPlayerId: {}, reason: {}",
                    apply.getId(), mainPlayer.getId(), subPlayer.getId(), judgeResult.getReason());
            return false;
        }
        //大号签约或解约未满14天的厅与小号跳槽厅为同一公会
        if(applyRepository.isOpenSameFamilyCheck() && mainPlayer.isSameFamily(accusedRoom)) {
            judgeResult.resetReason(JudgeReason.buildReason(subPlayer, SMALL_ACCOUNT_REPORT_RESULT_SAME_FAMILY));
            log.warn("[ReportJudgeService] judgeJobHop fail2, applyId: {}, mainPlayerId: {}, subPlayerId: {}, familyIdreason: {}",
                    apply.getId(), mainPlayer.getId(), subPlayer.getId(), judgeResult.getReason());
            return false;
        }
        //大号的关联厅/公会 命中挖槽厅黑名单
        if (mainPlayer.hasEverSigned() && applyRepository.isMainPlayerRoomInBlackList(apply.getAppId(), mainPlayer.getSignRoom())) {
            judgeResult.resetReason(JudgeReason.buildReason(mainPlayer, subPlayer, SMALL_ACCOUNT_REPORT_RESULT_NO_ASSOCIATION));
            log.warn("[ReportJudgeService] judgeJobHop fail4, applyId: {}, mainPlayerId: {}, subPlayerId: {}, reason: {}",
                    apply.getId(), mainPlayer.getId(), subPlayer.getId(), judgeResult.getReason());
            return false;
        }

        //命中挖槽厅白名单
        if (applyRepository.isAccusedRoomInWhiteList(apply.getAppId(), accusedRoom)) {
            log.info("[ReportJudgeService] judgeJobHop in whiteList5, applyId: {}, reportUserId:{}, accusedUserId:{}, accusedRoomId:{}, isInWhiteList:{}",
                    apply.getId(), apply.getReportUser().getId(), apply.getAccusedUser().getId(), accusedRoom.getId(), true);
            judgeResult.resetReason(JudgeReason.buildReason(mainPlayer, subPlayer, SMALL_ACCOUNT_REPORT_RESULT_NO_ASSOCIATION));
            return false;
        }

        //小号因为创作者跳槽而封禁
        if (subPlayer.isBanByWaveReport()) {
            judgeResult.resetReason(JudgeReason.buildReason(mainPlayer, subPlayer, SMALL_ACCOUNT_REPORT_RESULT_REPEAT));
            log.warn("[ReportJudgeService] judgeJobHop fail3, applyId: {}, mainPlayerId: {}, subPlayerId: {}, reason: {}",
                    apply.getId(), mainPlayer.getId(), subPlayer.getId(), judgeResult.getReason());
            return false;
        }
        //对比A、B账号：【实名信息】、【关联设备】、【IP】
        String relatedReason;
        if(mainPlayer.isSameVerify(subPlayer)) {
            relatedReason = "实名";
        } else if(mainPlayer.isSameIp(subPlayer)) {
            relatedReason = "IP";
        } else if(mainPlayer.isSameDeviceId(subPlayer)) {
            relatedReason = "设备";
        } else {
            judgeResult.resetReason(JudgeReason.buildReason(mainPlayer, subPlayer, SMALL_ACCOUNT_REPORT_RESULT_NO_ASSOCIATION));
            log.warn("[ReportJudgeService] judgeJobHop fail6, applyId: {}, mainPlayerId: {}, subPlayerId: {}, reason: {}",
                    apply.getId(), mainPlayer.getId(), subPlayer.getId(), judgeResult.getReason());
            return false;
        }
        if(subPlayer.isSigningInRoom(accusedRoom.getId())) {
            if(mainPlayer.hasEverSigned() && mainPlayer.getSignDate().after(subPlayer.getSignDate())) {
                judgeResult.resetReason(JudgeReason.buildReason(subPlayer, SMALL_ACCOUNT_REPORT_RESULT_NO_JUMP_ACTIVITY));
                log.warn("[ReportJudgeService] judgeJobHop fail7, applyId: {}, mainPlayerId: {}, subPlayerId: {}, accusedRoomId: {}, reason: {}",
                        apply.getId(), mainPlayer.getId(), subPlayer.getId(), accusedRoom.getId(), judgeResult.getReason());
                return false;
            }
            judgeResult.resetReason(JudgeReason.buildReason(relatedReason, SMALL_ACCOUNT_SAME_WITH_BIG_SIGN));
        } else {
            //判断收益
            TimeRange timeRange = TimeRange.create(apply.getSubmitTime());
            Long violationIncome = subPlayer.getWallet().getViolationIncome(timeRange, subPlayer.getId(), accusedRoom.getId());
            Integer income = applyRepository.getReportViolationIncomeConfig(apply.getReportType(), apply.getAppId());
            if(violationIncome < income) {
                judgeResult.resetReason(JudgeReason.buildReason(subPlayer, SMALL_ACCOUNT_REPORT_RESULT_NO_JUMP_ACTIVITY));
                log.warn("[ReportJudgeService] judgeJobHop fail8, applyId: {}, subPlayerId: {}, accusedRoomId: {}, violationIncome: {}, income: {}, timeRange: {}",
                        apply.getId(), subPlayer.getId(), accusedRoom.getId(), violationIncome, income, timeRange);
                return false;
            }
            judgeResult.resetReason(JudgeReason.buildReason(relatedReason, SMALL_ACCOUNT_REPORT_RESULT_BANNED));
        }
        log.info("[ReportJudgeService] judgeJobHop success, applyId: {}, mainPlayerId: {}, subPlayerId: {}, reason={}",
                apply.getId(), mainPlayer.getId(), subPlayer.getId(), judgeResult.getReason());

        return true;
    }


    //原号判定
    public boolean judgeJobHop(OriginalAccountPlayerJobHopReportApply apply) {
        JudgeResult judgeResult = apply.getJudgeResult();
        Player accusedUser = apply.getAccusedUser();
        Room accusedRoom = apply.getAccusedRoom();
        Room poachedRoom = apply.getPoachedRoom();
        Reporter reportUser = apply.getReportUser();
        boolean banByWaveReport = accusedUser.isBanByWaveReport();
        log.info("[ReportJudgeService] judgeJobHop, applyId: {}, reportUserId:{} accusedUserId:{}, banByWaveReport: {}",
                apply.getId(), reportUser.getId(), accusedUser.getId(), banByWaveReport);
        if (banByWaveReport) {
            //已经被封禁处罚了，不管了
            judgeResult.resetReason(JudgeReason.buildReason(accusedUser, ORIGINAL_ACCOUNT_REPORT_RESULT_REPEAT));
            return false;
        }

        //跳槽厅在白名单中
        if (applyRepository.isAccusedRoomInWhiteList(apply.getAppId(), accusedRoom)) {
            log.info("[ReportJudgeService] judgeJobHop in whiteList, applyId: {}, reportUserId:{}, accusedUserId:{}, accusedRoomId:{}, isInWhiteList:{}",
                    apply.getId(), apply.getReportUser().getId(), accusedUser.getId(), accusedRoom.getId(), true);
            judgeResult.resetReason(JudgeReason.buildReason(accusedUser, ORIGINAL_ACCOUNT_REPORT_RESULT_NO_JUMP_ACTIVITY));
            return false;
        }

        //跳槽主播与原厅签约中解约不满14天
        if (!(accusedUser.isInSign() || accusedUser.isUnSignedLessThan14Days())) {
            log.info("[ReportJudgeService] judgeJobHop not sign, applyId: {}, reportUserId:{}, accusedUserId:{}, accusedRoomId:{}, isInSign:{}, isUnSignedLessThan14Days:{}",
                    apply.getId(), reportUser.getId(), accusedUser.getId(), accusedRoom.getId(), accusedUser.isInSign(), accusedUser.isUnSignedLessThan14Days());
            judgeResult.resetReason(JudgeReason.buildReason(accusedUser, ORIGINAL_ACCOUNT_REPORT_RESULT_NOT_SIGNED_OR_UNBOUND));
            return false;
        }

        //跳槽厅与原厅签约的是同一个家族，举报不成功
        if (applyRepository.isOpenSameFamilyCheck() && accusedRoom.isSameFamily(poachedRoom)) {
            log.info("[ReportJudgeService] judgeJobHop same family, applyId: {}, reportUserId:{}, accusedUserId:{}, accusedRoomId:{}, poachedRoomId:{}, isSameFamily:{}",
                    apply.getId(), reportUser.getId(), accusedUser.getId(), accusedRoom.getId(), poachedRoom.getId(), true);
            judgeResult.resetReason(JudgeReason.buildReason(accusedUser, ORIGINAL_ACCOUNT_REPORT_RESULT_SAME_FAMILY));
            return false;
        }

        TimeRange timeRange = TimeRange.create(apply.getSubmitTime());
        Long violationIncome = accusedUser.getWallet().getViolationIncome(timeRange, accusedUser.getId(), accusedRoom.getId());
        Integer income = applyRepository.getReportViolationIncomeConfig(apply.getReportType(), apply.getAppId());
        log.info("[ReportJudgeService] judgeJobHop violationIncome, applyId: {}, reportUserId:{}, accusedUserId:{}, accusedRoomId:{}, poachedRoomId:{}, violationIncome:{}, income={}, timeRange={}",
                apply.getId(), reportUser.getId(), accusedUser.getId(), accusedRoom.getId(), poachedRoom.getId(), violationIncome, income, timeRange);
        if(violationIncome < income) {
            judgeResult.resetReason(JudgeReason.buildReason(accusedUser, ORIGINAL_ACCOUNT_REPORT_RESULT_NO_JUMP_ACTIVITY));
            return false;
        }
        judgeResult.resetReason(ORIGINAL_ACCOUNT_REPORT_RESULT_BANNED_NO_HALL_BAN);
        log.info("[ReportJudgeService] judgeJobHop success, applyId: {}, reportUserId:{}, accusedUserId:{}, accusedRoomId:{}, poachedRoomId:{}",
                apply.getId(), reportUser.getId(), accusedUser.getId(), accusedRoom.getId(), poachedRoom.getId());


        return true;
    }

}