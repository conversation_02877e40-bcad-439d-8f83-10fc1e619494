package fm.lizhi.ocean.wavecenter.domain.family.report.repository;

import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.ReportPlayerOperateItem;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.ReportRoomOperateItem;
import org.springframework.stereotype.Component;

/**
 * 举报操作命令
 */
@Component
public interface OperateCommand {

    /**
     * 标记用户封禁状态，进行提示，私信通知
     */
    boolean markUserBanStatus(Integer appId, ReportPlayerOperateItem operateItem, String operator, Long accusedRoomId, String operateReason);


    /**
     * 标记房间封禁状态，进行提示，私信通知
     */
    boolean markRoomBanStatus(Integer appId, ReportRoomOperateItem operateItem, String  operator);

    /**
     * 发送web站站内私信
     *
     * @param appId   appId
     * @param userId  收信人
     * @param title   标题
     * @param content 内容
     * @return 发送结果
     */
    boolean sendWebSystemMessage(Integer appId, Long userId, String title, String content);

    /**
     * 发送用户私信
     */
    void sendUserMessage(Integer appId, Long receiverId, String content);

    /**
     * 给家族的家族长发送私信
     */
    void sendFamilyMessage(Integer appId, Long familyId, String title, String content);

}
