package fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate;

import fm.lizhi.ocean.wavecenter.domain.eventBus.EventBusHolder;
import fm.lizhi.ocean.wavecenter.domain.family.report.constant.OperateStatusEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Room;
import fm.lizhi.ocean.wavecenter.domain.family.report.event.RoomFinishOperateEvent;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;

/**
 * 举报厅操作项
 */
@Data
@AllArgsConstructor
public class ReportRoomOperateItem {
    private Long id;

    private Room operatedRoom;
    /**
     * 操作对象-值对象
     */
    private Operate operate;

    /**
     * 当前周期 跳槽厅 处罚第currentOperateSeq次
     */
    private Integer currentOperateSeq;

    /**
     * 当前周期 跳槽厅 违规人数
     */
    private Long currentViolationUserSum;

    /**
     * 操作状态
     */
    private OperateStatusEnum status;

    private Date operateTime;

    public static ReportRoomOperateItem create(long id, Operate operate,
                                               Room accusedRoom,
                                               Integer currentOperateSeq,
                                               Long currentViolationUserSum) {
        return new ReportRoomOperateItem(id, accusedRoom,
                operate,
                currentOperateSeq,
                currentViolationUserSum,
                OperateStatusEnum.WAIT_OPERATE, null);
    }


    public void finishOperate(Long applyId, Integer appId, OperateStatusEnum status) {
        if(isOperated()) {
            throw new IllegalArgumentException("操作已结束");
        }
        this.operateTime = new Date();
        this.status = status;
        EventBusHolder.get().publishEvent(new RoomFinishOperateEvent(applyId, appId, this));
    }

    public Date calOperateInvalidTime() {
        return operateTime == null ? null : new Date(operateTime.getTime() + operate.getOperateDuration() * 60 * 1000);
    }

    public boolean isOperated() {
        return status != OperateStatusEnum.WAIT_OPERATE;
    }

    public boolean isSuccess() {
        return status == OperateStatusEnum.SUCCESS;
    }

    public boolean isPermanent() {
        return operate.isPermanent();
    }
}
