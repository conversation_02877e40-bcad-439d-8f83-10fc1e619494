package fm.lizhi.ocean.wavecenter.domain.family.report.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ReportTypeEnum {
    JOB_HOPPING(6, "大小号跳槽"),
    ORG_JOB_HOPPING(7, "原号跳槽");

    private final int code;
    private final String description;

    public static ReportTypeEnum findType(Integer reportType) {
        for (ReportTypeEnum type : values()) {
            if (type.code == reportType) {
                return type;
            }
        }
        return null;
    }
}
