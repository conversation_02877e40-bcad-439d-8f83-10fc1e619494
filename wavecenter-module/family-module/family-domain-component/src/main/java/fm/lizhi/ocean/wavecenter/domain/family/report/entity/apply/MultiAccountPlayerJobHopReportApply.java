package fm.lizhi.ocean.wavecenter.domain.family.report.entity.apply;

import fm.lizhi.ocean.wavecenter.domain.family.report.constant.ReportTypeEnum;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Player;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Reporter;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.Room;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.OperateOrder;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.ReportPlayerOperateItem;
import fm.lizhi.ocean.wavecenter.domain.family.report.entity.operate.RoomViolation;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.OperateCommand;
import fm.lizhi.ocean.wavecenter.domain.family.report.repository.PlayerReportApplyRepository;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import static fm.lizhi.ocean.wavecenter.domain.family.report.constant.JudgeReason.*;


/**
 * 大小号跳槽举报申请
 */
@Data
public class MultiAccountPlayerJobHopReportApply extends PlayerJobHopReportApply {


    /**
     * 被举报的大号
     */
    private Player mainPlayer;

    /**
     * 工厂方法 - 创建新对象 (完整参数版本)
     *
     * @param id 举报ID
     * @param appId 应用ID
     * @param reportUser 举报者
     * @param accusedRoom 挖槽厅
     * @param subPlayer 被举报的小号
     * @param mainPlayer 被举报的大号
     * @return 新创建的MultiAccountPlayerJobHopReportApply实例
     */
    public static MultiAccountPlayerJobHopReportApply create(Long id, Integer appId,
                                                             Reporter reportUser,
                                                             Player subPlayer,
                                                             Player mainPlayer,
                                                             Room accusedRoom,
                                                             String source) {
        MultiAccountPlayerJobHopReportApply apply = new MultiAccountPlayerJobHopReportApply();
        apply.setId(id);
        apply.setAppId(appId);
        apply.setReportUser(reportUser);
        //被挖厅就是大号的最近签约厅
        apply.setPoachedRoom(mainPlayer.getSignRoom());
        apply.setAccusedRoom(accusedRoom);
        apply.setAccusedUser(subPlayer);
        apply.setMainPlayer(mainPlayer);
        apply.setJudgeResult(JudgeResult.create());
        apply.setSource(source);
        apply.setReportType(ReportTypeEnum.JOB_HOPPING);
        apply.setSubmitTime(new Date());
        return apply;
    }

    /**
     * 私有构造函数，防止外部直接使用 new 创建实例
     */
    private MultiAccountPlayerJobHopReportApply() {
    }

    @Override
    public List<ReportPlayerOperateItem> buildPlayerOperateItems(PlayerReportApplyRepository applyRepository) {
        if(!isPass()) {
            throw new IllegalStateException("大小号-只有通过判定的举报单才能创建处罚单");
        }
        // 创建大小号跳槽主播处罚项
        return applyRepository.createPlayerReportOperate(appId, accusedUser, mainPlayer, accusedRoom);
    }

    @Override
    public void sendMessageAfterOperate(OperateOrder operateOrder) {
        RoomViolation roomViolation = operateOrder.getRoomViolation();
        OperateCommand command = operateOrder.getCommand();
        //是否有自动处罚厅的行为
        boolean roomPunish = operateOrder.hasRoomAutoOperate();
        boolean mainPlayerPunished = operateOrder.getItems().stream().anyMatch(item -> Objects.equals(item.getOperatedPlayer().getId(), mainPlayer.getId()));


        String  reportUserMessage;
        if(roomPunish) {
            Integer operateDuration = roomViolation.getOperateDuration();
            if(mainPlayerPunished) {
                reportUserMessage = buildBanReason(mainPlayer, accusedUser, accusedRoom, operateDuration, roomViolation.isForeverBan(), SMALL_ACCOUNT_REPORT_RESULT_BOTH_BANNED_WITH_HALL_BAN);
            } else {
                reportUserMessage = buildBanRoomReason(accusedUser, accusedRoom, operateDuration, roomViolation.isForeverBan(), SMALL_ACCOUNT_REPORT_RESULT_SMALL_BANNED_WITH_HALL_BAN);
            }
        } else {
            if(mainPlayerPunished) {
                reportUserMessage = buildReason(mainPlayer, accusedUser, SMALL_ACCOUNT_REPORT_RESULT_BOTH_BANNED);
            } else {
                reportUserMessage = buildReason(accusedUser, SMALL_ACCOUNT_REPORT_RESULT_SMALL_BANNED);
            }

            String accusedRoomMessage = buildReason(accusedRoom, accusedUser, ORIGINAL_ACCOUNT_REPORT_RESULT_BANNED_TO_ACCUSED_ROOM);
            //私信挖槽厅主+web站系统消息
            command.sendUserMessage(this.appId, this.accusedRoom.getId(), accusedRoomMessage);
            command.sendWebSystemMessage(this.appId, this.accusedRoom.getId(), "违规通知", accusedRoomMessage);
        }
        //私信举报者+web站系统消息
        command.sendUserMessage(this.appId, this.reportUser.getId(), reportUserMessage);
        command.sendWebSystemMessage(this.appId, this.reportUser.getId(), "「大小号跳槽」举报结果", reportUserMessage);
    }

    @Override
    public void store(PlayerReportApplyRepository playerReportApplyRepository) {
        playerReportApplyRepository.insert(this);
    }


}