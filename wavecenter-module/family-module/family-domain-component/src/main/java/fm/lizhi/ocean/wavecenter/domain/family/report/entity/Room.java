package fm.lizhi.ocean.wavecenter.domain.family.report.entity;

import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 厅对象
 */
@Data
@Slf4j
@ToString
public class Room {
    /**
     * 厅主id
     */
    private Long id;

    private String band;

    private String name;

    private String avatar;
    /**
     * 家族id
     */
    private Long familyId;

    /**
     * 公会码
     */
    private String societyCode;



    public static Room create(Long id, String name, String band, String avatar, Long familyId, String societyCode) {
        Room room = new Room();
        room.id = id;
        room.name = name;
        room.band = band;
        room.familyId = familyId;
        room.avatar = avatar;
        room.societyCode = societyCode;
        return room;
    }


    //行为1 该厅属于指定公会


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Room room = (Room) o;
        return id.equals(room.id);
    }


    /**
     * 判断两个厅是否属于同一家族
     * @param other
     * @return
     */
    public boolean isSameFamily(Room other) {
        if(other == null) {
            return false;
        }
        log.info("isSameFamily;self={};other={}", this, other);
        //是否是同个工会编码
        if(this.societyCode != null && Objects.equals(this.societyCode, other.getSocietyCode())) {
            return true;
        }
        //是否是同个家族id
        return Objects.equals(this.familyId, other.getFamilyId());
    }
}
