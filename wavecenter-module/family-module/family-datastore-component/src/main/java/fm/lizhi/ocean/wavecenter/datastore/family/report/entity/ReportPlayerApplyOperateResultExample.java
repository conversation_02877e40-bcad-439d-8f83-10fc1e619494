package fm.lizhi.ocean.wavecenter.datastore.family.report.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportPlayerApplyOperateResultExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_operate_result
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public ReportPlayerApplyOperateResultExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_operate_result
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_operate_result
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_operate_result
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_operate_result
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_operate_result
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_operate_result
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_operate_result
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_operate_result
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_operate_result
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_operate_result
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return fm.lizhi.ocean.wavecenter.datastore.family.report.entity.ReportPlayerApplyOperateResult.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table report_player_apply_operate_result
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdIsNull() {
            addCriterion("player_report_apply_record_id is null");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdIsNotNull() {
            addCriterion("player_report_apply_record_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdEqualTo(Long value) {
            addCriterion("player_report_apply_record_id =", value, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdNotEqualTo(Long value) {
            addCriterion("player_report_apply_record_id <>", value, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdGreaterThan(Long value) {
            addCriterion("player_report_apply_record_id >", value, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdGreaterThanOrEqualTo(Long value) {
            addCriterion("player_report_apply_record_id >=", value, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdLessThan(Long value) {
            addCriterion("player_report_apply_record_id <", value, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdLessThanOrEqualTo(Long value) {
            addCriterion("player_report_apply_record_id <=", value, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdIn(List<Long> values) {
            addCriterion("player_report_apply_record_id in", values, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdNotIn(List<Long> values) {
            addCriterion("player_report_apply_record_id not in", values, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdBetween(Long value1, Long value2) {
            addCriterion("player_report_apply_record_id between", value1, value2, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdNotBetween(Long value1, Long value2) {
            addCriterion("player_report_apply_record_id not between", value1, value2, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andOperatedPlayerIdIsNull() {
            addCriterion("operated_player_id is null");
            return (Criteria) this;
        }

        public Criteria andOperatedPlayerIdIsNotNull() {
            addCriterion("operated_player_id is not null");
            return (Criteria) this;
        }

        public Criteria andOperatedPlayerIdEqualTo(Long value) {
            addCriterion("operated_player_id =", value, "operatedPlayerId");
            return (Criteria) this;
        }

        public Criteria andOperatedPlayerIdNotEqualTo(Long value) {
            addCriterion("operated_player_id <>", value, "operatedPlayerId");
            return (Criteria) this;
        }

        public Criteria andOperatedPlayerIdGreaterThan(Long value) {
            addCriterion("operated_player_id >", value, "operatedPlayerId");
            return (Criteria) this;
        }

        public Criteria andOperatedPlayerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("operated_player_id >=", value, "operatedPlayerId");
            return (Criteria) this;
        }

        public Criteria andOperatedPlayerIdLessThan(Long value) {
            addCriterion("operated_player_id <", value, "operatedPlayerId");
            return (Criteria) this;
        }

        public Criteria andOperatedPlayerIdLessThanOrEqualTo(Long value) {
            addCriterion("operated_player_id <=", value, "operatedPlayerId");
            return (Criteria) this;
        }

        public Criteria andOperatedPlayerIdIn(List<Long> values) {
            addCriterion("operated_player_id in", values, "operatedPlayerId");
            return (Criteria) this;
        }

        public Criteria andOperatedPlayerIdNotIn(List<Long> values) {
            addCriterion("operated_player_id not in", values, "operatedPlayerId");
            return (Criteria) this;
        }

        public Criteria andOperatedPlayerIdBetween(Long value1, Long value2) {
            addCriterion("operated_player_id between", value1, value2, "operatedPlayerId");
            return (Criteria) this;
        }

        public Criteria andOperatedPlayerIdNotBetween(Long value1, Long value2) {
            addCriterion("operated_player_id not between", value1, value2, "operatedPlayerId");
            return (Criteria) this;
        }

        public Criteria andReportTypeIsNull() {
            addCriterion("report_type is null");
            return (Criteria) this;
        }

        public Criteria andReportTypeIsNotNull() {
            addCriterion("report_type is not null");
            return (Criteria) this;
        }

        public Criteria andReportTypeEqualTo(Integer value) {
            addCriterion("report_type =", value, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeNotEqualTo(Integer value) {
            addCriterion("report_type <>", value, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeGreaterThan(Integer value) {
            addCriterion("report_type >", value, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("report_type >=", value, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeLessThan(Integer value) {
            addCriterion("report_type <", value, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeLessThanOrEqualTo(Integer value) {
            addCriterion("report_type <=", value, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeIn(List<Integer> values) {
            addCriterion("report_type in", values, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeNotIn(List<Integer> values) {
            addCriterion("report_type not in", values, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeBetween(Integer value1, Integer value2) {
            addCriterion("report_type between", value1, value2, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("report_type not between", value1, value2, "reportType");
            return (Criteria) this;
        }

        public Criteria andOperateObjTypeIsNull() {
            addCriterion("operate_obj_type is null");
            return (Criteria) this;
        }

        public Criteria andOperateObjTypeIsNotNull() {
            addCriterion("operate_obj_type is not null");
            return (Criteria) this;
        }

        public Criteria andOperateObjTypeEqualTo(Integer value) {
            addCriterion("operate_obj_type =", value, "operateObjType");
            return (Criteria) this;
        }

        public Criteria andOperateObjTypeNotEqualTo(Integer value) {
            addCriterion("operate_obj_type <>", value, "operateObjType");
            return (Criteria) this;
        }

        public Criteria andOperateObjTypeGreaterThan(Integer value) {
            addCriterion("operate_obj_type >", value, "operateObjType");
            return (Criteria) this;
        }

        public Criteria andOperateObjTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("operate_obj_type >=", value, "operateObjType");
            return (Criteria) this;
        }

        public Criteria andOperateObjTypeLessThan(Integer value) {
            addCriterion("operate_obj_type <", value, "operateObjType");
            return (Criteria) this;
        }

        public Criteria andOperateObjTypeLessThanOrEqualTo(Integer value) {
            addCriterion("operate_obj_type <=", value, "operateObjType");
            return (Criteria) this;
        }

        public Criteria andOperateObjTypeIn(List<Integer> values) {
            addCriterion("operate_obj_type in", values, "operateObjType");
            return (Criteria) this;
        }

        public Criteria andOperateObjTypeNotIn(List<Integer> values) {
            addCriterion("operate_obj_type not in", values, "operateObjType");
            return (Criteria) this;
        }

        public Criteria andOperateObjTypeBetween(Integer value1, Integer value2) {
            addCriterion("operate_obj_type between", value1, value2, "operateObjType");
            return (Criteria) this;
        }

        public Criteria andOperateObjTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("operate_obj_type not between", value1, value2, "operateObjType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeIsNull() {
            addCriterion("operate_type is null");
            return (Criteria) this;
        }

        public Criteria andOperateTypeIsNotNull() {
            addCriterion("operate_type is not null");
            return (Criteria) this;
        }

        public Criteria andOperateTypeEqualTo(Integer value) {
            addCriterion("operate_type =", value, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeNotEqualTo(Integer value) {
            addCriterion("operate_type <>", value, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeGreaterThan(Integer value) {
            addCriterion("operate_type >", value, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("operate_type >=", value, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeLessThan(Integer value) {
            addCriterion("operate_type <", value, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeLessThanOrEqualTo(Integer value) {
            addCriterion("operate_type <=", value, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeIn(List<Integer> values) {
            addCriterion("operate_type in", values, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeNotIn(List<Integer> values) {
            addCriterion("operate_type not in", values, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeBetween(Integer value1, Integer value2) {
            addCriterion("operate_type between", value1, value2, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("operate_type not between", value1, value2, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateDurationIsNull() {
            addCriterion("operate_duration is null");
            return (Criteria) this;
        }

        public Criteria andOperateDurationIsNotNull() {
            addCriterion("operate_duration is not null");
            return (Criteria) this;
        }

        public Criteria andOperateDurationEqualTo(Integer value) {
            addCriterion("operate_duration =", value, "operateDuration");
            return (Criteria) this;
        }

        public Criteria andOperateDurationNotEqualTo(Integer value) {
            addCriterion("operate_duration <>", value, "operateDuration");
            return (Criteria) this;
        }

        public Criteria andOperateDurationGreaterThan(Integer value) {
            addCriterion("operate_duration >", value, "operateDuration");
            return (Criteria) this;
        }

        public Criteria andOperateDurationGreaterThanOrEqualTo(Integer value) {
            addCriterion("operate_duration >=", value, "operateDuration");
            return (Criteria) this;
        }

        public Criteria andOperateDurationLessThan(Integer value) {
            addCriterion("operate_duration <", value, "operateDuration");
            return (Criteria) this;
        }

        public Criteria andOperateDurationLessThanOrEqualTo(Integer value) {
            addCriterion("operate_duration <=", value, "operateDuration");
            return (Criteria) this;
        }

        public Criteria andOperateDurationIn(List<Integer> values) {
            addCriterion("operate_duration in", values, "operateDuration");
            return (Criteria) this;
        }

        public Criteria andOperateDurationNotIn(List<Integer> values) {
            addCriterion("operate_duration not in", values, "operateDuration");
            return (Criteria) this;
        }

        public Criteria andOperateDurationBetween(Integer value1, Integer value2) {
            addCriterion("operate_duration between", value1, value2, "operateDuration");
            return (Criteria) this;
        }

        public Criteria andOperateDurationNotBetween(Integer value1, Integer value2) {
            addCriterion("operate_duration not between", value1, value2, "operateDuration");
            return (Criteria) this;
        }

        public Criteria andManualIsNull() {
            addCriterion("manual is null");
            return (Criteria) this;
        }

        public Criteria andManualIsNotNull() {
            addCriterion("manual is not null");
            return (Criteria) this;
        }

        public Criteria andManualEqualTo(Boolean value) {
            addCriterion("manual =", value, "manual");
            return (Criteria) this;
        }

        public Criteria andManualNotEqualTo(Boolean value) {
            addCriterion("manual <>", value, "manual");
            return (Criteria) this;
        }

        public Criteria andManualGreaterThan(Boolean value) {
            addCriterion("manual >", value, "manual");
            return (Criteria) this;
        }

        public Criteria andManualGreaterThanOrEqualTo(Boolean value) {
            addCriterion("manual >=", value, "manual");
            return (Criteria) this;
        }

        public Criteria andManualLessThan(Boolean value) {
            addCriterion("manual <", value, "manual");
            return (Criteria) this;
        }

        public Criteria andManualLessThanOrEqualTo(Boolean value) {
            addCriterion("manual <=", value, "manual");
            return (Criteria) this;
        }

        public Criteria andManualIn(List<Boolean> values) {
            addCriterion("manual in", values, "manual");
            return (Criteria) this;
        }

        public Criteria andManualNotIn(List<Boolean> values) {
            addCriterion("manual not in", values, "manual");
            return (Criteria) this;
        }

        public Criteria andManualBetween(Boolean value1, Boolean value2) {
            addCriterion("manual between", value1, value2, "manual");
            return (Criteria) this;
        }

        public Criteria andManualNotBetween(Boolean value1, Boolean value2) {
            addCriterion("manual not between", value1, value2, "manual");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNull() {
            addCriterion("operator is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNotNull() {
            addCriterion("operator is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorEqualTo(String value) {
            addCriterion("operator =", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotEqualTo(String value) {
            addCriterion("operator <>", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThan(String value) {
            addCriterion("operator >", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThanOrEqualTo(String value) {
            addCriterion("operator >=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThan(String value) {
            addCriterion("operator <", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThanOrEqualTo(String value) {
            addCriterion("operator <=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLike(String value) {
            addCriterion("operator like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotLike(String value) {
            addCriterion("operator not like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorIn(List<String> values) {
            addCriterion("operator in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotIn(List<String> values) {
            addCriterion("operator not in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorBetween(String value1, String value2) {
            addCriterion("operator between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotBetween(String value1, String value2) {
            addCriterion("operator not between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNull() {
            addCriterion("deploy_env is null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNotNull() {
            addCriterion("deploy_env is not null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvEqualTo(String value) {
            addCriterion("deploy_env =", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotEqualTo(String value) {
            addCriterion("deploy_env <>", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThan(String value) {
            addCriterion("deploy_env >", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThanOrEqualTo(String value) {
            addCriterion("deploy_env >=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThan(String value) {
            addCriterion("deploy_env <", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThanOrEqualTo(String value) {
            addCriterion("deploy_env <=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLike(String value) {
            addCriterion("deploy_env like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotLike(String value) {
            addCriterion("deploy_env not like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIn(List<String> values) {
            addCriterion("deploy_env in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotIn(List<String> values) {
            addCriterion("deploy_env not in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvBetween(String value1, String value2) {
            addCriterion("deploy_env between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotBetween(String value1, String value2) {
            addCriterion("deploy_env not between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table report_player_apply_operate_result
     *
     * @mbg.generated do_not_delete_during_merge Thu Aug 21 15:29:11 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table report_player_apply_operate_result
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}