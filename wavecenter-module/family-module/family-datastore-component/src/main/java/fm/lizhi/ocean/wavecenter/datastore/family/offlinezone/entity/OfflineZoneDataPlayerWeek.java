package fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 线下专区-主播明细-周
 *
 * @date 2025-08-08 02:38:52
 */
@Table(name = "`offline_zone_data_player_week`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OfflineZoneDataPlayerWeek {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 周开始日期，格式YYYY-MM-DD
     */
    @Column(name= "`start_week_date`")
    private Date startWeekDate;

    /**
     * 周结束日期，格式YYYY-MM-DD
     */
    @Column(name= "`end_week_date`")
    private Date endWeekDate;

    /**
     * 主播ID
     */
    @Column(name= "`user_id`")
    private Long userId;

    /**
     * 主播名称
     */
    @Column(name= "`user_name`")
    private String userName;

    /**
     * 实名证件号码
     */
    @Column(name= "`id_card_number`")
    private String idCardNumber;

    /**
     * 实名姓名
     */
    @Column(name= "`id_name`")
    private String idName;

    /**
     * 公会ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 公会名称
     */
    @Column(name= "`family_name`")
    private String familyName;

    /**
     * 厅主ID
     */
    @Column(name= "`nj_id`")
    private Long njId;

    /**
     * 厅主名称
     */
    @Column(name= "`nj_name`")
    private String njName;

    /**
     * 签约时间
     */
    @Column(name= "`begin_sign_time`")
    private Date beginSignTime;

    /**
     * 主播分类：0 线下，1 线上
     */
    @Column(name= "`category`")
    private Integer category;

    /**
     * 国家
     */
    @Column(name= "`country`")
    private String country;

    /**
     * 省份
     */
    @Column(name= "`province`")
    private String province;

    /**
     * 城市
     */
    @Column(name= "`city`")
    private String city;

    /**
     * 主播收入
     */
    @Column(name= "`income`")
    private BigDecimal income;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", startWeekDate=").append(startWeekDate);
        sb.append(", endWeekDate=").append(endWeekDate);
        sb.append(", userId=").append(userId);
        sb.append(", userName=").append(userName);
        sb.append(", idCardNumber=").append(idCardNumber);
        sb.append(", idName=").append(idName);
        sb.append(", familyId=").append(familyId);
        sb.append(", familyName=").append(familyName);
        sb.append(", njId=").append(njId);
        sb.append(", njName=").append(njName);
        sb.append(", beginSignTime=").append(beginSignTime);
        sb.append(", category=").append(category);
        sb.append(", country=").append(country);
        sb.append(", province=").append(province);
        sb.append(", city=").append(city);
        sb.append(", income=").append(income);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}