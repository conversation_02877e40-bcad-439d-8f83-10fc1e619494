package fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OfflineZoneProtectionExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_protection
     *
     * @mbg.generated Mon Aug 11 21:54:38 CST 2025
     */
    public OfflineZoneProtectionExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_protection
     *
     * @mbg.generated Mon Aug 11 21:54:38 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_protection
     *
     * @mbg.generated Mon Aug 11 21:54:38 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_protection
     *
     * @mbg.generated Mon Aug 11 21:54:38 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_protection
     *
     * @mbg.generated Mon Aug 11 21:54:38 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_protection
     *
     * @mbg.generated Mon Aug 11 21:54:38 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_protection
     *
     * @mbg.generated Mon Aug 11 21:54:38 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_protection
     *
     * @mbg.generated Mon Aug 11 21:54:38 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_protection
     *
     * @mbg.generated Mon Aug 11 21:54:38 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_protection
     *
     * @mbg.generated Mon Aug 11 21:54:38 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_protection
     *
     * @mbg.generated Mon Aug 11 21:54:38 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneProtection.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table offline_zone_protection
     *
     * @mbg.generated Mon Aug 11 21:54:38 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNull() {
            addCriterion("family_id is null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNotNull() {
            addCriterion("family_id is not null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdEqualTo(Long value) {
            addCriterion("family_id =", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotEqualTo(Long value) {
            addCriterion("family_id <>", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThan(Long value) {
            addCriterion("family_id >", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("family_id >=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThan(Long value) {
            addCriterion("family_id <", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("family_id <=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIn(List<Long> values) {
            addCriterion("family_id in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotIn(List<Long> values) {
            addCriterion("family_id not in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdBetween(Long value1, Long value2) {
            addCriterion("family_id between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("family_id not between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdIsNull() {
            addCriterion("upload_user_id is null");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdIsNotNull() {
            addCriterion("upload_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdEqualTo(Long value) {
            addCriterion("upload_user_id =", value, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdNotEqualTo(Long value) {
            addCriterion("upload_user_id <>", value, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdGreaterThan(Long value) {
            addCriterion("upload_user_id >", value, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("upload_user_id >=", value, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdLessThan(Long value) {
            addCriterion("upload_user_id <", value, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdLessThanOrEqualTo(Long value) {
            addCriterion("upload_user_id <=", value, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdIn(List<Long> values) {
            addCriterion("upload_user_id in", values, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdNotIn(List<Long> values) {
            addCriterion("upload_user_id not in", values, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdBetween(Long value1, Long value2) {
            addCriterion("upload_user_id between", value1, value2, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdNotBetween(Long value1, Long value2) {
            addCriterion("upload_user_id not between", value1, value2, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andNjIdIsNull() {
            addCriterion("nj_id is null");
            return (Criteria) this;
        }

        public Criteria andNjIdIsNotNull() {
            addCriterion("nj_id is not null");
            return (Criteria) this;
        }

        public Criteria andNjIdEqualTo(Long value) {
            addCriterion("nj_id =", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotEqualTo(Long value) {
            addCriterion("nj_id <>", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdGreaterThan(Long value) {
            addCriterion("nj_id >", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdGreaterThanOrEqualTo(Long value) {
            addCriterion("nj_id >=", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdLessThan(Long value) {
            addCriterion("nj_id <", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdLessThanOrEqualTo(Long value) {
            addCriterion("nj_id <=", value, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdIn(List<Long> values) {
            addCriterion("nj_id in", values, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotIn(List<Long> values) {
            addCriterion("nj_id not in", values, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdBetween(Long value1, Long value2) {
            addCriterion("nj_id between", value1, value2, "njId");
            return (Criteria) this;
        }

        public Criteria andNjIdNotBetween(Long value1, Long value2) {
            addCriterion("nj_id not between", value1, value2, "njId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdIsNull() {
            addCriterion("player_id is null");
            return (Criteria) this;
        }

        public Criteria andPlayerIdIsNotNull() {
            addCriterion("player_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlayerIdEqualTo(Long value) {
            addCriterion("player_id =", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdNotEqualTo(Long value) {
            addCriterion("player_id <>", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdGreaterThan(Long value) {
            addCriterion("player_id >", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("player_id >=", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdLessThan(Long value) {
            addCriterion("player_id <", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdLessThanOrEqualTo(Long value) {
            addCriterion("player_id <=", value, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdIn(List<Long> values) {
            addCriterion("player_id in", values, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdNotIn(List<Long> values) {
            addCriterion("player_id not in", values, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdBetween(Long value1, Long value2) {
            addCriterion("player_id between", value1, value2, "playerId");
            return (Criteria) this;
        }

        public Criteria andPlayerIdNotBetween(Long value1, Long value2) {
            addCriterion("player_id not between", value1, value2, "playerId");
            return (Criteria) this;
        }

        public Criteria andAgreementStartTimeIsNull() {
            addCriterion("agreement_start_time is null");
            return (Criteria) this;
        }

        public Criteria andAgreementStartTimeIsNotNull() {
            addCriterion("agreement_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andAgreementStartTimeEqualTo(Date value) {
            addCriterion("agreement_start_time =", value, "agreementStartTime");
            return (Criteria) this;
        }

        public Criteria andAgreementStartTimeNotEqualTo(Date value) {
            addCriterion("agreement_start_time <>", value, "agreementStartTime");
            return (Criteria) this;
        }

        public Criteria andAgreementStartTimeGreaterThan(Date value) {
            addCriterion("agreement_start_time >", value, "agreementStartTime");
            return (Criteria) this;
        }

        public Criteria andAgreementStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("agreement_start_time >=", value, "agreementStartTime");
            return (Criteria) this;
        }

        public Criteria andAgreementStartTimeLessThan(Date value) {
            addCriterion("agreement_start_time <", value, "agreementStartTime");
            return (Criteria) this;
        }

        public Criteria andAgreementStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("agreement_start_time <=", value, "agreementStartTime");
            return (Criteria) this;
        }

        public Criteria andAgreementStartTimeIn(List<Date> values) {
            addCriterion("agreement_start_time in", values, "agreementStartTime");
            return (Criteria) this;
        }

        public Criteria andAgreementStartTimeNotIn(List<Date> values) {
            addCriterion("agreement_start_time not in", values, "agreementStartTime");
            return (Criteria) this;
        }

        public Criteria andAgreementStartTimeBetween(Date value1, Date value2) {
            addCriterion("agreement_start_time between", value1, value2, "agreementStartTime");
            return (Criteria) this;
        }

        public Criteria andAgreementStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("agreement_start_time not between", value1, value2, "agreementStartTime");
            return (Criteria) this;
        }

        public Criteria andAgreementEndTimeIsNull() {
            addCriterion("agreement_end_time is null");
            return (Criteria) this;
        }

        public Criteria andAgreementEndTimeIsNotNull() {
            addCriterion("agreement_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andAgreementEndTimeEqualTo(Date value) {
            addCriterion("agreement_end_time =", value, "agreementEndTime");
            return (Criteria) this;
        }

        public Criteria andAgreementEndTimeNotEqualTo(Date value) {
            addCriterion("agreement_end_time <>", value, "agreementEndTime");
            return (Criteria) this;
        }

        public Criteria andAgreementEndTimeGreaterThan(Date value) {
            addCriterion("agreement_end_time >", value, "agreementEndTime");
            return (Criteria) this;
        }

        public Criteria andAgreementEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("agreement_end_time >=", value, "agreementEndTime");
            return (Criteria) this;
        }

        public Criteria andAgreementEndTimeLessThan(Date value) {
            addCriterion("agreement_end_time <", value, "agreementEndTime");
            return (Criteria) this;
        }

        public Criteria andAgreementEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("agreement_end_time <=", value, "agreementEndTime");
            return (Criteria) this;
        }

        public Criteria andAgreementEndTimeIn(List<Date> values) {
            addCriterion("agreement_end_time in", values, "agreementEndTime");
            return (Criteria) this;
        }

        public Criteria andAgreementEndTimeNotIn(List<Date> values) {
            addCriterion("agreement_end_time not in", values, "agreementEndTime");
            return (Criteria) this;
        }

        public Criteria andAgreementEndTimeBetween(Date value1, Date value2) {
            addCriterion("agreement_end_time between", value1, value2, "agreementEndTime");
            return (Criteria) this;
        }

        public Criteria andAgreementEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("agreement_end_time not between", value1, value2, "agreementEndTime");
            return (Criteria) this;
        }

        public Criteria andAgreementUpdateTimeIsNull() {
            addCriterion("agreement_update_time is null");
            return (Criteria) this;
        }

        public Criteria andAgreementUpdateTimeIsNotNull() {
            addCriterion("agreement_update_time is not null");
            return (Criteria) this;
        }

        public Criteria andAgreementUpdateTimeEqualTo(Date value) {
            addCriterion("agreement_update_time =", value, "agreementUpdateTime");
            return (Criteria) this;
        }

        public Criteria andAgreementUpdateTimeNotEqualTo(Date value) {
            addCriterion("agreement_update_time <>", value, "agreementUpdateTime");
            return (Criteria) this;
        }

        public Criteria andAgreementUpdateTimeGreaterThan(Date value) {
            addCriterion("agreement_update_time >", value, "agreementUpdateTime");
            return (Criteria) this;
        }

        public Criteria andAgreementUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("agreement_update_time >=", value, "agreementUpdateTime");
            return (Criteria) this;
        }

        public Criteria andAgreementUpdateTimeLessThan(Date value) {
            addCriterion("agreement_update_time <", value, "agreementUpdateTime");
            return (Criteria) this;
        }

        public Criteria andAgreementUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("agreement_update_time <=", value, "agreementUpdateTime");
            return (Criteria) this;
        }

        public Criteria andAgreementUpdateTimeIn(List<Date> values) {
            addCriterion("agreement_update_time in", values, "agreementUpdateTime");
            return (Criteria) this;
        }

        public Criteria andAgreementUpdateTimeNotIn(List<Date> values) {
            addCriterion("agreement_update_time not in", values, "agreementUpdateTime");
            return (Criteria) this;
        }

        public Criteria andAgreementUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("agreement_update_time between", value1, value2, "agreementUpdateTime");
            return (Criteria) this;
        }

        public Criteria andAgreementUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("agreement_update_time not between", value1, value2, "agreementUpdateTime");
            return (Criteria) this;
        }

        public Criteria andStampSignIsNull() {
            addCriterion("stamp_sign is null");
            return (Criteria) this;
        }

        public Criteria andStampSignIsNotNull() {
            addCriterion("stamp_sign is not null");
            return (Criteria) this;
        }

        public Criteria andStampSignEqualTo(Boolean value) {
            addCriterion("stamp_sign =", value, "stampSign");
            return (Criteria) this;
        }

        public Criteria andStampSignNotEqualTo(Boolean value) {
            addCriterion("stamp_sign <>", value, "stampSign");
            return (Criteria) this;
        }

        public Criteria andStampSignGreaterThan(Boolean value) {
            addCriterion("stamp_sign >", value, "stampSign");
            return (Criteria) this;
        }

        public Criteria andStampSignGreaterThanOrEqualTo(Boolean value) {
            addCriterion("stamp_sign >=", value, "stampSign");
            return (Criteria) this;
        }

        public Criteria andStampSignLessThan(Boolean value) {
            addCriterion("stamp_sign <", value, "stampSign");
            return (Criteria) this;
        }

        public Criteria andStampSignLessThanOrEqualTo(Boolean value) {
            addCriterion("stamp_sign <=", value, "stampSign");
            return (Criteria) this;
        }

        public Criteria andStampSignIn(List<Boolean> values) {
            addCriterion("stamp_sign in", values, "stampSign");
            return (Criteria) this;
        }

        public Criteria andStampSignNotIn(List<Boolean> values) {
            addCriterion("stamp_sign not in", values, "stampSign");
            return (Criteria) this;
        }

        public Criteria andStampSignBetween(Boolean value1, Boolean value2) {
            addCriterion("stamp_sign between", value1, value2, "stampSign");
            return (Criteria) this;
        }

        public Criteria andStampSignNotBetween(Boolean value1, Boolean value2) {
            addCriterion("stamp_sign not between", value1, value2, "stampSign");
            return (Criteria) this;
        }

        public Criteria andPlayerAgreeIsNull() {
            addCriterion("player_agree is null");
            return (Criteria) this;
        }

        public Criteria andPlayerAgreeIsNotNull() {
            addCriterion("player_agree is not null");
            return (Criteria) this;
        }

        public Criteria andPlayerAgreeEqualTo(Integer value) {
            addCriterion("player_agree =", value, "playerAgree");
            return (Criteria) this;
        }

        public Criteria andPlayerAgreeNotEqualTo(Integer value) {
            addCriterion("player_agree <>", value, "playerAgree");
            return (Criteria) this;
        }

        public Criteria andPlayerAgreeGreaterThan(Integer value) {
            addCriterion("player_agree >", value, "playerAgree");
            return (Criteria) this;
        }

        public Criteria andPlayerAgreeGreaterThanOrEqualTo(Integer value) {
            addCriterion("player_agree >=", value, "playerAgree");
            return (Criteria) this;
        }

        public Criteria andPlayerAgreeLessThan(Integer value) {
            addCriterion("player_agree <", value, "playerAgree");
            return (Criteria) this;
        }

        public Criteria andPlayerAgreeLessThanOrEqualTo(Integer value) {
            addCriterion("player_agree <=", value, "playerAgree");
            return (Criteria) this;
        }

        public Criteria andPlayerAgreeIn(List<Integer> values) {
            addCriterion("player_agree in", values, "playerAgree");
            return (Criteria) this;
        }

        public Criteria andPlayerAgreeNotIn(List<Integer> values) {
            addCriterion("player_agree not in", values, "playerAgree");
            return (Criteria) this;
        }

        public Criteria andPlayerAgreeBetween(Integer value1, Integer value2) {
            addCriterion("player_agree between", value1, value2, "playerAgree");
            return (Criteria) this;
        }

        public Criteria andPlayerAgreeNotBetween(Integer value1, Integer value2) {
            addCriterion("player_agree not between", value1, value2, "playerAgree");
            return (Criteria) this;
        }

        public Criteria andArchivedIsNull() {
            addCriterion("archived is null");
            return (Criteria) this;
        }

        public Criteria andArchivedIsNotNull() {
            addCriterion("archived is not null");
            return (Criteria) this;
        }

        public Criteria andArchivedEqualTo(Boolean value) {
            addCriterion("archived =", value, "archived");
            return (Criteria) this;
        }

        public Criteria andArchivedNotEqualTo(Boolean value) {
            addCriterion("archived <>", value, "archived");
            return (Criteria) this;
        }

        public Criteria andArchivedGreaterThan(Boolean value) {
            addCriterion("archived >", value, "archived");
            return (Criteria) this;
        }

        public Criteria andArchivedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("archived >=", value, "archived");
            return (Criteria) this;
        }

        public Criteria andArchivedLessThan(Boolean value) {
            addCriterion("archived <", value, "archived");
            return (Criteria) this;
        }

        public Criteria andArchivedLessThanOrEqualTo(Boolean value) {
            addCriterion("archived <=", value, "archived");
            return (Criteria) this;
        }

        public Criteria andArchivedIn(List<Boolean> values) {
            addCriterion("archived in", values, "archived");
            return (Criteria) this;
        }

        public Criteria andArchivedNotIn(List<Boolean> values) {
            addCriterion("archived not in", values, "archived");
            return (Criteria) this;
        }

        public Criteria andArchivedBetween(Boolean value1, Boolean value2) {
            addCriterion("archived between", value1, value2, "archived");
            return (Criteria) this;
        }

        public Criteria andArchivedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("archived not between", value1, value2, "archived");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNull() {
            addCriterion("deploy_env is null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNotNull() {
            addCriterion("deploy_env is not null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvEqualTo(String value) {
            addCriterion("deploy_env =", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotEqualTo(String value) {
            addCriterion("deploy_env <>", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThan(String value) {
            addCriterion("deploy_env >", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThanOrEqualTo(String value) {
            addCriterion("deploy_env >=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThan(String value) {
            addCriterion("deploy_env <", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThanOrEqualTo(String value) {
            addCriterion("deploy_env <=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLike(String value) {
            addCriterion("deploy_env like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotLike(String value) {
            addCriterion("deploy_env not like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIn(List<String> values) {
            addCriterion("deploy_env in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotIn(List<String> values) {
            addCriterion("deploy_env not in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvBetween(String value1, String value2) {
            addCriterion("deploy_env between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotBetween(String value1, String value2) {
            addCriterion("deploy_env not between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNull() {
            addCriterion("operator is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNotNull() {
            addCriterion("operator is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorEqualTo(String value) {
            addCriterion("operator =", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotEqualTo(String value) {
            addCriterion("operator <>", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThan(String value) {
            addCriterion("operator >", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThanOrEqualTo(String value) {
            addCriterion("operator >=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThan(String value) {
            addCriterion("operator <", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThanOrEqualTo(String value) {
            addCriterion("operator <=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLike(String value) {
            addCriterion("operator like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotLike(String value) {
            addCriterion("operator not like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorIn(List<String> values) {
            addCriterion("operator in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotIn(List<String> values) {
            addCriterion("operator not in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorBetween(String value1, String value2) {
            addCriterion("operator between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotBetween(String value1, String value2) {
            addCriterion("operator not between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table offline_zone_protection
     *
     * @mbg.generated do_not_delete_during_merge Mon Aug 11 21:54:38 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table offline_zone_protection
     *
     * @mbg.generated Mon Aug 11 21:54:38 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}