package fm.lizhi.ocean.wavecenter.datastore.family.report.rediskey;


import fm.lizhi.ocean.wavecenter.base.constants.IRedisKey;
import fm.lizhi.ocean.wavecenter.base.util.KeyUtils;

/**
 * 玩家举报相关Redis Key
 */
public enum PlayerReportRedisKey implements IRedisKey {

    /**
     * 举报者成功举报记录
     * WAVECENTER_PLAYER_REPORT_PASS_APPLY_[appId]_[reporterId]
     * value=1
     */
    REPORTER_PASS_APPLY(true),
    
    /**
     * 被成功举报的挖槽厅记录
     * WAVECENTER_PLAYER_REPORT_ACCUSED_ROOM_[appId]_[reporterId]_[accusedUserId]_[accusedRoomId]
     * value=1
     */
    ACCUSED_ROOM(true),

    REPORTER_APPLY_LOCK(true);

    /**
     * 是否区分环境
     */
    private boolean afx;

    PlayerReportRedisKey() {
    }

    PlayerReportRedisKey(boolean afx) {
        this.afx = afx;
    }

    @Override
    public String getPrefix() {
        return KeyUtils.getPrefix("WAVECENTER_PLAYER_REPORT", afx);
    }

    @Override
    public String getName() {
        return this.name();
    }
}