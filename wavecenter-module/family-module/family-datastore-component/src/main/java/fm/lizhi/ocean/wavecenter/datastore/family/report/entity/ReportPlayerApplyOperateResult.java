package fm.lizhi.ocean.wavecenter.datastore.family.report.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 主播举报处理结果表
 *
 * @date 2025-08-21 03:29:11
 */
@Table(name = "`report_player_apply_operate_result`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReportPlayerApplyOperateResult {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 举报申请记录id
     */
    @Column(name= "`player_report_apply_record_id`")
    private Long playerReportApplyRecordId;

    /**
     * 被操作的主播id
     */
    @Column(name= "`operated_player_id`")
    private Long operatedPlayerId;

    /**
     * 冗余-举报类型，6:大小号跳槽，7:原号跳槽
     */
    @Column(name= "`report_type`")
    private Integer reportType;

    /**
     * 操作对象类型(1：账号，2：厅开播权限 3设备)
     */
    @Column(name= "`operate_obj_type`")
    private Integer operateObjType;

    /**
     * 操作类型(1：封禁，2：解禁)
     */
    @Column(name= "`operate_type`")
    private Integer operateType;

    /**
     * 操作时长;eg:设备封禁时长
     */
    @Column(name= "`operate_duration`")
    private Integer operateDuration;

    /**
     * 是否手动 0-否 1-是
     */
    @Column(name= "`manual`")
    private Boolean manual;

    /**
     * 状态 1：成功，2：失败
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 环境
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", playerReportApplyRecordId=").append(playerReportApplyRecordId);
        sb.append(", operatedPlayerId=").append(operatedPlayerId);
        sb.append(", reportType=").append(reportType);
        sb.append(", operateObjType=").append(operateObjType);
        sb.append(", operateType=").append(operateType);
        sb.append(", operateDuration=").append(operateDuration);
        sb.append(", manual=").append(manual);
        sb.append(", status=").append(status);
        sb.append(", operator=").append(operator);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}