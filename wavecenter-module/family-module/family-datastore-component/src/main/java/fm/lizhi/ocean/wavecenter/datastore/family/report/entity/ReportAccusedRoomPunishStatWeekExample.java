package fm.lizhi.ocean.wavecenter.datastore.family.report.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class ReportAccusedRoomPunishStatWeekExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_accused_room_punish_stat_week
     *
     * @mbg.generated Wed Aug 20 19:36:14 CST 2025
     */
    public ReportAccusedRoomPunishStatWeekExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_accused_room_punish_stat_week
     *
     * @mbg.generated Wed Aug 20 19:36:14 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_accused_room_punish_stat_week
     *
     * @mbg.generated Wed Aug 20 19:36:14 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_accused_room_punish_stat_week
     *
     * @mbg.generated Wed Aug 20 19:36:14 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_accused_room_punish_stat_week
     *
     * @mbg.generated Wed Aug 20 19:36:14 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_accused_room_punish_stat_week
     *
     * @mbg.generated Wed Aug 20 19:36:14 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_accused_room_punish_stat_week
     *
     * @mbg.generated Wed Aug 20 19:36:14 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_accused_room_punish_stat_week
     *
     * @mbg.generated Wed Aug 20 19:36:14 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_accused_room_punish_stat_week
     *
     * @mbg.generated Wed Aug 20 19:36:14 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_accused_room_punish_stat_week
     *
     * @mbg.generated Wed Aug 20 19:36:14 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_accused_room_punish_stat_week
     *
     * @mbg.generated Wed Aug 20 19:36:14 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return fm.lizhi.ocean.wavecenter.datastore.family.report.entity.ReportAccusedRoomPunishStatWeek.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table report_accused_room_punish_stat_week
     *
     * @mbg.generated Wed Aug 20 19:36:14 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateIsNull() {
            addCriterion("start_week_date is null");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateIsNotNull() {
            addCriterion("start_week_date is not null");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date =", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date <>", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateGreaterThan(Date value) {
            addCriterionForJDBCDate("start_week_date >", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date >=", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateLessThan(Date value) {
            addCriterionForJDBCDate("start_week_date <", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date <=", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateIn(List<Date> values) {
            addCriterionForJDBCDate("start_week_date in", values, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("start_week_date not in", values, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("start_week_date between", value1, value2, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("start_week_date not between", value1, value2, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateIsNull() {
            addCriterion("end_week_date is null");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateIsNotNull() {
            addCriterion("end_week_date is not null");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date =", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date <>", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateGreaterThan(Date value) {
            addCriterionForJDBCDate("end_week_date >", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date >=", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateLessThan(Date value) {
            addCriterionForJDBCDate("end_week_date <", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date <=", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateIn(List<Date> values) {
            addCriterionForJDBCDate("end_week_date in", values, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("end_week_date not in", values, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("end_week_date between", value1, value2, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("end_week_date not between", value1, value2, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdsIsNull() {
            addCriterion("accused_user_ids is null");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdsIsNotNull() {
            addCriterion("accused_user_ids is not null");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdsEqualTo(String value) {
            addCriterion("accused_user_ids =", value, "accusedUserIds");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdsNotEqualTo(String value) {
            addCriterion("accused_user_ids <>", value, "accusedUserIds");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdsGreaterThan(String value) {
            addCriterion("accused_user_ids >", value, "accusedUserIds");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdsGreaterThanOrEqualTo(String value) {
            addCriterion("accused_user_ids >=", value, "accusedUserIds");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdsLessThan(String value) {
            addCriterion("accused_user_ids <", value, "accusedUserIds");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdsLessThanOrEqualTo(String value) {
            addCriterion("accused_user_ids <=", value, "accusedUserIds");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdsLike(String value) {
            addCriterion("accused_user_ids like", value, "accusedUserIds");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdsNotLike(String value) {
            addCriterion("accused_user_ids not like", value, "accusedUserIds");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdsIn(List<String> values) {
            addCriterion("accused_user_ids in", values, "accusedUserIds");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdsNotIn(List<String> values) {
            addCriterion("accused_user_ids not in", values, "accusedUserIds");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdsBetween(String value1, String value2) {
            addCriterion("accused_user_ids between", value1, value2, "accusedUserIds");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdsNotBetween(String value1, String value2) {
            addCriterion("accused_user_ids not between", value1, value2, "accusedUserIds");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdIsNull() {
            addCriterion("accused_room_id is null");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdIsNotNull() {
            addCriterion("accused_room_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdEqualTo(Long value) {
            addCriterion("accused_room_id =", value, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdNotEqualTo(Long value) {
            addCriterion("accused_room_id <>", value, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdGreaterThan(Long value) {
            addCriterion("accused_room_id >", value, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdGreaterThanOrEqualTo(Long value) {
            addCriterion("accused_room_id >=", value, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdLessThan(Long value) {
            addCriterion("accused_room_id <", value, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdLessThanOrEqualTo(Long value) {
            addCriterion("accused_room_id <=", value, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdIn(List<Long> values) {
            addCriterion("accused_room_id in", values, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdNotIn(List<Long> values) {
            addCriterion("accused_room_id not in", values, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdBetween(Long value1, Long value2) {
            addCriterion("accused_room_id between", value1, value2, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdNotBetween(Long value1, Long value2) {
            addCriterion("accused_room_id not between", value1, value2, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdIsNull() {
            addCriterion("accused_family_id is null");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdIsNotNull() {
            addCriterion("accused_family_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdEqualTo(Long value) {
            addCriterion("accused_family_id =", value, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdNotEqualTo(Long value) {
            addCriterion("accused_family_id <>", value, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdGreaterThan(Long value) {
            addCriterion("accused_family_id >", value, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("accused_family_id >=", value, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdLessThan(Long value) {
            addCriterion("accused_family_id <", value, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("accused_family_id <=", value, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdIn(List<Long> values) {
            addCriterion("accused_family_id in", values, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdNotIn(List<Long> values) {
            addCriterion("accused_family_id not in", values, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdBetween(Long value1, Long value2) {
            addCriterion("accused_family_id between", value1, value2, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("accused_family_id not between", value1, value2, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andRankIsNull() {
            addCriterion("rank is null");
            return (Criteria) this;
        }

        public Criteria andRankIsNotNull() {
            addCriterion("rank is not null");
            return (Criteria) this;
        }

        public Criteria andRankEqualTo(Integer value) {
            addCriterion("rank =", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankNotEqualTo(Integer value) {
            addCriterion("rank <>", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankGreaterThan(Integer value) {
            addCriterion("rank >", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankGreaterThanOrEqualTo(Integer value) {
            addCriterion("rank >=", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankLessThan(Integer value) {
            addCriterion("rank <", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankLessThanOrEqualTo(Integer value) {
            addCriterion("rank <=", value, "rank");
            return (Criteria) this;
        }

        public Criteria andRankIn(List<Integer> values) {
            addCriterion("rank in", values, "rank");
            return (Criteria) this;
        }

        public Criteria andRankNotIn(List<Integer> values) {
            addCriterion("rank not in", values, "rank");
            return (Criteria) this;
        }

        public Criteria andRankBetween(Integer value1, Integer value2) {
            addCriterion("rank between", value1, value2, "rank");
            return (Criteria) this;
        }

        public Criteria andRankNotBetween(Integer value1, Integer value2) {
            addCriterion("rank not between", value1, value2, "rank");
            return (Criteria) this;
        }

        public Criteria andCountIsNull() {
            addCriterion("count is null");
            return (Criteria) this;
        }

        public Criteria andCountIsNotNull() {
            addCriterion("count is not null");
            return (Criteria) this;
        }

        public Criteria andCountEqualTo(Integer value) {
            addCriterion("count =", value, "count");
            return (Criteria) this;
        }

        public Criteria andCountNotEqualTo(Integer value) {
            addCriterion("count <>", value, "count");
            return (Criteria) this;
        }

        public Criteria andCountGreaterThan(Integer value) {
            addCriterion("count >", value, "count");
            return (Criteria) this;
        }

        public Criteria andCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("count >=", value, "count");
            return (Criteria) this;
        }

        public Criteria andCountLessThan(Integer value) {
            addCriterion("count <", value, "count");
            return (Criteria) this;
        }

        public Criteria andCountLessThanOrEqualTo(Integer value) {
            addCriterion("count <=", value, "count");
            return (Criteria) this;
        }

        public Criteria andCountIn(List<Integer> values) {
            addCriterion("count in", values, "count");
            return (Criteria) this;
        }

        public Criteria andCountNotIn(List<Integer> values) {
            addCriterion("count not in", values, "count");
            return (Criteria) this;
        }

        public Criteria andCountBetween(Integer value1, Integer value2) {
            addCriterion("count between", value1, value2, "count");
            return (Criteria) this;
        }

        public Criteria andCountNotBetween(Integer value1, Integer value2) {
            addCriterion("count not between", value1, value2, "count");
            return (Criteria) this;
        }

        public Criteria andPunishReasonIsNull() {
            addCriterion("punish_reason is null");
            return (Criteria) this;
        }

        public Criteria andPunishReasonIsNotNull() {
            addCriterion("punish_reason is not null");
            return (Criteria) this;
        }

        public Criteria andPunishReasonEqualTo(String value) {
            addCriterion("punish_reason =", value, "punishReason");
            return (Criteria) this;
        }

        public Criteria andPunishReasonNotEqualTo(String value) {
            addCriterion("punish_reason <>", value, "punishReason");
            return (Criteria) this;
        }

        public Criteria andPunishReasonGreaterThan(String value) {
            addCriterion("punish_reason >", value, "punishReason");
            return (Criteria) this;
        }

        public Criteria andPunishReasonGreaterThanOrEqualTo(String value) {
            addCriterion("punish_reason >=", value, "punishReason");
            return (Criteria) this;
        }

        public Criteria andPunishReasonLessThan(String value) {
            addCriterion("punish_reason <", value, "punishReason");
            return (Criteria) this;
        }

        public Criteria andPunishReasonLessThanOrEqualTo(String value) {
            addCriterion("punish_reason <=", value, "punishReason");
            return (Criteria) this;
        }

        public Criteria andPunishReasonLike(String value) {
            addCriterion("punish_reason like", value, "punishReason");
            return (Criteria) this;
        }

        public Criteria andPunishReasonNotLike(String value) {
            addCriterion("punish_reason not like", value, "punishReason");
            return (Criteria) this;
        }

        public Criteria andPunishReasonIn(List<String> values) {
            addCriterion("punish_reason in", values, "punishReason");
            return (Criteria) this;
        }

        public Criteria andPunishReasonNotIn(List<String> values) {
            addCriterion("punish_reason not in", values, "punishReason");
            return (Criteria) this;
        }

        public Criteria andPunishReasonBetween(String value1, String value2) {
            addCriterion("punish_reason between", value1, value2, "punishReason");
            return (Criteria) this;
        }

        public Criteria andPunishReasonNotBetween(String value1, String value2) {
            addCriterion("punish_reason not between", value1, value2, "punishReason");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNull() {
            addCriterion("deploy_env is null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNotNull() {
            addCriterion("deploy_env is not null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvEqualTo(String value) {
            addCriterion("deploy_env =", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotEqualTo(String value) {
            addCriterion("deploy_env <>", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThan(String value) {
            addCriterion("deploy_env >", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThanOrEqualTo(String value) {
            addCriterion("deploy_env >=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThan(String value) {
            addCriterion("deploy_env <", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThanOrEqualTo(String value) {
            addCriterion("deploy_env <=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLike(String value) {
            addCriterion("deploy_env like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotLike(String value) {
            addCriterion("deploy_env not like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIn(List<String> values) {
            addCriterion("deploy_env in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotIn(List<String> values) {
            addCriterion("deploy_env not in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvBetween(String value1, String value2) {
            addCriterion("deploy_env between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotBetween(String value1, String value2) {
            addCriterion("deploy_env not between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table report_accused_room_punish_stat_week
     *
     * @mbg.generated do_not_delete_during_merge Wed Aug 20 19:36:14 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table report_accused_room_punish_stat_week
     *
     * @mbg.generated Wed Aug 20 19:36:14 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}