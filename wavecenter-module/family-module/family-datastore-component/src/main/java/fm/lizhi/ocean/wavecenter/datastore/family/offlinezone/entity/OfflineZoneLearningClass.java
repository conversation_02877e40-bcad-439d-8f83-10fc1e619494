package fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 线下学习课堂配置
 *
 * @date 2025-08-18 07:36:57
 */
@Table(name = "`offline_zone_learning_class`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OfflineZoneLearningClass {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 环境：TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 标题
     */
    @Column(name= "`title`")
    private String title;

    /**
     * 类型：1-文档链接，2-视频文件
     */
    @Column(name= "`type`")
    private Integer type;

    /**
     * 文件URL，如果为相对路径则拼创作服务中心CDN域名
     */
    @Column(name= "`file_url`")
    private String fileUrl;

    /**
     * 文件封面，以斜杆开头的相对路径，类型为视频文件时必填
     */
    @Column(name= "`file_cover`")
    private String fileCover;

    /**
     * 排序权重，数字越大越前
     */
    @Column(name= "`weight`")
    private Integer weight;

    /**
     * 状态：0-下架，1-上架
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 标签
     */
    @Column(name= "`label`")
    private String label;

    /**
     * 标签颜色
     */
    @Column(name= "`label_color`")
    private String labelColor;

    /**
     * 是否删除：0-否，1-是
     */
    @Column(name= "`deleted`")
    private Boolean deleted;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", title=").append(title);
        sb.append(", type=").append(type);
        sb.append(", fileUrl=").append(fileUrl);
        sb.append(", fileCover=").append(fileCover);
        sb.append(", weight=").append(weight);
        sb.append(", status=").append(status);
        sb.append(", label=").append(label);
        sb.append(", labelColor=").append(labelColor);
        sb.append(", deleted=").append(deleted);
        sb.append(", operator=").append(operator);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}