package fm.lizhi.ocean.wavecenter.datastore.family.report.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 厅处理结果-违规记录映射表
 *
 * @date 2025-08-28 11:20:49
 */
@Table(name = "`report_room_violation_mapping`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReportRoomViolationMapping {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 主播举报申请记录id
     */
    @Column(name= "`player_report_apply_record_id`")
    private Long playerReportApplyRecordId;

    /**
     * 厅违规记录id
     */
    @Column(name= "`room_violation_record_id`")
    private Long roomViolationRecordId;

    /**
     * 冗余-被举报者
     */
    @Column(name= "`accused_user_id`")
    private Long accusedUserId;

    /**
     * 厅操作记录id
     */
    @Column(name= "`room_violation_operate_result_id`")
    private Long roomViolationOperateResultId;

    /**
     * 达成厅处罚条件:0否 1是; ps:如果本次导致厅被处罚，值为1 
     */
    @Column(name= "`reach_condition`")
    private Integer reachCondition;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", playerReportApplyRecordId=").append(playerReportApplyRecordId);
        sb.append(", roomViolationRecordId=").append(roomViolationRecordId);
        sb.append(", accusedUserId=").append(accusedUserId);
        sb.append(", roomViolationOperateResultId=").append(roomViolationOperateResultId);
        sb.append(", reachCondition=").append(reachCondition);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }
}