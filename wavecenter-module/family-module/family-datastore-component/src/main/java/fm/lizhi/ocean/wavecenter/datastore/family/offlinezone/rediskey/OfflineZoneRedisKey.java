package fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.rediskey;


import fm.lizhi.ocean.wavecenter.base.constants.IRedisKey;
import fm.lizhi.ocean.wavecenter.base.util.KeyUtils;

/**
 * 离线区域数据相关Redis Key
 * <AUTHOR>
 */
public enum OfflineZoneRedisKey implements IRedisKey {

    /**
     * 行政区划列表缓存
     * WAVECENTER_OFFLINE_ZONE_REGION_LIST_[appId]
     * value=行政区划列表JSON
     */
    REGION_LIST_X(true),

    /**
     * 地图数据缓存
     * WAVECENTER_OFFLINE_ZONE_MAP_DATA_[appId]_[familyId]
     * value=地图数据列表JSON
     */
    MAP_DATA_X(true);

    /**
     * 是否区分环境
     */
    private boolean afx;

    OfflineZoneRedisKey() {
    }

    OfflineZoneRedisKey(boolean afx) {
        this.afx = afx;
    }

    @Override
    public String getPrefix() {
        return KeyUtils.getPrefix("WAVECENTER_OFFLINE_ZONE", afx);
    }

    @Override
    public String getName() {
        return this.name();
    }
}