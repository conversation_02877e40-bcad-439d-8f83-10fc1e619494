package fm.lizhi.ocean.wavecenter.datastore.family.report.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportPlayerApplyRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_record
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public ReportPlayerApplyRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_record
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_record
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_record
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_record
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_record
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_record
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_record
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_record
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_record
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_player_apply_record
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return fm.lizhi.ocean.wavecenter.datastore.family.report.entity.ReportPlayerApplyRecord.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table report_player_apply_record
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andReportTypeIsNull() {
            addCriterion("report_type is null");
            return (Criteria) this;
        }

        public Criteria andReportTypeIsNotNull() {
            addCriterion("report_type is not null");
            return (Criteria) this;
        }

        public Criteria andReportTypeEqualTo(Integer value) {
            addCriterion("report_type =", value, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeNotEqualTo(Integer value) {
            addCriterion("report_type <>", value, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeGreaterThan(Integer value) {
            addCriterion("report_type >", value, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("report_type >=", value, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeLessThan(Integer value) {
            addCriterion("report_type <", value, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeLessThanOrEqualTo(Integer value) {
            addCriterion("report_type <=", value, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeIn(List<Integer> values) {
            addCriterion("report_type in", values, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeNotIn(List<Integer> values) {
            addCriterion("report_type not in", values, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeBetween(Integer value1, Integer value2) {
            addCriterion("report_type between", value1, value2, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("report_type not between", value1, value2, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportUserIdIsNull() {
            addCriterion("report_user_id is null");
            return (Criteria) this;
        }

        public Criteria andReportUserIdIsNotNull() {
            addCriterion("report_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportUserIdEqualTo(Long value) {
            addCriterion("report_user_id =", value, "reportUserId");
            return (Criteria) this;
        }

        public Criteria andReportUserIdNotEqualTo(Long value) {
            addCriterion("report_user_id <>", value, "reportUserId");
            return (Criteria) this;
        }

        public Criteria andReportUserIdGreaterThan(Long value) {
            addCriterion("report_user_id >", value, "reportUserId");
            return (Criteria) this;
        }

        public Criteria andReportUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_user_id >=", value, "reportUserId");
            return (Criteria) this;
        }

        public Criteria andReportUserIdLessThan(Long value) {
            addCriterion("report_user_id <", value, "reportUserId");
            return (Criteria) this;
        }

        public Criteria andReportUserIdLessThanOrEqualTo(Long value) {
            addCriterion("report_user_id <=", value, "reportUserId");
            return (Criteria) this;
        }

        public Criteria andReportUserIdIn(List<Long> values) {
            addCriterion("report_user_id in", values, "reportUserId");
            return (Criteria) this;
        }

        public Criteria andReportUserIdNotIn(List<Long> values) {
            addCriterion("report_user_id not in", values, "reportUserId");
            return (Criteria) this;
        }

        public Criteria andReportUserIdBetween(Long value1, Long value2) {
            addCriterion("report_user_id between", value1, value2, "reportUserId");
            return (Criteria) this;
        }

        public Criteria andReportUserIdNotBetween(Long value1, Long value2) {
            addCriterion("report_user_id not between", value1, value2, "reportUserId");
            return (Criteria) this;
        }

        public Criteria andReportRoomIdIsNull() {
            addCriterion("report_room_id is null");
            return (Criteria) this;
        }

        public Criteria andReportRoomIdIsNotNull() {
            addCriterion("report_room_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportRoomIdEqualTo(Long value) {
            addCriterion("report_room_id =", value, "reportRoomId");
            return (Criteria) this;
        }

        public Criteria andReportRoomIdNotEqualTo(Long value) {
            addCriterion("report_room_id <>", value, "reportRoomId");
            return (Criteria) this;
        }

        public Criteria andReportRoomIdGreaterThan(Long value) {
            addCriterion("report_room_id >", value, "reportRoomId");
            return (Criteria) this;
        }

        public Criteria andReportRoomIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_room_id >=", value, "reportRoomId");
            return (Criteria) this;
        }

        public Criteria andReportRoomIdLessThan(Long value) {
            addCriterion("report_room_id <", value, "reportRoomId");
            return (Criteria) this;
        }

        public Criteria andReportRoomIdLessThanOrEqualTo(Long value) {
            addCriterion("report_room_id <=", value, "reportRoomId");
            return (Criteria) this;
        }

        public Criteria andReportRoomIdIn(List<Long> values) {
            addCriterion("report_room_id in", values, "reportRoomId");
            return (Criteria) this;
        }

        public Criteria andReportRoomIdNotIn(List<Long> values) {
            addCriterion("report_room_id not in", values, "reportRoomId");
            return (Criteria) this;
        }

        public Criteria andReportRoomIdBetween(Long value1, Long value2) {
            addCriterion("report_room_id between", value1, value2, "reportRoomId");
            return (Criteria) this;
        }

        public Criteria andReportRoomIdNotBetween(Long value1, Long value2) {
            addCriterion("report_room_id not between", value1, value2, "reportRoomId");
            return (Criteria) this;
        }

        public Criteria andReportFamilyIdIsNull() {
            addCriterion("report_family_id is null");
            return (Criteria) this;
        }

        public Criteria andReportFamilyIdIsNotNull() {
            addCriterion("report_family_id is not null");
            return (Criteria) this;
        }

        public Criteria andReportFamilyIdEqualTo(Long value) {
            addCriterion("report_family_id =", value, "reportFamilyId");
            return (Criteria) this;
        }

        public Criteria andReportFamilyIdNotEqualTo(Long value) {
            addCriterion("report_family_id <>", value, "reportFamilyId");
            return (Criteria) this;
        }

        public Criteria andReportFamilyIdGreaterThan(Long value) {
            addCriterion("report_family_id >", value, "reportFamilyId");
            return (Criteria) this;
        }

        public Criteria andReportFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("report_family_id >=", value, "reportFamilyId");
            return (Criteria) this;
        }

        public Criteria andReportFamilyIdLessThan(Long value) {
            addCriterion("report_family_id <", value, "reportFamilyId");
            return (Criteria) this;
        }

        public Criteria andReportFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("report_family_id <=", value, "reportFamilyId");
            return (Criteria) this;
        }

        public Criteria andReportFamilyIdIn(List<Long> values) {
            addCriterion("report_family_id in", values, "reportFamilyId");
            return (Criteria) this;
        }

        public Criteria andReportFamilyIdNotIn(List<Long> values) {
            addCriterion("report_family_id not in", values, "reportFamilyId");
            return (Criteria) this;
        }

        public Criteria andReportFamilyIdBetween(Long value1, Long value2) {
            addCriterion("report_family_id between", value1, value2, "reportFamilyId");
            return (Criteria) this;
        }

        public Criteria andReportFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("report_family_id not between", value1, value2, "reportFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdIsNull() {
            addCriterion("accused_user_id is null");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdIsNotNull() {
            addCriterion("accused_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdEqualTo(Long value) {
            addCriterion("accused_user_id =", value, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdNotEqualTo(Long value) {
            addCriterion("accused_user_id <>", value, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdGreaterThan(Long value) {
            addCriterion("accused_user_id >", value, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("accused_user_id >=", value, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdLessThan(Long value) {
            addCriterion("accused_user_id <", value, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdLessThanOrEqualTo(Long value) {
            addCriterion("accused_user_id <=", value, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdIn(List<Long> values) {
            addCriterion("accused_user_id in", values, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdNotIn(List<Long> values) {
            addCriterion("accused_user_id not in", values, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdBetween(Long value1, Long value2) {
            addCriterion("accused_user_id between", value1, value2, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdNotBetween(Long value1, Long value2) {
            addCriterion("accused_user_id not between", value1, value2, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdIsNull() {
            addCriterion("accused_room_id is null");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdIsNotNull() {
            addCriterion("accused_room_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdEqualTo(Long value) {
            addCriterion("accused_room_id =", value, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdNotEqualTo(Long value) {
            addCriterion("accused_room_id <>", value, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdGreaterThan(Long value) {
            addCriterion("accused_room_id >", value, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdGreaterThanOrEqualTo(Long value) {
            addCriterion("accused_room_id >=", value, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdLessThan(Long value) {
            addCriterion("accused_room_id <", value, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdLessThanOrEqualTo(Long value) {
            addCriterion("accused_room_id <=", value, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdIn(List<Long> values) {
            addCriterion("accused_room_id in", values, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdNotIn(List<Long> values) {
            addCriterion("accused_room_id not in", values, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdBetween(Long value1, Long value2) {
            addCriterion("accused_room_id between", value1, value2, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdNotBetween(Long value1, Long value2) {
            addCriterion("accused_room_id not between", value1, value2, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdIsNull() {
            addCriterion("accused_family_id is null");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdIsNotNull() {
            addCriterion("accused_family_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdEqualTo(Long value) {
            addCriterion("accused_family_id =", value, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdNotEqualTo(Long value) {
            addCriterion("accused_family_id <>", value, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdGreaterThan(Long value) {
            addCriterion("accused_family_id >", value, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("accused_family_id >=", value, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdLessThan(Long value) {
            addCriterion("accused_family_id <", value, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("accused_family_id <=", value, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdIn(List<Long> values) {
            addCriterion("accused_family_id in", values, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdNotIn(List<Long> values) {
            addCriterion("accused_family_id not in", values, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdBetween(Long value1, Long value2) {
            addCriterion("accused_family_id between", value1, value2, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("accused_family_id not between", value1, value2, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalRoomIdIsNull() {
            addCriterion("accused_original_room_id is null");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalRoomIdIsNotNull() {
            addCriterion("accused_original_room_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalRoomIdEqualTo(Long value) {
            addCriterion("accused_original_room_id =", value, "accusedOriginalRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalRoomIdNotEqualTo(Long value) {
            addCriterion("accused_original_room_id <>", value, "accusedOriginalRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalRoomIdGreaterThan(Long value) {
            addCriterion("accused_original_room_id >", value, "accusedOriginalRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalRoomIdGreaterThanOrEqualTo(Long value) {
            addCriterion("accused_original_room_id >=", value, "accusedOriginalRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalRoomIdLessThan(Long value) {
            addCriterion("accused_original_room_id <", value, "accusedOriginalRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalRoomIdLessThanOrEqualTo(Long value) {
            addCriterion("accused_original_room_id <=", value, "accusedOriginalRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalRoomIdIn(List<Long> values) {
            addCriterion("accused_original_room_id in", values, "accusedOriginalRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalRoomIdNotIn(List<Long> values) {
            addCriterion("accused_original_room_id not in", values, "accusedOriginalRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalRoomIdBetween(Long value1, Long value2) {
            addCriterion("accused_original_room_id between", value1, value2, "accusedOriginalRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalRoomIdNotBetween(Long value1, Long value2) {
            addCriterion("accused_original_room_id not between", value1, value2, "accusedOriginalRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalFamilyIdIsNull() {
            addCriterion("accused_original_family_id is null");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalFamilyIdIsNotNull() {
            addCriterion("accused_original_family_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalFamilyIdEqualTo(Long value) {
            addCriterion("accused_original_family_id =", value, "accusedOriginalFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalFamilyIdNotEqualTo(Long value) {
            addCriterion("accused_original_family_id <>", value, "accusedOriginalFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalFamilyIdGreaterThan(Long value) {
            addCriterion("accused_original_family_id >", value, "accusedOriginalFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("accused_original_family_id >=", value, "accusedOriginalFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalFamilyIdLessThan(Long value) {
            addCriterion("accused_original_family_id <", value, "accusedOriginalFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("accused_original_family_id <=", value, "accusedOriginalFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalFamilyIdIn(List<Long> values) {
            addCriterion("accused_original_family_id in", values, "accusedOriginalFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalFamilyIdNotIn(List<Long> values) {
            addCriterion("accused_original_family_id not in", values, "accusedOriginalFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalFamilyIdBetween(Long value1, Long value2) {
            addCriterion("accused_original_family_id between", value1, value2, "accusedOriginalFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedOriginalFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("accused_original_family_id not between", value1, value2, "accusedOriginalFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignStatusIsNull() {
            addCriterion("accused_user_sign_status is null");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignStatusIsNotNull() {
            addCriterion("accused_user_sign_status is not null");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignStatusEqualTo(Integer value) {
            addCriterion("accused_user_sign_status =", value, "accusedUserSignStatus");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignStatusNotEqualTo(Integer value) {
            addCriterion("accused_user_sign_status <>", value, "accusedUserSignStatus");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignStatusGreaterThan(Integer value) {
            addCriterion("accused_user_sign_status >", value, "accusedUserSignStatus");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("accused_user_sign_status >=", value, "accusedUserSignStatus");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignStatusLessThan(Integer value) {
            addCriterion("accused_user_sign_status <", value, "accusedUserSignStatus");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignStatusLessThanOrEqualTo(Integer value) {
            addCriterion("accused_user_sign_status <=", value, "accusedUserSignStatus");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignStatusIn(List<Integer> values) {
            addCriterion("accused_user_sign_status in", values, "accusedUserSignStatus");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignStatusNotIn(List<Integer> values) {
            addCriterion("accused_user_sign_status not in", values, "accusedUserSignStatus");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignStatusBetween(Integer value1, Integer value2) {
            addCriterion("accused_user_sign_status between", value1, value2, "accusedUserSignStatus");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("accused_user_sign_status not between", value1, value2, "accusedUserSignStatus");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignTimeIsNull() {
            addCriterion("accused_user_sign_time is null");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignTimeIsNotNull() {
            addCriterion("accused_user_sign_time is not null");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignTimeEqualTo(Date value) {
            addCriterion("accused_user_sign_time =", value, "accusedUserSignTime");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignTimeNotEqualTo(Date value) {
            addCriterion("accused_user_sign_time <>", value, "accusedUserSignTime");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignTimeGreaterThan(Date value) {
            addCriterion("accused_user_sign_time >", value, "accusedUserSignTime");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("accused_user_sign_time >=", value, "accusedUserSignTime");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignTimeLessThan(Date value) {
            addCriterion("accused_user_sign_time <", value, "accusedUserSignTime");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignTimeLessThanOrEqualTo(Date value) {
            addCriterion("accused_user_sign_time <=", value, "accusedUserSignTime");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignTimeIn(List<Date> values) {
            addCriterion("accused_user_sign_time in", values, "accusedUserSignTime");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignTimeNotIn(List<Date> values) {
            addCriterion("accused_user_sign_time not in", values, "accusedUserSignTime");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignTimeBetween(Date value1, Date value2) {
            addCriterion("accused_user_sign_time between", value1, value2, "accusedUserSignTime");
            return (Criteria) this;
        }

        public Criteria andAccusedUserSignTimeNotBetween(Date value1, Date value2) {
            addCriterion("accused_user_sign_time not between", value1, value2, "accusedUserSignTime");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserIdIsNull() {
            addCriterion("accused_main_user_id is null");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserIdIsNotNull() {
            addCriterion("accused_main_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserIdEqualTo(Long value) {
            addCriterion("accused_main_user_id =", value, "accusedMainUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserIdNotEqualTo(Long value) {
            addCriterion("accused_main_user_id <>", value, "accusedMainUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserIdGreaterThan(Long value) {
            addCriterion("accused_main_user_id >", value, "accusedMainUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("accused_main_user_id >=", value, "accusedMainUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserIdLessThan(Long value) {
            addCriterion("accused_main_user_id <", value, "accusedMainUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserIdLessThanOrEqualTo(Long value) {
            addCriterion("accused_main_user_id <=", value, "accusedMainUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserIdIn(List<Long> values) {
            addCriterion("accused_main_user_id in", values, "accusedMainUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserIdNotIn(List<Long> values) {
            addCriterion("accused_main_user_id not in", values, "accusedMainUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserIdBetween(Long value1, Long value2) {
            addCriterion("accused_main_user_id between", value1, value2, "accusedMainUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserIdNotBetween(Long value1, Long value2) {
            addCriterion("accused_main_user_id not between", value1, value2, "accusedMainUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainRoomIdIsNull() {
            addCriterion("accused_main_room_id is null");
            return (Criteria) this;
        }

        public Criteria andAccusedMainRoomIdIsNotNull() {
            addCriterion("accused_main_room_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccusedMainRoomIdEqualTo(Long value) {
            addCriterion("accused_main_room_id =", value, "accusedMainRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainRoomIdNotEqualTo(Long value) {
            addCriterion("accused_main_room_id <>", value, "accusedMainRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainRoomIdGreaterThan(Long value) {
            addCriterion("accused_main_room_id >", value, "accusedMainRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainRoomIdGreaterThanOrEqualTo(Long value) {
            addCriterion("accused_main_room_id >=", value, "accusedMainRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainRoomIdLessThan(Long value) {
            addCriterion("accused_main_room_id <", value, "accusedMainRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainRoomIdLessThanOrEqualTo(Long value) {
            addCriterion("accused_main_room_id <=", value, "accusedMainRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainRoomIdIn(List<Long> values) {
            addCriterion("accused_main_room_id in", values, "accusedMainRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainRoomIdNotIn(List<Long> values) {
            addCriterion("accused_main_room_id not in", values, "accusedMainRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainRoomIdBetween(Long value1, Long value2) {
            addCriterion("accused_main_room_id between", value1, value2, "accusedMainRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainRoomIdNotBetween(Long value1, Long value2) {
            addCriterion("accused_main_room_id not between", value1, value2, "accusedMainRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainFamilyIdIsNull() {
            addCriterion("accused_main_family_id is null");
            return (Criteria) this;
        }

        public Criteria andAccusedMainFamilyIdIsNotNull() {
            addCriterion("accused_main_family_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccusedMainFamilyIdEqualTo(Long value) {
            addCriterion("accused_main_family_id =", value, "accusedMainFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainFamilyIdNotEqualTo(Long value) {
            addCriterion("accused_main_family_id <>", value, "accusedMainFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainFamilyIdGreaterThan(Long value) {
            addCriterion("accused_main_family_id >", value, "accusedMainFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("accused_main_family_id >=", value, "accusedMainFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainFamilyIdLessThan(Long value) {
            addCriterion("accused_main_family_id <", value, "accusedMainFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("accused_main_family_id <=", value, "accusedMainFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainFamilyIdIn(List<Long> values) {
            addCriterion("accused_main_family_id in", values, "accusedMainFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainFamilyIdNotIn(List<Long> values) {
            addCriterion("accused_main_family_id not in", values, "accusedMainFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainFamilyIdBetween(Long value1, Long value2) {
            addCriterion("accused_main_family_id between", value1, value2, "accusedMainFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("accused_main_family_id not between", value1, value2, "accusedMainFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignStatusIsNull() {
            addCriterion("accused_main_user_sign_status is null");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignStatusIsNotNull() {
            addCriterion("accused_main_user_sign_status is not null");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignStatusEqualTo(Integer value) {
            addCriterion("accused_main_user_sign_status =", value, "accusedMainUserSignStatus");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignStatusNotEqualTo(Integer value) {
            addCriterion("accused_main_user_sign_status <>", value, "accusedMainUserSignStatus");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignStatusGreaterThan(Integer value) {
            addCriterion("accused_main_user_sign_status >", value, "accusedMainUserSignStatus");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("accused_main_user_sign_status >=", value, "accusedMainUserSignStatus");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignStatusLessThan(Integer value) {
            addCriterion("accused_main_user_sign_status <", value, "accusedMainUserSignStatus");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignStatusLessThanOrEqualTo(Integer value) {
            addCriterion("accused_main_user_sign_status <=", value, "accusedMainUserSignStatus");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignStatusIn(List<Integer> values) {
            addCriterion("accused_main_user_sign_status in", values, "accusedMainUserSignStatus");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignStatusNotIn(List<Integer> values) {
            addCriterion("accused_main_user_sign_status not in", values, "accusedMainUserSignStatus");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignStatusBetween(Integer value1, Integer value2) {
            addCriterion("accused_main_user_sign_status between", value1, value2, "accusedMainUserSignStatus");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("accused_main_user_sign_status not between", value1, value2, "accusedMainUserSignStatus");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignTimeIsNull() {
            addCriterion("accused_main_user_sign_time is null");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignTimeIsNotNull() {
            addCriterion("accused_main_user_sign_time is not null");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignTimeEqualTo(Date value) {
            addCriterion("accused_main_user_sign_time =", value, "accusedMainUserSignTime");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignTimeNotEqualTo(Date value) {
            addCriterion("accused_main_user_sign_time <>", value, "accusedMainUserSignTime");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignTimeGreaterThan(Date value) {
            addCriterion("accused_main_user_sign_time >", value, "accusedMainUserSignTime");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("accused_main_user_sign_time >=", value, "accusedMainUserSignTime");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignTimeLessThan(Date value) {
            addCriterion("accused_main_user_sign_time <", value, "accusedMainUserSignTime");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignTimeLessThanOrEqualTo(Date value) {
            addCriterion("accused_main_user_sign_time <=", value, "accusedMainUserSignTime");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignTimeIn(List<Date> values) {
            addCriterion("accused_main_user_sign_time in", values, "accusedMainUserSignTime");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignTimeNotIn(List<Date> values) {
            addCriterion("accused_main_user_sign_time not in", values, "accusedMainUserSignTime");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignTimeBetween(Date value1, Date value2) {
            addCriterion("accused_main_user_sign_time between", value1, value2, "accusedMainUserSignTime");
            return (Criteria) this;
        }

        public Criteria andAccusedMainUserSignTimeNotBetween(Date value1, Date value2) {
            addCriterion("accused_main_user_sign_time not between", value1, value2, "accusedMainUserSignTime");
            return (Criteria) this;
        }

        public Criteria andReportResultStatusIsNull() {
            addCriterion("report_result_status is null");
            return (Criteria) this;
        }

        public Criteria andReportResultStatusIsNotNull() {
            addCriterion("report_result_status is not null");
            return (Criteria) this;
        }

        public Criteria andReportResultStatusEqualTo(Integer value) {
            addCriterion("report_result_status =", value, "reportResultStatus");
            return (Criteria) this;
        }

        public Criteria andReportResultStatusNotEqualTo(Integer value) {
            addCriterion("report_result_status <>", value, "reportResultStatus");
            return (Criteria) this;
        }

        public Criteria andReportResultStatusGreaterThan(Integer value) {
            addCriterion("report_result_status >", value, "reportResultStatus");
            return (Criteria) this;
        }

        public Criteria andReportResultStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("report_result_status >=", value, "reportResultStatus");
            return (Criteria) this;
        }

        public Criteria andReportResultStatusLessThan(Integer value) {
            addCriterion("report_result_status <", value, "reportResultStatus");
            return (Criteria) this;
        }

        public Criteria andReportResultStatusLessThanOrEqualTo(Integer value) {
            addCriterion("report_result_status <=", value, "reportResultStatus");
            return (Criteria) this;
        }

        public Criteria andReportResultStatusIn(List<Integer> values) {
            addCriterion("report_result_status in", values, "reportResultStatus");
            return (Criteria) this;
        }

        public Criteria andReportResultStatusNotIn(List<Integer> values) {
            addCriterion("report_result_status not in", values, "reportResultStatus");
            return (Criteria) this;
        }

        public Criteria andReportResultStatusBetween(Integer value1, Integer value2) {
            addCriterion("report_result_status between", value1, value2, "reportResultStatus");
            return (Criteria) this;
        }

        public Criteria andReportResultStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("report_result_status not between", value1, value2, "reportResultStatus");
            return (Criteria) this;
        }

        public Criteria andReportResultReasonIsNull() {
            addCriterion("report_result_reason is null");
            return (Criteria) this;
        }

        public Criteria andReportResultReasonIsNotNull() {
            addCriterion("report_result_reason is not null");
            return (Criteria) this;
        }

        public Criteria andReportResultReasonEqualTo(String value) {
            addCriterion("report_result_reason =", value, "reportResultReason");
            return (Criteria) this;
        }

        public Criteria andReportResultReasonNotEqualTo(String value) {
            addCriterion("report_result_reason <>", value, "reportResultReason");
            return (Criteria) this;
        }

        public Criteria andReportResultReasonGreaterThan(String value) {
            addCriterion("report_result_reason >", value, "reportResultReason");
            return (Criteria) this;
        }

        public Criteria andReportResultReasonGreaterThanOrEqualTo(String value) {
            addCriterion("report_result_reason >=", value, "reportResultReason");
            return (Criteria) this;
        }

        public Criteria andReportResultReasonLessThan(String value) {
            addCriterion("report_result_reason <", value, "reportResultReason");
            return (Criteria) this;
        }

        public Criteria andReportResultReasonLessThanOrEqualTo(String value) {
            addCriterion("report_result_reason <=", value, "reportResultReason");
            return (Criteria) this;
        }

        public Criteria andReportResultReasonLike(String value) {
            addCriterion("report_result_reason like", value, "reportResultReason");
            return (Criteria) this;
        }

        public Criteria andReportResultReasonNotLike(String value) {
            addCriterion("report_result_reason not like", value, "reportResultReason");
            return (Criteria) this;
        }

        public Criteria andReportResultReasonIn(List<String> values) {
            addCriterion("report_result_reason in", values, "reportResultReason");
            return (Criteria) this;
        }

        public Criteria andReportResultReasonNotIn(List<String> values) {
            addCriterion("report_result_reason not in", values, "reportResultReason");
            return (Criteria) this;
        }

        public Criteria andReportResultReasonBetween(String value1, String value2) {
            addCriterion("report_result_reason between", value1, value2, "reportResultReason");
            return (Criteria) this;
        }

        public Criteria andReportResultReasonNotBetween(String value1, String value2) {
            addCriterion("report_result_reason not between", value1, value2, "reportResultReason");
            return (Criteria) this;
        }

        public Criteria andAppDeviceIdIsNull() {
            addCriterion("app_device_id is null");
            return (Criteria) this;
        }

        public Criteria andAppDeviceIdIsNotNull() {
            addCriterion("app_device_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppDeviceIdEqualTo(String value) {
            addCriterion("app_device_id =", value, "appDeviceId");
            return (Criteria) this;
        }

        public Criteria andAppDeviceIdNotEqualTo(String value) {
            addCriterion("app_device_id <>", value, "appDeviceId");
            return (Criteria) this;
        }

        public Criteria andAppDeviceIdGreaterThan(String value) {
            addCriterion("app_device_id >", value, "appDeviceId");
            return (Criteria) this;
        }

        public Criteria andAppDeviceIdGreaterThanOrEqualTo(String value) {
            addCriterion("app_device_id >=", value, "appDeviceId");
            return (Criteria) this;
        }

        public Criteria andAppDeviceIdLessThan(String value) {
            addCriterion("app_device_id <", value, "appDeviceId");
            return (Criteria) this;
        }

        public Criteria andAppDeviceIdLessThanOrEqualTo(String value) {
            addCriterion("app_device_id <=", value, "appDeviceId");
            return (Criteria) this;
        }

        public Criteria andAppDeviceIdLike(String value) {
            addCriterion("app_device_id like", value, "appDeviceId");
            return (Criteria) this;
        }

        public Criteria andAppDeviceIdNotLike(String value) {
            addCriterion("app_device_id not like", value, "appDeviceId");
            return (Criteria) this;
        }

        public Criteria andAppDeviceIdIn(List<String> values) {
            addCriterion("app_device_id in", values, "appDeviceId");
            return (Criteria) this;
        }

        public Criteria andAppDeviceIdNotIn(List<String> values) {
            addCriterion("app_device_id not in", values, "appDeviceId");
            return (Criteria) this;
        }

        public Criteria andAppDeviceIdBetween(String value1, String value2) {
            addCriterion("app_device_id between", value1, value2, "appDeviceId");
            return (Criteria) this;
        }

        public Criteria andAppDeviceIdNotBetween(String value1, String value2) {
            addCriterion("app_device_id not between", value1, value2, "appDeviceId");
            return (Criteria) this;
        }

        public Criteria andPcDeviceIdIsNull() {
            addCriterion("pc_device_id is null");
            return (Criteria) this;
        }

        public Criteria andPcDeviceIdIsNotNull() {
            addCriterion("pc_device_id is not null");
            return (Criteria) this;
        }

        public Criteria andPcDeviceIdEqualTo(String value) {
            addCriterion("pc_device_id =", value, "pcDeviceId");
            return (Criteria) this;
        }

        public Criteria andPcDeviceIdNotEqualTo(String value) {
            addCriterion("pc_device_id <>", value, "pcDeviceId");
            return (Criteria) this;
        }

        public Criteria andPcDeviceIdGreaterThan(String value) {
            addCriterion("pc_device_id >", value, "pcDeviceId");
            return (Criteria) this;
        }

        public Criteria andPcDeviceIdGreaterThanOrEqualTo(String value) {
            addCriterion("pc_device_id >=", value, "pcDeviceId");
            return (Criteria) this;
        }

        public Criteria andPcDeviceIdLessThan(String value) {
            addCriterion("pc_device_id <", value, "pcDeviceId");
            return (Criteria) this;
        }

        public Criteria andPcDeviceIdLessThanOrEqualTo(String value) {
            addCriterion("pc_device_id <=", value, "pcDeviceId");
            return (Criteria) this;
        }

        public Criteria andPcDeviceIdLike(String value) {
            addCriterion("pc_device_id like", value, "pcDeviceId");
            return (Criteria) this;
        }

        public Criteria andPcDeviceIdNotLike(String value) {
            addCriterion("pc_device_id not like", value, "pcDeviceId");
            return (Criteria) this;
        }

        public Criteria andPcDeviceIdIn(List<String> values) {
            addCriterion("pc_device_id in", values, "pcDeviceId");
            return (Criteria) this;
        }

        public Criteria andPcDeviceIdNotIn(List<String> values) {
            addCriterion("pc_device_id not in", values, "pcDeviceId");
            return (Criteria) this;
        }

        public Criteria andPcDeviceIdBetween(String value1, String value2) {
            addCriterion("pc_device_id between", value1, value2, "pcDeviceId");
            return (Criteria) this;
        }

        public Criteria andPcDeviceIdNotBetween(String value1, String value2) {
            addCriterion("pc_device_id not between", value1, value2, "pcDeviceId");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNull() {
            addCriterion("deploy_env is null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNotNull() {
            addCriterion("deploy_env is not null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvEqualTo(String value) {
            addCriterion("deploy_env =", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotEqualTo(String value) {
            addCriterion("deploy_env <>", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThan(String value) {
            addCriterion("deploy_env >", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThanOrEqualTo(String value) {
            addCriterion("deploy_env >=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThan(String value) {
            addCriterion("deploy_env <", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThanOrEqualTo(String value) {
            addCriterion("deploy_env <=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLike(String value) {
            addCriterion("deploy_env like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotLike(String value) {
            addCriterion("deploy_env not like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIn(List<String> values) {
            addCriterion("deploy_env in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotIn(List<String> values) {
            addCriterion("deploy_env not in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvBetween(String value1, String value2) {
            addCriterion("deploy_env between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotBetween(String value1, String value2) {
            addCriterion("deploy_env not between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNull() {
            addCriterion("operator is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNotNull() {
            addCriterion("operator is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorEqualTo(String value) {
            addCriterion("operator =", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotEqualTo(String value) {
            addCriterion("operator <>", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThan(String value) {
            addCriterion("operator >", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThanOrEqualTo(String value) {
            addCriterion("operator >=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThan(String value) {
            addCriterion("operator <", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThanOrEqualTo(String value) {
            addCriterion("operator <=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLike(String value) {
            addCriterion("operator like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotLike(String value) {
            addCriterion("operator not like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorIn(List<String> values) {
            addCriterion("operator in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotIn(List<String> values) {
            addCriterion("operator not in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorBetween(String value1, String value2) {
            addCriterion("operator between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotBetween(String value1, String value2) {
            addCriterion("operator not between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("source is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("source is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(String value) {
            addCriterion("source =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(String value) {
            addCriterion("source <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(String value) {
            addCriterion("source >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(String value) {
            addCriterion("source >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(String value) {
            addCriterion("source <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(String value) {
            addCriterion("source <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLike(String value) {
            addCriterion("source like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotLike(String value) {
            addCriterion("source not like", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<String> values) {
            addCriterion("source in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<String> values) {
            addCriterion("source not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(String value1, String value2) {
            addCriterion("source between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(String value1, String value2) {
            addCriterion("source not between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table report_player_apply_record
     *
     * @mbg.generated do_not_delete_during_merge Thu Aug 21 15:29:11 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table report_player_apply_record
     *
     * @mbg.generated Thu Aug 21 15:29:11 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}