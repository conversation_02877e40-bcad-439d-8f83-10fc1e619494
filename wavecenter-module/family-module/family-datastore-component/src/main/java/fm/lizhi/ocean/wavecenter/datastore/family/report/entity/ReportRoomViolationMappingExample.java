package fm.lizhi.ocean.wavecenter.datastore.family.report.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportRoomViolationMappingExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_mapping
     *
     * @mbg.generated Thu Aug 28 11:20:49 CST 2025
     */
    public ReportRoomViolationMappingExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_mapping
     *
     * @mbg.generated Thu Aug 28 11:20:49 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_mapping
     *
     * @mbg.generated Thu Aug 28 11:20:49 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_mapping
     *
     * @mbg.generated Thu Aug 28 11:20:49 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_mapping
     *
     * @mbg.generated Thu Aug 28 11:20:49 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_mapping
     *
     * @mbg.generated Thu Aug 28 11:20:49 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_mapping
     *
     * @mbg.generated Thu Aug 28 11:20:49 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_mapping
     *
     * @mbg.generated Thu Aug 28 11:20:49 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_mapping
     *
     * @mbg.generated Thu Aug 28 11:20:49 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_mapping
     *
     * @mbg.generated Thu Aug 28 11:20:49 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_mapping
     *
     * @mbg.generated Thu Aug 28 11:20:49 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return fm.lizhi.ocean.wavecenter.datastore.family.report.entity.ReportRoomViolationMapping.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table report_room_violation_mapping
     *
     * @mbg.generated Thu Aug 28 11:20:49 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdIsNull() {
            addCriterion("player_report_apply_record_id is null");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdIsNotNull() {
            addCriterion("player_report_apply_record_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdEqualTo(Long value) {
            addCriterion("player_report_apply_record_id =", value, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdNotEqualTo(Long value) {
            addCriterion("player_report_apply_record_id <>", value, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdGreaterThan(Long value) {
            addCriterion("player_report_apply_record_id >", value, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdGreaterThanOrEqualTo(Long value) {
            addCriterion("player_report_apply_record_id >=", value, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdLessThan(Long value) {
            addCriterion("player_report_apply_record_id <", value, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdLessThanOrEqualTo(Long value) {
            addCriterion("player_report_apply_record_id <=", value, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdIn(List<Long> values) {
            addCriterion("player_report_apply_record_id in", values, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdNotIn(List<Long> values) {
            addCriterion("player_report_apply_record_id not in", values, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdBetween(Long value1, Long value2) {
            addCriterion("player_report_apply_record_id between", value1, value2, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdNotBetween(Long value1, Long value2) {
            addCriterion("player_report_apply_record_id not between", value1, value2, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andRoomViolationRecordIdIsNull() {
            addCriterion("room_violation_record_id is null");
            return (Criteria) this;
        }

        public Criteria andRoomViolationRecordIdIsNotNull() {
            addCriterion("room_violation_record_id is not null");
            return (Criteria) this;
        }

        public Criteria andRoomViolationRecordIdEqualTo(Long value) {
            addCriterion("room_violation_record_id =", value, "roomViolationRecordId");
            return (Criteria) this;
        }

        public Criteria andRoomViolationRecordIdNotEqualTo(Long value) {
            addCriterion("room_violation_record_id <>", value, "roomViolationRecordId");
            return (Criteria) this;
        }

        public Criteria andRoomViolationRecordIdGreaterThan(Long value) {
            addCriterion("room_violation_record_id >", value, "roomViolationRecordId");
            return (Criteria) this;
        }

        public Criteria andRoomViolationRecordIdGreaterThanOrEqualTo(Long value) {
            addCriterion("room_violation_record_id >=", value, "roomViolationRecordId");
            return (Criteria) this;
        }

        public Criteria andRoomViolationRecordIdLessThan(Long value) {
            addCriterion("room_violation_record_id <", value, "roomViolationRecordId");
            return (Criteria) this;
        }

        public Criteria andRoomViolationRecordIdLessThanOrEqualTo(Long value) {
            addCriterion("room_violation_record_id <=", value, "roomViolationRecordId");
            return (Criteria) this;
        }

        public Criteria andRoomViolationRecordIdIn(List<Long> values) {
            addCriterion("room_violation_record_id in", values, "roomViolationRecordId");
            return (Criteria) this;
        }

        public Criteria andRoomViolationRecordIdNotIn(List<Long> values) {
            addCriterion("room_violation_record_id not in", values, "roomViolationRecordId");
            return (Criteria) this;
        }

        public Criteria andRoomViolationRecordIdBetween(Long value1, Long value2) {
            addCriterion("room_violation_record_id between", value1, value2, "roomViolationRecordId");
            return (Criteria) this;
        }

        public Criteria andRoomViolationRecordIdNotBetween(Long value1, Long value2) {
            addCriterion("room_violation_record_id not between", value1, value2, "roomViolationRecordId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdIsNull() {
            addCriterion("accused_user_id is null");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdIsNotNull() {
            addCriterion("accused_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdEqualTo(Long value) {
            addCriterion("accused_user_id =", value, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdNotEqualTo(Long value) {
            addCriterion("accused_user_id <>", value, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdGreaterThan(Long value) {
            addCriterion("accused_user_id >", value, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("accused_user_id >=", value, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdLessThan(Long value) {
            addCriterion("accused_user_id <", value, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdLessThanOrEqualTo(Long value) {
            addCriterion("accused_user_id <=", value, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdIn(List<Long> values) {
            addCriterion("accused_user_id in", values, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdNotIn(List<Long> values) {
            addCriterion("accused_user_id not in", values, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdBetween(Long value1, Long value2) {
            addCriterion("accused_user_id between", value1, value2, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdNotBetween(Long value1, Long value2) {
            addCriterion("accused_user_id not between", value1, value2, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andRoomViolationOperateResultIdIsNull() {
            addCriterion("room_violation_operate_result_id is null");
            return (Criteria) this;
        }

        public Criteria andRoomViolationOperateResultIdIsNotNull() {
            addCriterion("room_violation_operate_result_id is not null");
            return (Criteria) this;
        }

        public Criteria andRoomViolationOperateResultIdEqualTo(Long value) {
            addCriterion("room_violation_operate_result_id =", value, "roomViolationOperateResultId");
            return (Criteria) this;
        }

        public Criteria andRoomViolationOperateResultIdNotEqualTo(Long value) {
            addCriterion("room_violation_operate_result_id <>", value, "roomViolationOperateResultId");
            return (Criteria) this;
        }

        public Criteria andRoomViolationOperateResultIdGreaterThan(Long value) {
            addCriterion("room_violation_operate_result_id >", value, "roomViolationOperateResultId");
            return (Criteria) this;
        }

        public Criteria andRoomViolationOperateResultIdGreaterThanOrEqualTo(Long value) {
            addCriterion("room_violation_operate_result_id >=", value, "roomViolationOperateResultId");
            return (Criteria) this;
        }

        public Criteria andRoomViolationOperateResultIdLessThan(Long value) {
            addCriterion("room_violation_operate_result_id <", value, "roomViolationOperateResultId");
            return (Criteria) this;
        }

        public Criteria andRoomViolationOperateResultIdLessThanOrEqualTo(Long value) {
            addCriterion("room_violation_operate_result_id <=", value, "roomViolationOperateResultId");
            return (Criteria) this;
        }

        public Criteria andRoomViolationOperateResultIdIn(List<Long> values) {
            addCriterion("room_violation_operate_result_id in", values, "roomViolationOperateResultId");
            return (Criteria) this;
        }

        public Criteria andRoomViolationOperateResultIdNotIn(List<Long> values) {
            addCriterion("room_violation_operate_result_id not in", values, "roomViolationOperateResultId");
            return (Criteria) this;
        }

        public Criteria andRoomViolationOperateResultIdBetween(Long value1, Long value2) {
            addCriterion("room_violation_operate_result_id between", value1, value2, "roomViolationOperateResultId");
            return (Criteria) this;
        }

        public Criteria andRoomViolationOperateResultIdNotBetween(Long value1, Long value2) {
            addCriterion("room_violation_operate_result_id not between", value1, value2, "roomViolationOperateResultId");
            return (Criteria) this;
        }

        public Criteria andReachConditionIsNull() {
            addCriterion("reach_condition is null");
            return (Criteria) this;
        }

        public Criteria andReachConditionIsNotNull() {
            addCriterion("reach_condition is not null");
            return (Criteria) this;
        }

        public Criteria andReachConditionEqualTo(Integer value) {
            addCriterion("reach_condition =", value, "reachCondition");
            return (Criteria) this;
        }

        public Criteria andReachConditionNotEqualTo(Integer value) {
            addCriterion("reach_condition <>", value, "reachCondition");
            return (Criteria) this;
        }

        public Criteria andReachConditionGreaterThan(Integer value) {
            addCriterion("reach_condition >", value, "reachCondition");
            return (Criteria) this;
        }

        public Criteria andReachConditionGreaterThanOrEqualTo(Integer value) {
            addCriterion("reach_condition >=", value, "reachCondition");
            return (Criteria) this;
        }

        public Criteria andReachConditionLessThan(Integer value) {
            addCriterion("reach_condition <", value, "reachCondition");
            return (Criteria) this;
        }

        public Criteria andReachConditionLessThanOrEqualTo(Integer value) {
            addCriterion("reach_condition <=", value, "reachCondition");
            return (Criteria) this;
        }

        public Criteria andReachConditionIn(List<Integer> values) {
            addCriterion("reach_condition in", values, "reachCondition");
            return (Criteria) this;
        }

        public Criteria andReachConditionNotIn(List<Integer> values) {
            addCriterion("reach_condition not in", values, "reachCondition");
            return (Criteria) this;
        }

        public Criteria andReachConditionBetween(Integer value1, Integer value2) {
            addCriterion("reach_condition between", value1, value2, "reachCondition");
            return (Criteria) this;
        }

        public Criteria andReachConditionNotBetween(Integer value1, Integer value2) {
            addCriterion("reach_condition not between", value1, value2, "reachCondition");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table report_room_violation_mapping
     *
     * @mbg.generated do_not_delete_during_merge Thu Aug 28 11:20:49 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table report_room_violation_mapping
     *
     * @mbg.generated Thu Aug 28 11:20:49 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}