package fm.lizhi.ocean.wavecenter.datastore.family.report.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 挖槽厅处罚周统计表
 *
 * @date 2025-08-20 07:36:14
 */
@Table(name = "`report_accused_room_punish_stat_week`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReportAccusedRoomPunishStatWeek {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 日期 格式 YYYY-MM-DD
     */
    @Column(name= "`start_week_date`")
    private Date startWeekDate;

    /**
     * 日期 格式 YYYY-MM-DD
     */
    @Column(name= "`end_week_date`")
    private Date endWeekDate;

    /**
     * 跳槽主播id集合，多个逗号分隔
     */
    @Column(name= "`accused_user_ids`")
    private String accusedUserIds;

    /**
     * 挖槽厅ID
     */
    @Column(name= "`accused_room_id`")
    private Long accusedRoomId;

    /**
     * 挖槽厅公会ID
     */
    @Column(name= "`accused_family_id`")
    private Long accusedFamilyId;

    /**
     * 排名
     */
    @Column(name= "`rank`")
    private Integer rank;

    /**
     * 处罚次数
     */
    @Column(name= "`count`")
    private Integer count;

    /**
     * 处罚结果
     */
    @Column(name= "`punish_reason`")
    private String punishReason;

    /**
     * 环境：TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", startWeekDate=").append(startWeekDate);
        sb.append(", endWeekDate=").append(endWeekDate);
        sb.append(", accusedUserIds=").append(accusedUserIds);
        sb.append(", accusedRoomId=").append(accusedRoomId);
        sb.append(", accusedFamilyId=").append(accusedFamilyId);
        sb.append(", rank=").append(rank);
        sb.append(", count=").append(count);
        sb.append(", punishReason=").append(punishReason);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}