package fm.lizhi.ocean.wavecenter.datastore.family.report.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 厅处理结果
 *
 * @date 2025-08-21 03:29:11
 */
@Table(name = "`report_room_violation_operate_result`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReportRoomViolationOperateResult {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 被举报厅ID-挖槽厅
     */
    @Column(name= "`accused_room_id`")
    private Long accusedRoomId;

    /**
     * 被举报的厅所属公会-挖槽厅公会
     */
    @Column(name= "`accused_family_id`")
    private Long accusedFamilyId;

    /**
     * 周期内处罚次数
     */
    @Column(name= "`operate_seq`")
    private Integer operateSeq;

    /**
     * 操作时长;eg:厅封禁小时
     */
    @Column(name= "`operate_duration`")
    private Integer operateDuration;

    /**
     * 操作类型(1：账号，2：厅开播权限 3:设备)
     */
    @Column(name= "`operate_obj_type`")
    private Integer operateObjType;

    /**
     * 操作类型(1：封禁，2：解禁)
     */
    @Column(name= "`operate_type`")
    private Integer operateType;

    /**
     * 是否手动 0-否 1-是
     */
    @Column(name= "`manual`")
    private Boolean manual;

    /**
     * 状态，0待执行 1：成功，2：失败
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 操作备注
     */
    @Column(name= "`operate_remark`")
    private String operateRemark;

    /**
     * 此次处罚的违规人数
     */
    @Column(name= "`violation_user_sum`")
    private Integer violationUserSum;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 环境：TEST/PRE/PRO',
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", accusedRoomId=").append(accusedRoomId);
        sb.append(", accusedFamilyId=").append(accusedFamilyId);
        sb.append(", operateSeq=").append(operateSeq);
        sb.append(", operateDuration=").append(operateDuration);
        sb.append(", operateObjType=").append(operateObjType);
        sb.append(", operateType=").append(operateType);
        sb.append(", manual=").append(manual);
        sb.append(", status=").append(status);
        sb.append(", operateRemark=").append(operateRemark);
        sb.append(", violationUserSum=").append(violationUserSum);
        sb.append(", operator=").append(operator);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}