package fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 等级与权益关联关系
 *
 * @date 2025-09-04 05:24:08
 */
@Table(name = "`offline_zone_level_right_relation`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OfflineZoneLevelRightRelation {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 等级ID
     */
    @Column(name= "`level_id`")
    private Long levelId;

    /**
     * 权益ID
     */
    @Column(name= "`right_id`")
    private Long rightId;

    /**
     * 排序下标，从0开始，越小越靠前
     */
    @Column(name= "`index`")
    private Integer index;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 是否已解锁：0-否，1-是
     */
    @Column(name= "`unlocked`")
    private Boolean unlocked;

    /**
     * 解锁等级ID，即达到该等级可解锁权益
     */
    @Column(name= "`unlocked_level_id`")
    private Long unlockedLevelId;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", levelId=").append(levelId);
        sb.append(", rightId=").append(rightId);
        sb.append(", index=").append(index);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", unlocked=").append(unlocked);
        sb.append(", unlockedLevelId=").append(unlockedLevelId);
        sb.append("]");
        return sb.toString();
    }
}