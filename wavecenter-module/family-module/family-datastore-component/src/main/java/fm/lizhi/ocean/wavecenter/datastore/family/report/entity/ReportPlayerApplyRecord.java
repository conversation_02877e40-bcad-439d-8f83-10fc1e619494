package fm.lizhi.ocean.wavecenter.datastore.family.report.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 主播举报申请表
 *
 * @date 2025-08-21 03:29:11
 */
@Table(name = "`report_player_apply_record`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReportPlayerApplyRecord {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 举报类型，6:大小号跳槽，7:原号跳槽
     */
    @Column(name= "`report_type`")
    private Integer reportType;

    /**
     * 举报者
     */
    @Column(name= "`report_user_id`")
    private Long reportUserId;

    /**
     * 举报者厅id
     */
    @Column(name= "`report_room_id`")
    private Long reportRoomId;

    /**
     * 举报者公会id
     */
    @Column(name= "`report_family_id`")
    private Long reportFamilyId;

    /**
     * 被举报者
     */
    @Column(name= "`accused_user_id`")
    private Long accusedUserId;

    /**
     * 被举报厅ID-挖槽厅
     */
    @Column(name= "`accused_room_id`")
    private Long accusedRoomId;

    /**
     * 被举报的厅所属公会-跳槽公会
     */
    @Column(name= "`accused_family_id`")
    private Long accusedFamilyId;

    /**
     * 被举报者的原厅ID-原号跳槽才有-被挖厅
     */
    @Column(name= "`accused_original_room_id`")
    private Long accusedOriginalRoomId;

    /**
     * 被举报者的原厅所属公会-原号跳槽才有-被挖公会
     */
    @Column(name= "`accused_original_family_id`")
    private Long accusedOriginalFamilyId;

    /**
     * (原号/小号)被举报者签约状态；0-未签约 1-生效中 2-已解约
     */
    @Column(name= "`accused_user_sign_status`")
    private Integer accusedUserSignStatus;

    /**
     * (原号/小号)被举报者签约时间
     */
    @Column(name= "`accused_user_sign_time`")
    private Date accusedUserSignTime;

    /**
     * 大号-大小号跳槽才有
     */
    @Column(name= "`accused_main_user_id`")
    private Long accusedMainUserId;

    /**
     * 大号厅id-大小号跳槽才有-被挖厅
     */
    @Column(name= "`accused_main_room_id`")
    private Long accusedMainRoomId;

    /**
     * 大号公会id-大小号跳槽才有-被挖公会
     */
    @Column(name= "`accused_main_family_id`")
    private Long accusedMainFamilyId;

    /**
     * 大号签约状态；0-未签约 1-生效中 2-已解约
     */
    @Column(name= "`accused_main_user_sign_status`")
    private Integer accusedMainUserSignStatus;

    /**
     * 大号签约时间
     */
    @Column(name= "`accused_main_user_sign_time`")
    private Date accusedMainUserSignTime;

    /**
     * 举报结果状态，0:待处理 1：成功，2：失败 
     */
    @Column(name= "`report_result_status`")
    private Integer reportResultStatus;

    /**
     * 举报结果原因
     */
    @Column(name= "`report_result_reason`")
    private String reportResultReason;

    /**
     * app设备号
     */
    @Column(name= "`app_device_id`")
    private String appDeviceId;

    /**
     * pc设备号
     */
    @Column(name= "`pc_device_id`")
    private String pcDeviceId;

    /**
     * 环境：TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 举报来源:pc/web
     */
    @Column(name= "`source`")
    private String source;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", reportType=").append(reportType);
        sb.append(", reportUserId=").append(reportUserId);
        sb.append(", reportRoomId=").append(reportRoomId);
        sb.append(", reportFamilyId=").append(reportFamilyId);
        sb.append(", accusedUserId=").append(accusedUserId);
        sb.append(", accusedRoomId=").append(accusedRoomId);
        sb.append(", accusedFamilyId=").append(accusedFamilyId);
        sb.append(", accusedOriginalRoomId=").append(accusedOriginalRoomId);
        sb.append(", accusedOriginalFamilyId=").append(accusedOriginalFamilyId);
        sb.append(", accusedUserSignStatus=").append(accusedUserSignStatus);
        sb.append(", accusedUserSignTime=").append(accusedUserSignTime);
        sb.append(", accusedMainUserId=").append(accusedMainUserId);
        sb.append(", accusedMainRoomId=").append(accusedMainRoomId);
        sb.append(", accusedMainFamilyId=").append(accusedMainFamilyId);
        sb.append(", accusedMainUserSignStatus=").append(accusedMainUserSignStatus);
        sb.append(", accusedMainUserSignTime=").append(accusedMainUserSignTime);
        sb.append(", reportResultStatus=").append(reportResultStatus);
        sb.append(", reportResultReason=").append(reportResultReason);
        sb.append(", appDeviceId=").append(appDeviceId);
        sb.append(", pcDeviceId=").append(pcDeviceId);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", operator=").append(operator);
        sb.append(", source=").append(source);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}