package fm.lizhi.ocean.wavecenter.datastore.family.report.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 单个跳槽主播收入流水周统计
 *
 * @date 2025-08-28 11:20:18
 */
@Table(name = "`report_accused_single_player_income_stat_week`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReportAccusedSinglePlayerIncomeStatWeek {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 日期 格式 YYYY-MM-DD
     */
    @Column(name= "`start_week_date`")
    private Date startWeekDate;

    /**
     * 日期 格式 YYYY-MM-DD
     */
    @Column(name= "`end_week_date`")
    private Date endWeekDate;

    /**
     * 跳槽主播id
     */
    @Column(name= "`accused_user_id`")
    private Long accusedUserId;

    /**
     * 被挖厅ID
     */
    @Column(name= "`room_id`")
    private Long roomId;

    /**
     * 被挖公会ID
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 挖槽厅ID
     */
    @Column(name= "`accused_room_id`")
    private Long accusedRoomId;

    /**
     * 挖槽厅公会ID
     */
    @Column(name= "`accused_family_id`")
    private Long accusedFamilyId;

    /**
     * 主播违规收益
     */
    @Column(name= "`income`")
    private Long income;

    /**
     * 环境：TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", startWeekDate=").append(startWeekDate);
        sb.append(", endWeekDate=").append(endWeekDate);
        sb.append(", accusedUserId=").append(accusedUserId);
        sb.append(", roomId=").append(roomId);
        sb.append(", familyId=").append(familyId);
        sb.append(", accusedRoomId=").append(accusedRoomId);
        sb.append(", accusedFamilyId=").append(accusedFamilyId);
        sb.append(", income=").append(income);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}