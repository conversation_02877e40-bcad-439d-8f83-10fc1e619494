package fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 线下广告展位配置
 *
 * @date 2025-08-15 06:05:39
 */
@Table(name = "`offline_zone_ad_position`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OfflineZoneAdPosition {
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 应用ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 环境：TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 主题
     */
    @Column(name= "`theme`")
    private String theme;

    /**
     * 横幅图片URL
     */
    @Column(name= "`banner`")
    private String banner;

    /**
     * 权重，数字越大越靠前
     */
    @Column(name= "`weight`")
    private Integer weight;

    /**
     * 跳转链接
     */
    @Column(name= "`jump_link`")
    private String jumpLink;

    /**
     * 上架状态：0-下架，1-上架
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 是否删除：0-否，1-是
     */
    @Column(name= "`deleted`")
    private Boolean deleted;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", theme=").append(theme);
        sb.append(", banner=").append(banner);
        sb.append(", weight=").append(weight);
        sb.append(", jumpLink=").append(jumpLink);
        sb.append(", status=").append(status);
        sb.append(", deleted=").append(deleted);
        sb.append(", operator=").append(operator);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}