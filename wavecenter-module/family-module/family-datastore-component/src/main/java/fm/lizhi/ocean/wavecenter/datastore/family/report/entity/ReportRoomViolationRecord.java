package fm.lizhi.ocean.wavecenter.datastore.family.report.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 厅违规记录表
 *
 * @date 2025-09-04 05:03:06
 */
@Table(name = "`report_room_violation_record`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReportRoomViolationRecord {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 举报申请记录id
     */
    @Column(name= "`player_report_apply_record_id`")
    private Long playerReportApplyRecordId;

    /**
     * 同一周期当前违规次数
     */
    @Column(name= "`count`")
    private Integer count;

    /**
     * 同一周期违规次数达成处罚条件值
     */
    @Column(name= "`condition_count`")
    private Integer conditionCount;

    /**
     * 达成厅处罚条件:0否 1是; ps:如果本次导致厅被处罚，值为1 
     */
    @Column(name= "`punished`")
    private Boolean punished;

    /**
     * 举报类型，6:大小号跳槽，7:原号跳槽
     */
    @Column(name= "`report_type`")
    private Integer reportType;

    /**
     * 被举报者
     */
    @Column(name= "`accused_user_id`")
    private Long accusedUserId;

    /**
     * 被举报厅ID-挖槽厅
     */
    @Column(name= "`accused_room_id`")
    private Long accusedRoomId;

    /**
     * 被举报的厅所属公会-挖槽厅公会
     */
    @Column(name= "`accused_family_id`")
    private Long accusedFamilyId;

    /**
     * 举报者厅id-被挖厅
     */
    @Column(name= "`room_id`")
    private Long roomId;

    /**
     * 举报者公会id-被挖厅公会
     */
    @Column(name= "`family_id`")
    private Long familyId;

    /**
     * 环境：TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 创建时间-违规时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 达成厅处罚条件:0否 1是; ps:如果本次导致厅被处罚，值为1 
     */
    @Column(name= "`reach_condition`")
    private Boolean reachCondition;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", playerReportApplyRecordId=").append(playerReportApplyRecordId);
        sb.append(", count=").append(count);
        sb.append(", conditionCount=").append(conditionCount);
        sb.append(", punished=").append(punished);
        sb.append(", reportType=").append(reportType);
        sb.append(", accusedUserId=").append(accusedUserId);
        sb.append(", accusedRoomId=").append(accusedRoomId);
        sb.append(", accusedFamilyId=").append(accusedFamilyId);
        sb.append(", roomId=").append(roomId);
        sb.append(", familyId=").append(familyId);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", operator=").append(operator);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", reachCondition=").append(reachCondition);
        sb.append("]");
        return sb.toString();
    }
}