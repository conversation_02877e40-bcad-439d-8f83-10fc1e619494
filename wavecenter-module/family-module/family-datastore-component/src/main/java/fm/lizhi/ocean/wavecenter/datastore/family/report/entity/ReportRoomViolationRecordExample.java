package fm.lizhi.ocean.wavecenter.datastore.family.report.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ReportRoomViolationRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_record
     *
     * @mbg.generated Thu Sep 04 17:03:06 CST 2025
     */
    public ReportRoomViolationRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_record
     *
     * @mbg.generated Thu Sep 04 17:03:06 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_record
     *
     * @mbg.generated Thu Sep 04 17:03:06 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_record
     *
     * @mbg.generated Thu Sep 04 17:03:06 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_record
     *
     * @mbg.generated Thu Sep 04 17:03:06 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_record
     *
     * @mbg.generated Thu Sep 04 17:03:06 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_record
     *
     * @mbg.generated Thu Sep 04 17:03:06 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_record
     *
     * @mbg.generated Thu Sep 04 17:03:06 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_record
     *
     * @mbg.generated Thu Sep 04 17:03:06 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_record
     *
     * @mbg.generated Thu Sep 04 17:03:06 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table report_room_violation_record
     *
     * @mbg.generated Thu Sep 04 17:03:06 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return fm.lizhi.ocean.wavecenter.datastore.family.report.entity.ReportRoomViolationRecord.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table report_room_violation_record
     *
     * @mbg.generated Thu Sep 04 17:03:06 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdIsNull() {
            addCriterion("player_report_apply_record_id is null");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdIsNotNull() {
            addCriterion("player_report_apply_record_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdEqualTo(Long value) {
            addCriterion("player_report_apply_record_id =", value, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdNotEqualTo(Long value) {
            addCriterion("player_report_apply_record_id <>", value, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdGreaterThan(Long value) {
            addCriterion("player_report_apply_record_id >", value, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdGreaterThanOrEqualTo(Long value) {
            addCriterion("player_report_apply_record_id >=", value, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdLessThan(Long value) {
            addCriterion("player_report_apply_record_id <", value, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdLessThanOrEqualTo(Long value) {
            addCriterion("player_report_apply_record_id <=", value, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdIn(List<Long> values) {
            addCriterion("player_report_apply_record_id in", values, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdNotIn(List<Long> values) {
            addCriterion("player_report_apply_record_id not in", values, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdBetween(Long value1, Long value2) {
            addCriterion("player_report_apply_record_id between", value1, value2, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andPlayerReportApplyRecordIdNotBetween(Long value1, Long value2) {
            addCriterion("player_report_apply_record_id not between", value1, value2, "playerReportApplyRecordId");
            return (Criteria) this;
        }

        public Criteria andCountIsNull() {
            addCriterion("count is null");
            return (Criteria) this;
        }

        public Criteria andCountIsNotNull() {
            addCriterion("count is not null");
            return (Criteria) this;
        }

        public Criteria andCountEqualTo(Integer value) {
            addCriterion("count =", value, "count");
            return (Criteria) this;
        }

        public Criteria andCountNotEqualTo(Integer value) {
            addCriterion("count <>", value, "count");
            return (Criteria) this;
        }

        public Criteria andCountGreaterThan(Integer value) {
            addCriterion("count >", value, "count");
            return (Criteria) this;
        }

        public Criteria andCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("count >=", value, "count");
            return (Criteria) this;
        }

        public Criteria andCountLessThan(Integer value) {
            addCriterion("count <", value, "count");
            return (Criteria) this;
        }

        public Criteria andCountLessThanOrEqualTo(Integer value) {
            addCriterion("count <=", value, "count");
            return (Criteria) this;
        }

        public Criteria andCountIn(List<Integer> values) {
            addCriterion("count in", values, "count");
            return (Criteria) this;
        }

        public Criteria andCountNotIn(List<Integer> values) {
            addCriterion("count not in", values, "count");
            return (Criteria) this;
        }

        public Criteria andCountBetween(Integer value1, Integer value2) {
            addCriterion("count between", value1, value2, "count");
            return (Criteria) this;
        }

        public Criteria andCountNotBetween(Integer value1, Integer value2) {
            addCriterion("count not between", value1, value2, "count");
            return (Criteria) this;
        }

        public Criteria andConditionCountIsNull() {
            addCriterion("condition_count is null");
            return (Criteria) this;
        }

        public Criteria andConditionCountIsNotNull() {
            addCriterion("condition_count is not null");
            return (Criteria) this;
        }

        public Criteria andConditionCountEqualTo(Integer value) {
            addCriterion("condition_count =", value, "conditionCount");
            return (Criteria) this;
        }

        public Criteria andConditionCountNotEqualTo(Integer value) {
            addCriterion("condition_count <>", value, "conditionCount");
            return (Criteria) this;
        }

        public Criteria andConditionCountGreaterThan(Integer value) {
            addCriterion("condition_count >", value, "conditionCount");
            return (Criteria) this;
        }

        public Criteria andConditionCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("condition_count >=", value, "conditionCount");
            return (Criteria) this;
        }

        public Criteria andConditionCountLessThan(Integer value) {
            addCriterion("condition_count <", value, "conditionCount");
            return (Criteria) this;
        }

        public Criteria andConditionCountLessThanOrEqualTo(Integer value) {
            addCriterion("condition_count <=", value, "conditionCount");
            return (Criteria) this;
        }

        public Criteria andConditionCountIn(List<Integer> values) {
            addCriterion("condition_count in", values, "conditionCount");
            return (Criteria) this;
        }

        public Criteria andConditionCountNotIn(List<Integer> values) {
            addCriterion("condition_count not in", values, "conditionCount");
            return (Criteria) this;
        }

        public Criteria andConditionCountBetween(Integer value1, Integer value2) {
            addCriterion("condition_count between", value1, value2, "conditionCount");
            return (Criteria) this;
        }

        public Criteria andConditionCountNotBetween(Integer value1, Integer value2) {
            addCriterion("condition_count not between", value1, value2, "conditionCount");
            return (Criteria) this;
        }

        public Criteria andPunishedIsNull() {
            addCriterion("punished is null");
            return (Criteria) this;
        }

        public Criteria andPunishedIsNotNull() {
            addCriterion("punished is not null");
            return (Criteria) this;
        }

        public Criteria andPunishedEqualTo(Boolean value) {
            addCriterion("punished =", value, "punished");
            return (Criteria) this;
        }

        public Criteria andPunishedNotEqualTo(Boolean value) {
            addCriterion("punished <>", value, "punished");
            return (Criteria) this;
        }

        public Criteria andPunishedGreaterThan(Boolean value) {
            addCriterion("punished >", value, "punished");
            return (Criteria) this;
        }

        public Criteria andPunishedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("punished >=", value, "punished");
            return (Criteria) this;
        }

        public Criteria andPunishedLessThan(Boolean value) {
            addCriterion("punished <", value, "punished");
            return (Criteria) this;
        }

        public Criteria andPunishedLessThanOrEqualTo(Boolean value) {
            addCriterion("punished <=", value, "punished");
            return (Criteria) this;
        }

        public Criteria andPunishedIn(List<Boolean> values) {
            addCriterion("punished in", values, "punished");
            return (Criteria) this;
        }

        public Criteria andPunishedNotIn(List<Boolean> values) {
            addCriterion("punished not in", values, "punished");
            return (Criteria) this;
        }

        public Criteria andPunishedBetween(Boolean value1, Boolean value2) {
            addCriterion("punished between", value1, value2, "punished");
            return (Criteria) this;
        }

        public Criteria andPunishedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("punished not between", value1, value2, "punished");
            return (Criteria) this;
        }

        public Criteria andReportTypeIsNull() {
            addCriterion("report_type is null");
            return (Criteria) this;
        }

        public Criteria andReportTypeIsNotNull() {
            addCriterion("report_type is not null");
            return (Criteria) this;
        }

        public Criteria andReportTypeEqualTo(Integer value) {
            addCriterion("report_type =", value, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeNotEqualTo(Integer value) {
            addCriterion("report_type <>", value, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeGreaterThan(Integer value) {
            addCriterion("report_type >", value, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("report_type >=", value, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeLessThan(Integer value) {
            addCriterion("report_type <", value, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeLessThanOrEqualTo(Integer value) {
            addCriterion("report_type <=", value, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeIn(List<Integer> values) {
            addCriterion("report_type in", values, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeNotIn(List<Integer> values) {
            addCriterion("report_type not in", values, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeBetween(Integer value1, Integer value2) {
            addCriterion("report_type between", value1, value2, "reportType");
            return (Criteria) this;
        }

        public Criteria andReportTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("report_type not between", value1, value2, "reportType");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdIsNull() {
            addCriterion("accused_user_id is null");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdIsNotNull() {
            addCriterion("accused_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdEqualTo(Long value) {
            addCriterion("accused_user_id =", value, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdNotEqualTo(Long value) {
            addCriterion("accused_user_id <>", value, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdGreaterThan(Long value) {
            addCriterion("accused_user_id >", value, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("accused_user_id >=", value, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdLessThan(Long value) {
            addCriterion("accused_user_id <", value, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdLessThanOrEqualTo(Long value) {
            addCriterion("accused_user_id <=", value, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdIn(List<Long> values) {
            addCriterion("accused_user_id in", values, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdNotIn(List<Long> values) {
            addCriterion("accused_user_id not in", values, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdBetween(Long value1, Long value2) {
            addCriterion("accused_user_id between", value1, value2, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedUserIdNotBetween(Long value1, Long value2) {
            addCriterion("accused_user_id not between", value1, value2, "accusedUserId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdIsNull() {
            addCriterion("accused_room_id is null");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdIsNotNull() {
            addCriterion("accused_room_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdEqualTo(Long value) {
            addCriterion("accused_room_id =", value, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdNotEqualTo(Long value) {
            addCriterion("accused_room_id <>", value, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdGreaterThan(Long value) {
            addCriterion("accused_room_id >", value, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdGreaterThanOrEqualTo(Long value) {
            addCriterion("accused_room_id >=", value, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdLessThan(Long value) {
            addCriterion("accused_room_id <", value, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdLessThanOrEqualTo(Long value) {
            addCriterion("accused_room_id <=", value, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdIn(List<Long> values) {
            addCriterion("accused_room_id in", values, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdNotIn(List<Long> values) {
            addCriterion("accused_room_id not in", values, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdBetween(Long value1, Long value2) {
            addCriterion("accused_room_id between", value1, value2, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedRoomIdNotBetween(Long value1, Long value2) {
            addCriterion("accused_room_id not between", value1, value2, "accusedRoomId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdIsNull() {
            addCriterion("accused_family_id is null");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdIsNotNull() {
            addCriterion("accused_family_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdEqualTo(Long value) {
            addCriterion("accused_family_id =", value, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdNotEqualTo(Long value) {
            addCriterion("accused_family_id <>", value, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdGreaterThan(Long value) {
            addCriterion("accused_family_id >", value, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("accused_family_id >=", value, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdLessThan(Long value) {
            addCriterion("accused_family_id <", value, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("accused_family_id <=", value, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdIn(List<Long> values) {
            addCriterion("accused_family_id in", values, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdNotIn(List<Long> values) {
            addCriterion("accused_family_id not in", values, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdBetween(Long value1, Long value2) {
            addCriterion("accused_family_id between", value1, value2, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andAccusedFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("accused_family_id not between", value1, value2, "accusedFamilyId");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNull() {
            addCriterion("room_id is null");
            return (Criteria) this;
        }

        public Criteria andRoomIdIsNotNull() {
            addCriterion("room_id is not null");
            return (Criteria) this;
        }

        public Criteria andRoomIdEqualTo(Long value) {
            addCriterion("room_id =", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotEqualTo(Long value) {
            addCriterion("room_id <>", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThan(Long value) {
            addCriterion("room_id >", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdGreaterThanOrEqualTo(Long value) {
            addCriterion("room_id >=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThan(Long value) {
            addCriterion("room_id <", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdLessThanOrEqualTo(Long value) {
            addCriterion("room_id <=", value, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdIn(List<Long> values) {
            addCriterion("room_id in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotIn(List<Long> values) {
            addCriterion("room_id not in", values, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdBetween(Long value1, Long value2) {
            addCriterion("room_id between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andRoomIdNotBetween(Long value1, Long value2) {
            addCriterion("room_id not between", value1, value2, "roomId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNull() {
            addCriterion("family_id is null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNotNull() {
            addCriterion("family_id is not null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdEqualTo(Long value) {
            addCriterion("family_id =", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotEqualTo(Long value) {
            addCriterion("family_id <>", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThan(Long value) {
            addCriterion("family_id >", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("family_id >=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThan(Long value) {
            addCriterion("family_id <", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("family_id <=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIn(List<Long> values) {
            addCriterion("family_id in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotIn(List<Long> values) {
            addCriterion("family_id not in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdBetween(Long value1, Long value2) {
            addCriterion("family_id between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("family_id not between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNull() {
            addCriterion("deploy_env is null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIsNotNull() {
            addCriterion("deploy_env is not null");
            return (Criteria) this;
        }

        public Criteria andDeployEnvEqualTo(String value) {
            addCriterion("deploy_env =", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotEqualTo(String value) {
            addCriterion("deploy_env <>", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThan(String value) {
            addCriterion("deploy_env >", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvGreaterThanOrEqualTo(String value) {
            addCriterion("deploy_env >=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThan(String value) {
            addCriterion("deploy_env <", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLessThanOrEqualTo(String value) {
            addCriterion("deploy_env <=", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvLike(String value) {
            addCriterion("deploy_env like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotLike(String value) {
            addCriterion("deploy_env not like", value, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvIn(List<String> values) {
            addCriterion("deploy_env in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotIn(List<String> values) {
            addCriterion("deploy_env not in", values, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvBetween(String value1, String value2) {
            addCriterion("deploy_env between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andDeployEnvNotBetween(String value1, String value2) {
            addCriterion("deploy_env not between", value1, value2, "deployEnv");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNull() {
            addCriterion("operator is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNotNull() {
            addCriterion("operator is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorEqualTo(String value) {
            addCriterion("operator =", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotEqualTo(String value) {
            addCriterion("operator <>", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThan(String value) {
            addCriterion("operator >", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThanOrEqualTo(String value) {
            addCriterion("operator >=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThan(String value) {
            addCriterion("operator <", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThanOrEqualTo(String value) {
            addCriterion("operator <=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLike(String value) {
            addCriterion("operator like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotLike(String value) {
            addCriterion("operator not like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorIn(List<String> values) {
            addCriterion("operator in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotIn(List<String> values) {
            addCriterion("operator not in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorBetween(String value1, String value2) {
            addCriterion("operator between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotBetween(String value1, String value2) {
            addCriterion("operator not between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andReachConditionIsNull() {
            addCriterion("reach_condition is null");
            return (Criteria) this;
        }

        public Criteria andReachConditionIsNotNull() {
            addCriterion("reach_condition is not null");
            return (Criteria) this;
        }

        public Criteria andReachConditionEqualTo(Boolean value) {
            addCriterion("reach_condition =", value, "reachCondition");
            return (Criteria) this;
        }

        public Criteria andReachConditionNotEqualTo(Boolean value) {
            addCriterion("reach_condition <>", value, "reachCondition");
            return (Criteria) this;
        }

        public Criteria andReachConditionGreaterThan(Boolean value) {
            addCriterion("reach_condition >", value, "reachCondition");
            return (Criteria) this;
        }

        public Criteria andReachConditionGreaterThanOrEqualTo(Boolean value) {
            addCriterion("reach_condition >=", value, "reachCondition");
            return (Criteria) this;
        }

        public Criteria andReachConditionLessThan(Boolean value) {
            addCriterion("reach_condition <", value, "reachCondition");
            return (Criteria) this;
        }

        public Criteria andReachConditionLessThanOrEqualTo(Boolean value) {
            addCriterion("reach_condition <=", value, "reachCondition");
            return (Criteria) this;
        }

        public Criteria andReachConditionIn(List<Boolean> values) {
            addCriterion("reach_condition in", values, "reachCondition");
            return (Criteria) this;
        }

        public Criteria andReachConditionNotIn(List<Boolean> values) {
            addCriterion("reach_condition not in", values, "reachCondition");
            return (Criteria) this;
        }

        public Criteria andReachConditionBetween(Boolean value1, Boolean value2) {
            addCriterion("reach_condition between", value1, value2, "reachCondition");
            return (Criteria) this;
        }

        public Criteria andReachConditionNotBetween(Boolean value1, Boolean value2) {
            addCriterion("reach_condition not between", value1, value2, "reachCondition");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table report_room_violation_record
     *
     * @mbg.generated do_not_delete_during_merge Thu Sep 04 17:03:06 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table report_room_violation_record
     *
     * @mbg.generated Thu Sep 04 17:03:06 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}