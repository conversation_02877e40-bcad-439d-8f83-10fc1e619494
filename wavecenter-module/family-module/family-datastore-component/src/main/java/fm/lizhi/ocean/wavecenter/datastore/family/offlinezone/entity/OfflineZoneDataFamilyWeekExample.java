package fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class OfflineZoneDataFamilyWeekExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_family_week
     *
     * @mbg.generated Tue Aug 12 17:25:23 CST 2025
     */
    public OfflineZoneDataFamilyWeekExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_family_week
     *
     * @mbg.generated Tue Aug 12 17:25:23 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_family_week
     *
     * @mbg.generated Tue Aug 12 17:25:23 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_family_week
     *
     * @mbg.generated Tue Aug 12 17:25:23 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_family_week
     *
     * @mbg.generated Tue Aug 12 17:25:23 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_family_week
     *
     * @mbg.generated Tue Aug 12 17:25:23 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_family_week
     *
     * @mbg.generated Tue Aug 12 17:25:23 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_family_week
     *
     * @mbg.generated Tue Aug 12 17:25:23 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_family_week
     *
     * @mbg.generated Tue Aug 12 17:25:23 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_family_week
     *
     * @mbg.generated Tue Aug 12 17:25:23 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table offline_zone_data_family_week
     *
     * @mbg.generated Tue Aug 12 17:25:23 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public Class getEntityClass() {
        return fm.lizhi.ocean.wavecenter.datastore.family.offlinezone.entity.OfflineZoneDataFamilyWeek.class;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table offline_zone_data_family_week
     *
     * @mbg.generated Tue Aug 12 17:25:23 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(Integer value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(Integer value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(Integer value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(Integer value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<Integer> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<Integer> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(Integer value1, Integer value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateIsNull() {
            addCriterion("start_week_date is null");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateIsNotNull() {
            addCriterion("start_week_date is not null");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date =", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date <>", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateGreaterThan(Date value) {
            addCriterionForJDBCDate("start_week_date >", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date >=", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateLessThan(Date value) {
            addCriterionForJDBCDate("start_week_date <", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("start_week_date <=", value, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateIn(List<Date> values) {
            addCriterionForJDBCDate("start_week_date in", values, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("start_week_date not in", values, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("start_week_date between", value1, value2, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andStartWeekDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("start_week_date not between", value1, value2, "startWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateIsNull() {
            addCriterion("end_week_date is null");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateIsNotNull() {
            addCriterion("end_week_date is not null");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date =", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date <>", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateGreaterThan(Date value) {
            addCriterionForJDBCDate("end_week_date >", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date >=", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateLessThan(Date value) {
            addCriterionForJDBCDate("end_week_date <", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("end_week_date <=", value, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateIn(List<Date> values) {
            addCriterionForJDBCDate("end_week_date in", values, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("end_week_date not in", values, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("end_week_date between", value1, value2, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andEndWeekDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("end_week_date not between", value1, value2, "endWeekDate");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNull() {
            addCriterion("family_id is null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIsNotNull() {
            addCriterion("family_id is not null");
            return (Criteria) this;
        }

        public Criteria andFamilyIdEqualTo(Long value) {
            addCriterion("family_id =", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotEqualTo(Long value) {
            addCriterion("family_id <>", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThan(Long value) {
            addCriterion("family_id >", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("family_id >=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThan(Long value) {
            addCriterion("family_id <", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdLessThanOrEqualTo(Long value) {
            addCriterion("family_id <=", value, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdIn(List<Long> values) {
            addCriterion("family_id in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotIn(List<Long> values) {
            addCriterion("family_id not in", values, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdBetween(Long value1, Long value2) {
            addCriterion("family_id between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyIdNotBetween(Long value1, Long value2) {
            addCriterion("family_id not between", value1, value2, "familyId");
            return (Criteria) this;
        }

        public Criteria andFamilyNameIsNull() {
            addCriterion("family_name is null");
            return (Criteria) this;
        }

        public Criteria andFamilyNameIsNotNull() {
            addCriterion("family_name is not null");
            return (Criteria) this;
        }

        public Criteria andFamilyNameEqualTo(String value) {
            addCriterion("family_name =", value, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameNotEqualTo(String value) {
            addCriterion("family_name <>", value, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameGreaterThan(String value) {
            addCriterion("family_name >", value, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameGreaterThanOrEqualTo(String value) {
            addCriterion("family_name >=", value, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameLessThan(String value) {
            addCriterion("family_name <", value, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameLessThanOrEqualTo(String value) {
            addCriterion("family_name <=", value, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameLike(String value) {
            addCriterion("family_name like", value, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameNotLike(String value) {
            addCriterion("family_name not like", value, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameIn(List<String> values) {
            addCriterion("family_name in", values, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameNotIn(List<String> values) {
            addCriterion("family_name not in", values, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameBetween(String value1, String value2) {
            addCriterion("family_name between", value1, value2, "familyName");
            return (Criteria) this;
        }

        public Criteria andFamilyNameNotBetween(String value1, String value2) {
            addCriterion("family_name not between", value1, value2, "familyName");
            return (Criteria) this;
        }

        public Criteria andBasicCntIsNull() {
            addCriterion("basic_cnt is null");
            return (Criteria) this;
        }

        public Criteria andBasicCntIsNotNull() {
            addCriterion("basic_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andBasicCntEqualTo(Integer value) {
            addCriterion("basic_cnt =", value, "basicCnt");
            return (Criteria) this;
        }

        public Criteria andBasicCntNotEqualTo(Integer value) {
            addCriterion("basic_cnt <>", value, "basicCnt");
            return (Criteria) this;
        }

        public Criteria andBasicCntGreaterThan(Integer value) {
            addCriterion("basic_cnt >", value, "basicCnt");
            return (Criteria) this;
        }

        public Criteria andBasicCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("basic_cnt >=", value, "basicCnt");
            return (Criteria) this;
        }

        public Criteria andBasicCntLessThan(Integer value) {
            addCriterion("basic_cnt <", value, "basicCnt");
            return (Criteria) this;
        }

        public Criteria andBasicCntLessThanOrEqualTo(Integer value) {
            addCriterion("basic_cnt <=", value, "basicCnt");
            return (Criteria) this;
        }

        public Criteria andBasicCntIn(List<Integer> values) {
            addCriterion("basic_cnt in", values, "basicCnt");
            return (Criteria) this;
        }

        public Criteria andBasicCntNotIn(List<Integer> values) {
            addCriterion("basic_cnt not in", values, "basicCnt");
            return (Criteria) this;
        }

        public Criteria andBasicCntBetween(Integer value1, Integer value2) {
            addCriterion("basic_cnt between", value1, value2, "basicCnt");
            return (Criteria) this;
        }

        public Criteria andBasicCntNotBetween(Integer value1, Integer value2) {
            addCriterion("basic_cnt not between", value1, value2, "basicCnt");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntIsNull() {
            addCriterion("offline_hall_cnt is null");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntIsNotNull() {
            addCriterion("offline_hall_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntEqualTo(Long value) {
            addCriterion("offline_hall_cnt =", value, "offlineHallCnt");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntNotEqualTo(Long value) {
            addCriterion("offline_hall_cnt <>", value, "offlineHallCnt");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntGreaterThan(Long value) {
            addCriterion("offline_hall_cnt >", value, "offlineHallCnt");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntGreaterThanOrEqualTo(Long value) {
            addCriterion("offline_hall_cnt >=", value, "offlineHallCnt");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntLessThan(Long value) {
            addCriterion("offline_hall_cnt <", value, "offlineHallCnt");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntLessThanOrEqualTo(Long value) {
            addCriterion("offline_hall_cnt <=", value, "offlineHallCnt");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntIn(List<Long> values) {
            addCriterion("offline_hall_cnt in", values, "offlineHallCnt");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntNotIn(List<Long> values) {
            addCriterion("offline_hall_cnt not in", values, "offlineHallCnt");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntBetween(Long value1, Long value2) {
            addCriterion("offline_hall_cnt between", value1, value2, "offlineHallCnt");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntNotBetween(Long value1, Long value2) {
            addCriterion("offline_hall_cnt not between", value1, value2, "offlineHallCnt");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntRateIsNull() {
            addCriterion("offline_hall_cnt_rate is null");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntRateIsNotNull() {
            addCriterion("offline_hall_cnt_rate is not null");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntRateEqualTo(BigDecimal value) {
            addCriterion("offline_hall_cnt_rate =", value, "offlineHallCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntRateNotEqualTo(BigDecimal value) {
            addCriterion("offline_hall_cnt_rate <>", value, "offlineHallCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntRateGreaterThan(BigDecimal value) {
            addCriterion("offline_hall_cnt_rate >", value, "offlineHallCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("offline_hall_cnt_rate >=", value, "offlineHallCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntRateLessThan(BigDecimal value) {
            addCriterion("offline_hall_cnt_rate <", value, "offlineHallCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("offline_hall_cnt_rate <=", value, "offlineHallCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntRateIn(List<BigDecimal> values) {
            addCriterion("offline_hall_cnt_rate in", values, "offlineHallCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntRateNotIn(List<BigDecimal> values) {
            addCriterion("offline_hall_cnt_rate not in", values, "offlineHallCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("offline_hall_cnt_rate between", value1, value2, "offlineHallCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflineHallCntRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("offline_hall_cnt_rate not between", value1, value2, "offlineHallCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeIsNull() {
            addCriterion("offline_hall_income is null");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeIsNotNull() {
            addCriterion("offline_hall_income is not null");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeEqualTo(BigDecimal value) {
            addCriterion("offline_hall_income =", value, "offlineHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeNotEqualTo(BigDecimal value) {
            addCriterion("offline_hall_income <>", value, "offlineHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeGreaterThan(BigDecimal value) {
            addCriterion("offline_hall_income >", value, "offlineHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("offline_hall_income >=", value, "offlineHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeLessThan(BigDecimal value) {
            addCriterion("offline_hall_income <", value, "offlineHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("offline_hall_income <=", value, "offlineHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeIn(List<BigDecimal> values) {
            addCriterion("offline_hall_income in", values, "offlineHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeNotIn(List<BigDecimal> values) {
            addCriterion("offline_hall_income not in", values, "offlineHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("offline_hall_income between", value1, value2, "offlineHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("offline_hall_income not between", value1, value2, "offlineHallIncome");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeRateIsNull() {
            addCriterion("offline_hall_income_rate is null");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeRateIsNotNull() {
            addCriterion("offline_hall_income_rate is not null");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeRateEqualTo(BigDecimal value) {
            addCriterion("offline_hall_income_rate =", value, "offlineHallIncomeRate");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeRateNotEqualTo(BigDecimal value) {
            addCriterion("offline_hall_income_rate <>", value, "offlineHallIncomeRate");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeRateGreaterThan(BigDecimal value) {
            addCriterion("offline_hall_income_rate >", value, "offlineHallIncomeRate");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("offline_hall_income_rate >=", value, "offlineHallIncomeRate");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeRateLessThan(BigDecimal value) {
            addCriterion("offline_hall_income_rate <", value, "offlineHallIncomeRate");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("offline_hall_income_rate <=", value, "offlineHallIncomeRate");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeRateIn(List<BigDecimal> values) {
            addCriterion("offline_hall_income_rate in", values, "offlineHallIncomeRate");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeRateNotIn(List<BigDecimal> values) {
            addCriterion("offline_hall_income_rate not in", values, "offlineHallIncomeRate");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("offline_hall_income_rate between", value1, value2, "offlineHallIncomeRate");
            return (Criteria) this;
        }

        public Criteria andOfflineHallIncomeRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("offline_hall_income_rate not between", value1, value2, "offlineHallIncomeRate");
            return (Criteria) this;
        }

        public Criteria andPlayerCntIsNull() {
            addCriterion("player_cnt is null");
            return (Criteria) this;
        }

        public Criteria andPlayerCntIsNotNull() {
            addCriterion("player_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andPlayerCntEqualTo(Integer value) {
            addCriterion("player_cnt =", value, "playerCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerCntNotEqualTo(Integer value) {
            addCriterion("player_cnt <>", value, "playerCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerCntGreaterThan(Integer value) {
            addCriterion("player_cnt >", value, "playerCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("player_cnt >=", value, "playerCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerCntLessThan(Integer value) {
            addCriterion("player_cnt <", value, "playerCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerCntLessThanOrEqualTo(Integer value) {
            addCriterion("player_cnt <=", value, "playerCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerCntIn(List<Integer> values) {
            addCriterion("player_cnt in", values, "playerCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerCntNotIn(List<Integer> values) {
            addCriterion("player_cnt not in", values, "playerCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerCntBetween(Integer value1, Integer value2) {
            addCriterion("player_cnt between", value1, value2, "playerCnt");
            return (Criteria) this;
        }

        public Criteria andPlayerCntNotBetween(Integer value1, Integer value2) {
            addCriterion("player_cnt not between", value1, value2, "playerCnt");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntIsNull() {
            addCriterion("offline_player_cnt is null");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntIsNotNull() {
            addCriterion("offline_player_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntEqualTo(Integer value) {
            addCriterion("offline_player_cnt =", value, "offlinePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntNotEqualTo(Integer value) {
            addCriterion("offline_player_cnt <>", value, "offlinePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntGreaterThan(Integer value) {
            addCriterion("offline_player_cnt >", value, "offlinePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("offline_player_cnt >=", value, "offlinePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntLessThan(Integer value) {
            addCriterion("offline_player_cnt <", value, "offlinePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntLessThanOrEqualTo(Integer value) {
            addCriterion("offline_player_cnt <=", value, "offlinePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntIn(List<Integer> values) {
            addCriterion("offline_player_cnt in", values, "offlinePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntNotIn(List<Integer> values) {
            addCriterion("offline_player_cnt not in", values, "offlinePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntBetween(Integer value1, Integer value2) {
            addCriterion("offline_player_cnt between", value1, value2, "offlinePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntNotBetween(Integer value1, Integer value2) {
            addCriterion("offline_player_cnt not between", value1, value2, "offlinePlayerCnt");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateIsNull() {
            addCriterion("offline_player_cnt_rate is null");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateIsNotNull() {
            addCriterion("offline_player_cnt_rate is not null");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateEqualTo(BigDecimal value) {
            addCriterion("offline_player_cnt_rate =", value, "offlinePlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateNotEqualTo(BigDecimal value) {
            addCriterion("offline_player_cnt_rate <>", value, "offlinePlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateGreaterThan(BigDecimal value) {
            addCriterion("offline_player_cnt_rate >", value, "offlinePlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("offline_player_cnt_rate >=", value, "offlinePlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateLessThan(BigDecimal value) {
            addCriterion("offline_player_cnt_rate <", value, "offlinePlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("offline_player_cnt_rate <=", value, "offlinePlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateIn(List<BigDecimal> values) {
            addCriterion("offline_player_cnt_rate in", values, "offlinePlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateNotIn(List<BigDecimal> values) {
            addCriterion("offline_player_cnt_rate not in", values, "offlinePlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("offline_player_cnt_rate between", value1, value2, "offlinePlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andOfflinePlayerCntRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("offline_player_cnt_rate not between", value1, value2, "offlinePlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntIsNull() {
            addCriterion("protected_player_cnt is null");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntIsNotNull() {
            addCriterion("protected_player_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntEqualTo(Integer value) {
            addCriterion("protected_player_cnt =", value, "protectedPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntNotEqualTo(Integer value) {
            addCriterion("protected_player_cnt <>", value, "protectedPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntGreaterThan(Integer value) {
            addCriterion("protected_player_cnt >", value, "protectedPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntGreaterThanOrEqualTo(Integer value) {
            addCriterion("protected_player_cnt >=", value, "protectedPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntLessThan(Integer value) {
            addCriterion("protected_player_cnt <", value, "protectedPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntLessThanOrEqualTo(Integer value) {
            addCriterion("protected_player_cnt <=", value, "protectedPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntIn(List<Integer> values) {
            addCriterion("protected_player_cnt in", values, "protectedPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntNotIn(List<Integer> values) {
            addCriterion("protected_player_cnt not in", values, "protectedPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntBetween(Integer value1, Integer value2) {
            addCriterion("protected_player_cnt between", value1, value2, "protectedPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntNotBetween(Integer value1, Integer value2) {
            addCriterion("protected_player_cnt not between", value1, value2, "protectedPlayerCnt");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateIsNull() {
            addCriterion("protected_player_cnt_rate is null");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateIsNotNull() {
            addCriterion("protected_player_cnt_rate is not null");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateEqualTo(BigDecimal value) {
            addCriterion("protected_player_cnt_rate =", value, "protectedPlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateNotEqualTo(BigDecimal value) {
            addCriterion("protected_player_cnt_rate <>", value, "protectedPlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateGreaterThan(BigDecimal value) {
            addCriterion("protected_player_cnt_rate >", value, "protectedPlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("protected_player_cnt_rate >=", value, "protectedPlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateLessThan(BigDecimal value) {
            addCriterion("protected_player_cnt_rate <", value, "protectedPlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("protected_player_cnt_rate <=", value, "protectedPlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateIn(List<BigDecimal> values) {
            addCriterion("protected_player_cnt_rate in", values, "protectedPlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateNotIn(List<BigDecimal> values) {
            addCriterion("protected_player_cnt_rate not in", values, "protectedPlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("protected_player_cnt_rate between", value1, value2, "protectedPlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("protected_player_cnt_rate not between", value1, value2, "protectedPlayerCntRate");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRealTimeIsNull() {
            addCriterion("protected_player_cnt_real_time is null");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRealTimeIsNotNull() {
            addCriterion("protected_player_cnt_real_time is not null");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRealTimeEqualTo(Integer value) {
            addCriterion("protected_player_cnt_real_time =", value, "protectedPlayerCntRealTime");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRealTimeNotEqualTo(Integer value) {
            addCriterion("protected_player_cnt_real_time <>", value, "protectedPlayerCntRealTime");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRealTimeGreaterThan(Integer value) {
            addCriterion("protected_player_cnt_real_time >", value, "protectedPlayerCntRealTime");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRealTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("protected_player_cnt_real_time >=", value, "protectedPlayerCntRealTime");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRealTimeLessThan(Integer value) {
            addCriterion("protected_player_cnt_real_time <", value, "protectedPlayerCntRealTime");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRealTimeLessThanOrEqualTo(Integer value) {
            addCriterion("protected_player_cnt_real_time <=", value, "protectedPlayerCntRealTime");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRealTimeIn(List<Integer> values) {
            addCriterion("protected_player_cnt_real_time in", values, "protectedPlayerCntRealTime");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRealTimeNotIn(List<Integer> values) {
            addCriterion("protected_player_cnt_real_time not in", values, "protectedPlayerCntRealTime");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRealTimeBetween(Integer value1, Integer value2) {
            addCriterion("protected_player_cnt_real_time between", value1, value2, "protectedPlayerCntRealTime");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRealTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("protected_player_cnt_real_time not between", value1, value2, "protectedPlayerCntRealTime");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateRealTimeIsNull() {
            addCriterion("protected_player_cnt_rate_real_time is null");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateRealTimeIsNotNull() {
            addCriterion("protected_player_cnt_rate_real_time is not null");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateRealTimeEqualTo(BigDecimal value) {
            addCriterion("protected_player_cnt_rate_real_time =", value, "protectedPlayerCntRateRealTime");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateRealTimeNotEqualTo(BigDecimal value) {
            addCriterion("protected_player_cnt_rate_real_time <>", value, "protectedPlayerCntRateRealTime");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateRealTimeGreaterThan(BigDecimal value) {
            addCriterion("protected_player_cnt_rate_real_time >", value, "protectedPlayerCntRateRealTime");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateRealTimeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("protected_player_cnt_rate_real_time >=", value, "protectedPlayerCntRateRealTime");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateRealTimeLessThan(BigDecimal value) {
            addCriterion("protected_player_cnt_rate_real_time <", value, "protectedPlayerCntRateRealTime");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateRealTimeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("protected_player_cnt_rate_real_time <=", value, "protectedPlayerCntRateRealTime");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateRealTimeIn(List<BigDecimal> values) {
            addCriterion("protected_player_cnt_rate_real_time in", values, "protectedPlayerCntRateRealTime");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateRealTimeNotIn(List<BigDecimal> values) {
            addCriterion("protected_player_cnt_rate_real_time not in", values, "protectedPlayerCntRateRealTime");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateRealTimeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("protected_player_cnt_rate_real_time between", value1, value2, "protectedPlayerCntRateRealTime");
            return (Criteria) this;
        }

        public Criteria andProtectedPlayerCntRateRealTimeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("protected_player_cnt_rate_real_time not between", value1, value2, "protectedPlayerCntRateRealTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNull() {
            addCriterion("modify_time is null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIsNotNull() {
            addCriterion("modify_time is not null");
            return (Criteria) this;
        }

        public Criteria andModifyTimeEqualTo(Date value) {
            addCriterion("modify_time =", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotEqualTo(Date value) {
            addCriterion("modify_time <>", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThan(Date value) {
            addCriterion("modify_time >", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("modify_time >=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThan(Date value) {
            addCriterion("modify_time <", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeLessThanOrEqualTo(Date value) {
            addCriterion("modify_time <=", value, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeIn(List<Date> values) {
            addCriterion("modify_time in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotIn(List<Date> values) {
            addCriterion("modify_time not in", values, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeBetween(Date value1, Date value2) {
            addCriterion("modify_time between", value1, value2, "modifyTime");
            return (Criteria) this;
        }

        public Criteria andModifyTimeNotBetween(Date value1, Date value2) {
            addCriterion("modify_time not between", value1, value2, "modifyTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table offline_zone_data_family_week
     *
     * @mbg.generated do_not_delete_during_merge Tue Aug 12 17:25:23 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table offline_zone_data_family_week
     *
     * @mbg.generated Tue Aug 12 17:25:23 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}