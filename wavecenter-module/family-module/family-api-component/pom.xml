<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>fm.lizhi.ocean.wavecenter</groupId>
        <artifactId>family-module</artifactId>
        <version>2.0.6-SNAPSHOT</version>
    </parent>
    <artifactId>family-api-component</artifactId>

    <!-- 添加依赖，参考grow-api-component -->
    <dependencies>
        <!--region =================基础架构的依赖=================-->
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-serviceclient</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--endregion-->

        <!--region ====================        本项目依赖        ==================== -->

        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>user-api-component</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>fm.lizhi.ocean.wavecenter</groupId>
            <artifactId>wavecenter-api</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>

        <!--endregion-->

        <!--region ====================        第二方框架依赖        ==================== -->
        <dependency>
            <groupId>fm.lizhi.ocean.wave</groupId>
            <artifactId>wave-server-common-context</artifactId>
        </dependency>
        <!--endregion-->

        <!--region ====================        第三方框架依赖        ==================== -->
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>

        <!--endregion-->
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>fm.lizhi.commons</groupId>
                <artifactId>autoapi-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>