package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean;

import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 签约厅数据
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class OfflineZoneRoomDataBean {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 周开始日期，格式YYYY-MM-DD
     */
    private Date startWeekDate;

    /**
     * 周结束日期，格式YYYY-MM-DD
     */
    private Date endWeekDate;

    /**
     * 公会ID
     */
    private Long familyId;

    /**
     * 公会名称
     */
    private String familyName;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 厅主信息
     */
    private UserBean njInfo;

    /**
     * 厅主名称
     */
    private String njName;

    /**
     * 厅签约时间
     */
    private Date beginSignTime;

    /**
     * 厅分类：0 线下，1 线上
     */
    private Integer category;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 厅收入
     */
    private BigDecimal income;

    /**
     * 线下主播数
     */
    private Integer offlinePlayerCnt;

    /**
     * 线下主播数占比
     */
    private BigDecimal offlinePlayerCntRate;

    /**
     * 受保护主播数
     */
    private Integer protectedPlayerCnt;

    /**
     * 受保护主播数占比
     */
    private BigDecimal protectedPlayerCntRate;

    /**
     * 线下主播收入
     */
    private BigDecimal offlinePlayerIncome;

    /**
     * 线下主播收入占比
     */
    private BigDecimal offlinePlayerIncomeRate;


}
