package fm.lizhi.ocean.wavecenter.module.api.family.report.bean;

import lombok.Data;

/**
 * 主播举报处理结果
 */
@Data
public class PlayerReportApplyOperateResultBean {
    
    /**
     * 操作类型(1：封禁 2 解封)
     */
    private Integer operateType;
    
    /**
     * 被封禁的用户
     */
    private OperatedPlayer operatedPlayer;
    
    /**
     * 操作对象类型(1：账号，2：厅开播权限 3设备)
     */
    private Integer operateObjType;
    
    /**
     * 操作时长;eg:设备封禁时长
     */
    private Integer operateDuration;
}