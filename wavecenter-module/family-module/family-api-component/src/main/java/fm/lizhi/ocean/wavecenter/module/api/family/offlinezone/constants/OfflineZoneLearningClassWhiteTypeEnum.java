package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 线下专区学习课堂白名单类型枚举, 用于指定学习课堂对哪些类型的用户可见. 预留扩展性, 目前仅支持公会类型.
 */
@AllArgsConstructor
@Getter
public enum OfflineZoneLearningClassWhiteTypeEnum {

    /**
     * 公会, 即学习课堂对公会内的家族管理和厅管理可见
     */
    FAMILY(1),
    ;

    /**
     * 枚举值
     */
    private final Integer value;
}
