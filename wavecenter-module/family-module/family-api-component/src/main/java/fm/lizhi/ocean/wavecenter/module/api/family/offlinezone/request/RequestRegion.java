package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request;

import fm.lizhi.ocean.wave.server.common.request.RequestAppIdAware;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 获取行政区划列表请求参数
 * <AUTHOR>
 */
@Data
public class RequestRegion implements RequestAppIdAware {

    @NotNull(message = "appId不能为空")
    private Integer appId;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}