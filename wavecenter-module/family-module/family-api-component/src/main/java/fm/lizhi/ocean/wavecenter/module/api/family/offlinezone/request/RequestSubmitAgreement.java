package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request;

import fm.lizhi.ocean.wave.server.common.request.RequestAppIdAware;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 提交跳槽保护申请
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestSubmitAgreement implements RequestAppIdAware {


    /**
     * 更新时必传
     */
    private Long id;

    /**
     * 应用ID
     */
    @NotNull(message = "appId不能为空")
    private int appId;

    /**
     * 公会ID
     */
    @NotNull(message = "公会ID不能为空")
    private Long familyId;

    /**
     * 厅ID
     */
    @NotNull(message = "厅ID不能为空")
    private Long njId;

    /**
     * 主播ID
     */
    @NotNull(message = "主播ID不能为空")
    private Long playerId;

    /**
     * 协议生效开始时间
     */
    private Date agreementStartTime;

    /**
     * 协议生效结束时间
     */
    private Date agreementEndTime;

    /**
     * 是否盖章签字：0-否，1-是
     */
    private Boolean stampSign;

    /**
     * 协议文件JSON
     */
    private String agreementFileJson;

    /**
     * 上传用户ID
     */
    private Long uploadUserId;


    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
