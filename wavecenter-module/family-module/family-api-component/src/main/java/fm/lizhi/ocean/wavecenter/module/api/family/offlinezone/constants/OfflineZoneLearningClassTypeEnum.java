package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 线下专区学习课堂类型枚举
 */
@AllArgsConstructor
@Getter
public enum OfflineZoneLearningClassTypeEnum {

    /**
     * 文档链接
     */
    DOCUMENT_LINK(1, false),
    /**
     * 视频文件
     */
    VIDEO_FILE(2, true),
    ;

    /**
     * 枚举值
     */
    private final Integer value;
    /**
     * 是否使用创作者平台罗马上传
     */
    private final boolean romeFs;

    /**
     * 根据枚举值获取对应的枚举类型
     *
     * @param value 枚举值
     * @return 对应的枚举类型，如果没有找到则返回null
     */
    public static OfflineZoneLearningClassTypeEnum fromValue(Integer value) {
        for (OfflineZoneLearningClassTypeEnum typeEnum : values()) {
            if (Objects.equals(typeEnum.getValue(), value)) {
                return typeEnum;
            }
        }
        return null;
    }
}
