package fm.lizhi.ocean.wavecenter.module.api.family.report.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum OperateDurationTypeEnum {

    /**
     * 一天
     */
    ONE_DAY(1, 24 * 60),

    /**
     * 三天
     */
    THREE_DAY(2, 3 * 24 * 60),

    /**
     * 永封
     */
    PERMANENT(99, -1),

    ;
    private final Integer code;
    private final Integer minute;


    public static OperateDurationTypeEnum getByCode(Integer code) {
        for (OperateDurationTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
