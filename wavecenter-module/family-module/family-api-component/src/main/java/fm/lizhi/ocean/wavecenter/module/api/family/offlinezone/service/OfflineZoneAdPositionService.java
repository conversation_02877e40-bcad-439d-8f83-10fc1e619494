package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListAdPositionBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestListAdPosition;

import javax.validation.Valid;
import java.util.List;

/**
 * 线下专区广告展位服务, 前接口错误码以258开头.
 * <ul>
 *     <li>错误码规范参考<a href="https://lizhi2021.feishu.cn/wiki/Kulywk1KliQVGTktY56cny01nvg#JYrkdVrauo2bhbx0IeCcYSjsnMh">错误码规范</a></li>
 *     <li>状态码请维护在<a href="https://lizhi2021.feishu.cn/wiki/Ld0Rw5uvGiUimnkgez5cSVJ9n1c">创作者平台状态码维护文档</a></li>
 *     <li>通用错误码请见{@link CommonService}</li>
 * </ul>
 */
public interface OfflineZoneAdPositionService {

    /**
     * 查询广告展位列表, 只返回上架状态的广告展位.
     *
     * @param request 请求参数
     * @return 广告展位列表
     */
    Result<List<ListAdPositionBean>> listAdPosition(@Valid RequestListAdPosition request);

    // ------------------ 方法00, listAdPosition ------------------
}
