package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service;

import java.util.Map;
import java.util.Set;

import javax.validation.Valid;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZoneProtectionWithStatusBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestAddAgreementValidity;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestBatchResetAgreementValidity;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestGetProtectionSupportInfo;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestPlayerHandleAgreement;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestSubmitAgreement;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ProtectionDetailResponse;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response.ProtectionSupportInfoResponse;

/**
 * 线下专区-主播跳槽保护协议服务
 * <AUTHOR>
 */
public interface OfflineZoneProtectionService {


    /**
     * 提交跳槽保护协议
     */
    Result<Void> submitAgreement(@Valid RequestSubmitAgreement request);

    /**
     * 主播处理跳槽保护协议（同意或拒绝）
     */
    Result<Void> playerHandleAgreement(@Valid RequestPlayerHandleAgreement request);

    /**
     * 跳槽保护-获取协议内容
     * @param id 协议ID
     * @return 协议详情
     */
    Result<ProtectionDetailResponse> getDetail(Long id);

    /**
     * 跳槽保护-主播认证协议支撑信息
     * @return 支撑信息
     */
    Result<ProtectionSupportInfoResponse> getSupportInfo(RequestGetProtectionSupportInfo request);

    /**
     * 批量查询主播的跳槽保护状态
     *
     * @param appId 应用ID
     * @param familyId 公会ID
     * @param njId 厅ID
     * @param playerIds 主播ID集合
     * @return 主播保护状态映射结果
     */
    Result<Map<Long, OfflineZoneProtectionWithStatusBean>> batchGetProtectionStatus(
            Integer appId, Long familyId, Long njId, Set<Long> playerIds);


    /**
     * 批量重置协议有效期
     */
    Result<Void> batchResetAgreementValidity(RequestBatchResetAgreementValidity request);

    /**
     * 新增跳槽保护协议时效记录
     * 用于签约成功时自动创建协议时效数据
     */
    Result<Void> addAgreementValidity(@Valid RequestAddAgreementValidity request);

    /**
     * 提交跳槽保护协议失败,参数错误
     */
    int SUBMIT_AGREEMENT_PARAM_ERROR = 2520001;

    /**
     * 提交跳槽保护协议失败,协议已存在
     */
    int SUBMIT_AGREEMENT_EXIST = 2520002;

    /**
     * 提交跳槽保护协议失败,保存协议失败
     */
    int SUBMIT_AGREEMENT_FAIL = 2520003;

    /**
     * 提交跳槽保护协议失败,协议已过期
     */
    int SUBMIT_AGREEMENT_EXPIRED = 2520004;

    /**
     * 提交跳槽保护协议失败,协议不存在
     */
    int SUBMIT_AGREEMENT_NOT_EXIST = 2520005;

    /**
     * 主播处理协议失败,参数错误
     */
    int PLAYER_HANDLE_PARAM_ERROR = 2520101;

    /**
     * 主播处理协议失败,协议不存在
     */
    int PLAYER_HANDLE_PROTECTION_NOT_FOUND = 2520102;

    /**
     * 主播处理协议失败,协议已处理
     */
    int PLAYER_HANDLE_ALREADY_PROCESSED = 2520103;

    /**
     * 主播处理协议失败,处理失败
     */
    int PLAYER_HANDLE_FAIL = 2520104;

    /**
     * 主播处理协议失败,协议已过期
     */
    int PLAYER_HANDLE_EXPIRED = 2520105;

    /**
     * 获取协议详情失败-协议不存在
     */
    int GET_DETAIL_NOT_FOUND = 2520201;

    /**
     * 获取协议详情失败-参数错误
     */
    int GET_DETAIL_PARAM_ERROR = 2520202;

    /**
     * 获取支撑信息失败-参数错误
     */
    int GET_SUPPORT_INFO_PARAM_ERROR = 2520301;

    /**
     * 获取支撑信息失败,主播不存在
     */
    int GET_SUPPORT_INFO_PLAYER_NOT_FOUND = 2520302;

    /**
     * 批量获取保护状态失败
     */
    int BATCH_GET_PROTECTION_STATUS_FAIL = 2520401;

    /**
     * 批量获取保护状态参数错误
     */
    int BATCH_GET_PROTECTION_STATUS_PARAM_ERROR = 2520402;

    /**
     * 批量重置协议有效期参数错误
     */
    int BATCH_RESET_AGREEMENT_VALIDITY_PARAM_ERROR = 2520501;

    /**
     * 批量重置协议有效期失败
     */
    int BATCH_RESET_AGREEMENT_VALIDITY_FAIL = 2520502;

    /**
     * 新增协议时效记录参数错误
     */
    int ADD_AGREEMENT_VALIDITY_PARAM_ERROR = 2520601;

    /**
     * 新增协议时效记录失败
     */
    int ADD_AGREEMENT_VALIDITY_FAIL = 2520602;

    /**
     * 新增协议时效记录已存在
     */
    int ADD_AGREEMENT_VALIDITY_EXIST = 2520603;

}
