package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.api.common.service.CommonService;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListLearningClassByTypeBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestListLearningClassByType;

import javax.validation.Valid;
import java.util.List;

/**
 * 学习课堂服务, 前接口错误码以251开头.
 * <ul>
 *     <li>错误码规范参考<a href="https://lizhi2021.feishu.cn/wiki/Kulywk1KliQVGTktY56cny01nvg#JYrkdVrauo2bhbx0IeCcYSjsnMh">错误码规范</a></li>
 *     <li>状态码请维护在<a href="https://lizhi2021.feishu.cn/wiki/Ld0Rw5uvGiUimnkgez5cSVJ9n1c">创作者平台状态码维护文档</a></li>
 *     <li>通用错误码请见{@link CommonService}</li>
 * </ul>
 */
public interface OfflineZoneLearningClassService {

    /**
     * 根据类型查询学习课堂列表, 只查询上架状态的
     *
     * @param request 请求参数
     * @return 学习课堂列表
     */
    Result<List<ListLearningClassByTypeBean>> listLearningClassByType(@Valid RequestListLearningClassByType request);

    // ------------------ 方法00, listLearningClassByType ------------------
}
