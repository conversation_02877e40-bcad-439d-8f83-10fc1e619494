package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 线下专区主播跳槽保护协议
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class OfflineZoneProtectionBean {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 应用ID
     */
    private Long appId;

    /**
     * 公会ID
     */
    private Long familyId;

    /**
     * 上传用户 ID
     */
    private Long uploadUserId;

    /**
     * 厅ID
     */
    private Long njId;

    /**
     * 主播ID
     */
    private Long playerId;

    /**
     * 协议生效开始时间
     */
    private Date agreementStartTime;

    /**
     * 协议生效结束时间
     */
    private Date agreementEndTime;

    /**
     * 协议更新时间
     */
    private Date agreementUpdateTime;

    /**
     * 是否盖章签字：0-否，1-是
     */
    private Boolean stampSign;

    /**
     * 主播同意状态：-1-未处理，0-不同意，1-同意
     */
    private Integer playerAgree;

    /**
     * 是否归档：0-否，1-是
     */
    private Boolean archived;

    /**
     * 环境：TEST/PRE/PRO
     */
    private String deployEnv;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;

    /**
     * 协议文件JSON
     */
    private String agreementFileJson;
    /**
     * 是否有效（当前时间在协议有效期内）
     */
    private Boolean isValid;
}
