package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request;

import fm.lizhi.ocean.wave.server.common.request.RequestAppIdAware;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 主播处理跳槽保护协议请求
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestPlayerHandleAgreement implements RequestAppIdAware {

    /**
     * 协议ID
     */
    @NotNull(message = "协议ID不能为空")
    private Long protectionId;

    /**
     * 应用ID
     */
    @NotNull(message = "应用ID不能为空")
    private int appId;

    /**
     * 主播 ID
     */
    @NotNull(message = "主播ID不能为空")
    private Long playerId;

    /**
     * 同意状态：1-同意，0-拒绝
     */
    @NotNull(message = "处理状态不能为空")
    private Integer agreeStatus;

    /**
     * 是否校验协议有效期
     */
    private Boolean verifyValidityDate = true;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
