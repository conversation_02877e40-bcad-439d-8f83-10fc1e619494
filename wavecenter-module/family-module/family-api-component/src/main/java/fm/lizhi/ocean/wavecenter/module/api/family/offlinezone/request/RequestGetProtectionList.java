package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request;

import fm.lizhi.ocean.wave.server.common.request.RequestAppIdAware;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 获取保护协议列表请求
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestGetProtectionList implements RequestAppIdAware {

    @NotNull(message = "appId不能为空")
    private Long appId;

    /**
     * 家族ID
     */
    private Long familyId;

    /**
     * 厅主ID
     */
    private Long njId;

    /**
     * 主播ID
     */
    private Long playerId;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 页大小
     */
    private Integer pageSize;

    @Override
    public Integer foundIdAppId() {
        return appId != null ? appId.intValue() : null;
    }
}
