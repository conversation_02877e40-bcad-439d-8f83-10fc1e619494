package fm.lizhi.ocean.wavecenter.module.api.family.report.request;

import fm.lizhi.ocean.wave.server.common.request.RequestAppIdAware;
import fm.lizhi.ocean.wave.server.common.validator.AppEnumId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 提交举报申请请求
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PageRoomViolationRecordRequest implements RequestAppIdAware {

    /**
     * 应用ID
     */
    @NotNull(message = "应用ID不能为空")
    @AppEnumId
    private Integer appId;
    @NotNull(message = "当前页码不能为空")
    private Integer pageNo;
    @NotNull(message = "每页数量不能为空")
    private Integer pageSize;

    /**
     * 开始时间毫秒时间戳, 包含
     */
    @NotNull(message = "开始时间不能为空")
    private Long startDate;

    /**
     * 结束时间毫秒时间戳, 包含
     */
    @NotNull(message = "结束时间不能为空")
    private Long endDate;

    /**
     * 家族ID. 如果是主播视角或厅主视角则不传, 如果是家族长视角或高级管理视角则需要传家族ID
     */
    private Long accusedFamilyId;

    /**
     * 指定挖槽厅房间ID
     */
    private Long accusedRoomId;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}