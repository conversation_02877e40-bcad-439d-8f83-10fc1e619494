package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 主播跳槽保护状态枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ProtectionStatusEnums {

    /**
     * 保护状态值
     * 0: 未上传 - 跳槽保护表不存在数据
     * 1: 已上传，待主播确认 - player_agree = -1 && archived = false
     * 2: 已上传，主播逾期未确认 - player_agree = -1 && archived = true
     * 3: 主播确认 - player_agree = 1 && archived = true
     * 4: 主播拒绝 - player_agree = 0 && archived = true
     * 5: 上传逾期 - offline_zone_protection_agreement_validity.expired_time <= 当前时间
     */

    /**
     * 未上传
     */
    NOT_UPLOADED(0, "未上传"),

    /**
     * 已上传，待主播确认
     */
    UPLOADED_PENDING_CONFIRMATION(1, "已上传，待主播确认"),

    /**
     * 已上传，主播逾期未确认
     */
    UPLOADED_OVERDUE_CONFIRMATION(2, "已上传，主播逾期未确认"),

    /**
     * 主播确认
     */
    PLAYER_CONFIRMED(3, "主播确认"),

    /**
     * 主播拒绝
     */
    PLAYER_REJECTED(4, "主播拒绝"),

    /**
     * 上传逾期
     */
    UPLOAD_EXPIRED(5, "上传逾期");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举值
     */
    public static ProtectionStatusEnums getByCode(Integer code) {
        if (code == null) {
            return NOT_UPLOADED;
        }
        
        for (ProtectionStatusEnums status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return NOT_UPLOADED;
    }
}
