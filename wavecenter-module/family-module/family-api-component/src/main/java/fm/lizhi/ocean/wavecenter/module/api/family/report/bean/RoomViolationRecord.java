package fm.lizhi.ocean.wavecenter.module.api.family.report.bean;

import lombok.Data;

/**
 * 厅举报处理结果记录
 */
@Data
public class RoomViolationRecord {

    /**
     * 被封禁的厅
     */
    private AccusedRoom accusedRoom;


    /**
     * 操作时长;eg:封禁天数
     */
    private Integer operateDuration;

    /**
     * 操作类型(1：封禁 2 解封)
     */
    private String operateType;

    /**
     * 操作对象类型(1：账号，2：厅开播权限 3设备)
     */
    private String operateObjType;
}
