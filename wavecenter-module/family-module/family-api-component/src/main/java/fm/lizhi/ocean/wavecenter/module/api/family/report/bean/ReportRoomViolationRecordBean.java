package fm.lizhi.ocean.wavecenter.module.api.family.report.bean;

import fm.lizhi.ocean.wavecenter.api.user.bean.UserBean;
import lombok.Data;

import java.util.Date;

@Data
public class ReportRoomViolationRecordBean {
    private Long id;

    private Integer appId;

    /**
     * 举报类型，6:大小号跳槽，7:原号跳槽
     */
    private Integer reportType;

    /**
     * 被举报者
     */
    private UserBean accusedUser;

    /**
     * 被举报厅ID-挖槽厅主信息
     */
    private UserBean accusedNjUser;

    /**
     * 被举报的厅所属公会-挖槽厅公会
     */
    private Long accusedFamilyId;

    /**
     * 创建时间-违规时间
     */
    private Date createTime;

}
