package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response;

import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZoneMetricsDataBean;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ResponseDataMonitorFamilySummary {

    /**
     * 基地数
     */
    private OfflineZoneMetricsDataBean basicCnt;

    /**
     * 线下厅数
     */
    private OfflineZoneMetricsDataBean offlineHallCnt;

    /**
     * 线下主播数
     */
    private OfflineZoneMetricsDataBean offlinePlayerCnt;

    /**
     * 线下厅数占比
     */
    private OfflineZoneMetricsDataBean offlineHallCntRate;

    /**
     * 线下主播数占比
     */
    private OfflineZoneMetricsDataBean offlinePlayerCntRate;

    /**
     * 线下厅收入
     */
    private OfflineZoneMetricsDataBean offlineHallIncome;

    /**
     * 线下厅收入占比
     */
    private OfflineZoneMetricsDataBean offlineHallIncomeRate;

    /**
     * 受保护主播数
     */
    private OfflineZoneMetricsDataBean protectedPlayerCnt;

    /**
     * 受保护主播数占比
     */
    private OfflineZoneMetricsDataBean protectedPlayerCntRate;

}
