package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean;

import com.ctrip.framework.apollo.core.enums.Env;
import lombok.Data;

import java.util.List;

/**
 * 列出线下专区等级配置结果bean
 */
@Data
public class ListLevelConfigBean {

    /**
     * 等级ID
     */
    private Long id;

    /**
     * 等级key, 用于前端做UI关联
     */
    private String levelKey;

    /**
     * 等级名称
     */
    private String levelName;

    /**
     * 等级顺序（数字越高等级越高）
     */
    private Integer levelOrder;

    /**
     * 部署环境
     *
     * @see Env#name()
     */
    private String deployEnv;

    /**
     * 是否已删除
     */
    private Boolean deleted;

    /**
     * 操作者
     */
    private String operator;

    /**
     * 创建时间, 毫秒时间戳
     */
    private Long createTime;

    /**
     * 修改时间, 毫秒时间戳
     */
    private Long modifyTime;

    /**
     * 等级权益关联列表
     */
    private List<Right> rights;

    /**
     * 等级权益关联信息
     */
    @Data
    public static class Right {

        /**
         * 权益ID
         */
        private Long rightId;

        /**
         * 是否解锁, true表示当前等级可以获得该权益
         */
        private Boolean unlocked;

        /**
         * 解锁等级ID, 当unlocked为false时有值
         */
        private Long unlockedLevelId;
    }
}
