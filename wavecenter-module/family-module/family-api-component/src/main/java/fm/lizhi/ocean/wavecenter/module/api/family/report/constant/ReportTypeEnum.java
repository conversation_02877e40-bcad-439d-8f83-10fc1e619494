package fm.lizhi.ocean.wavecenter.module.api.family.report.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@AllArgsConstructor
@Getter
public enum ReportTypeEnum {
    /**
     * 谩骂
     */
    BAD_WORD(1, "谩骂"),
    /**
     * 低俗
     */
    LOW(2, "低俗"),
    /**
     * 广告
     */
    AD(3, "广告"),
    /**
     * 敏感
     */
    SENSITIVE(4, "敏感"),
    /**
     * 其他
     */
    OTHER(5, "其他"),
    /**
     * 大小号跳槽
     */
    JOB_HOPPING(6, "大小号跳槽"),
    /**
     * 原号跳槽
     */
    ORG_JOB_HOPPING(7, "原号跳槽");

    private final Integer code;
    private final String description;

    /**
     * 获取举报类型枚举列表
     */
    public static List<ReportTypeEnum> getReportTypeList() {
        return Arrays.asList(values());
    }

    /**
     * 根据code获取枚举
     */
    public static ReportTypeEnum getReportType(Integer code) {
        for (ReportTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

}
