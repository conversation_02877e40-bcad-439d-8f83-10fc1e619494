
package fm.lizhi.ocean.wavecenter.module.api.family.report.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 举报结果状态枚举类
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ReportApplyStatusEnum {

    /**
     * 待处理
     */
    PENDING(0, "待处理"),

    /**
     * 成功
     */
    SUCCESS(1, "成功"),

    /**
     * 失败
     */
    FAILED(2, "失败");

    private final int status;
    private final String desc;


    /**
     * 根据code获取对应的枚举值
     *
     * @param code 状态码
     * @return 对应的枚举值，找不到返回null
     */
    public static ReportApplyStatusEnum from(int code) {
        for (ReportApplyStatusEnum status : ReportApplyStatusEnum.values()) {
            if (status.getStatus() == code) {
                return status;
            }
        }
        return null;
    }
}