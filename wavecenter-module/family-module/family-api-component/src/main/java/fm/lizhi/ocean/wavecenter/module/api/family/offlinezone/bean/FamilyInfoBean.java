package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 公会信息Bean
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class FamilyInfoBean {
    /**
     * 公会ID
     */
    private Long id;

    /**
     * 公会名称
     */
    private String name;

    /**
     * 认证类型：1=认证通过，2=未认证
     */
    private Integer authType;

    /**
     * 认证企业
     */
    private String authCompany;

    /**
     * 家族长波段号
     */
    private String familyUserBand;

    /**
     * 家族长名称
     */
    private String familyUserName;

    /**
     * 公会头像url
     */
    private String familyPhotoUrl;

    /**
     * 创建时间
     */
    private Long createTime;
}
