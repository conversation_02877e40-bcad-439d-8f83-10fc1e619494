package fm.lizhi.ocean.wavecenter.module.api.family.report.request;

import fm.lizhi.ocean.wave.server.common.request.RequestAppIdAware;
import fm.lizhi.ocean.wave.server.common.validator.AppEnumId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 提交举报申请请求
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SubmitReportApplyRequest implements RequestAppIdAware {

    /**
     * 应用ID
     */
    @NotNull(message = "应用ID不能为空")
    @AppEnumId
    private Integer appId;

    /**
     * 举报类型
     */
    @NotNull(message = "举报类型不能为空")
    private Integer reportType;

    /**
     * 举报者用户ID
     */
    @NotNull(message = "举报者用户ID不能为空")
    private Long reportUserId;

    /**
     * 被举报者用户ID
     */
    @NotNull(message = "被举报者用户ID不能为空")
    private Long accusedUserId;


    /**
     * 被举报者大号用户ID
     */
    private Long accusedMainUserId;

    /**
     * 被举报厅ID-挖槽厅
     */
    @NotNull(message = "被举报厅ID不能为空")
    private Long accusedRoomId;

    /**
     * 举报来源 web/pc
     */
    @NotNull(message = "来源")
    private String source;




    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}