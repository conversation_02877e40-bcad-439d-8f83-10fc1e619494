package fm.lizhi.ocean.wavecenter.module.api.family.report.bean;

import lombok.Data;
import java.util.List;

/**
 * 提交举报申请响应
 */
@Data
public class SubmitReportApplyResponse {


    /**
     * 举报结果状态，1：成功，2：失败
     */
    private Integer reportStatus;
    
    /**
     * 失败原因
     */
    private String failReason;
    
    /**
     * 主播举报处理结果
     */
    private List<PlayerReportApplyOperateResultBean> playerReportApplyOperateResult;
    
    /**
     * 厅举报处理结果
     */
    private RoomViolationRecord roomViolationRecord;

}