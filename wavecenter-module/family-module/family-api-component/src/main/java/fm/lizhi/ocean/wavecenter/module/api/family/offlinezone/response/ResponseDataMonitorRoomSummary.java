package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response;

import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZoneMetricsDataBean;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ResponseDataMonitorRoomSummary {



    /**
     * 线下主播数
     */
    private OfflineZoneMetricsDataBean offlinePlayerCnt;

    /**
     * 线下主播数占比
     */
    private OfflineZoneMetricsDataBean offlinePlayerCntRate;

    /**
     * 线下厅收入
     */
    private OfflineZoneMetricsDataBean income;

    /**
     * 受保护主播数
     */
    private OfflineZoneMetricsDataBean protectedPlayerCnt;

    /**
     * 受保护主播数占比
     */
    private OfflineZoneMetricsDataBean protectedPlayerCntRate;

    /**
     * 线下主播收入
     */
    private OfflineZoneMetricsDataBean offlinePlayerIncome;

    /**
     * 线下主播收入占比
     */
    private OfflineZoneMetricsDataBean offlinePlayerIncomeRate;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;


}
