package fm.lizhi.ocean.wavecenter.module.api.family.report.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.module.api.family.report.bean.ManualPlayerOperateResponse;
import fm.lizhi.ocean.wavecenter.module.api.family.report.bean.ManualRoomOperateResponse;
import fm.lizhi.ocean.wavecenter.module.api.family.report.bean.SubmitReportApplyResponse;
import fm.lizhi.ocean.wavecenter.module.api.family.report.request.ManualPlayerOperateRequest;
import fm.lizhi.ocean.wavecenter.module.api.family.report.request.ManualRoomOperateRequest;
import fm.lizhi.ocean.wavecenter.module.api.family.report.request.SubmitReportApplyRequest;

import javax.validation.Valid;

/**
 * 玩家跳槽举报服务
 * <ul>
 *     <li>错误码规范参考<a href="https://lizhi2021.feishu.cn/wiki/Kulywk1KliQVGTktY56cny01nvg#JYrkdVrauo2bhbx0IeCcYSjsnMh">错误码规范</a></li>
 *     <li>状态码请维护在<a href="https://lizhi2021.feishu.cn/wiki/Ld0Rw5uvGiUimnkgez5cSVJ9n1c">创作者平台状态码维护文档</a></li>
 * </ul>
 */
public interface PlayerJobHopReportService {


    /**
     * 提交举报申请
     *
     * @param request 请求参数
     * @return 响应结果
     */
    Result<SubmitReportApplyResponse> submitReportApply(@Valid SubmitReportApplyRequest request);


    Result<ManualPlayerOperateResponse> manualPlayerOperate(ManualPlayerOperateRequest request);


    Result<ManualRoomOperateResponse> manualRoomOperate(ManualRoomOperateRequest request);


    int SUBMIT_REPORT_APPLY_LOCK_FAIL = 2600005;


    /**
     * 举报提交过于频繁
     */
    int REPORT_ALREADY_LIMIT = 2600001;
    
    /**
     * 举报房间重复
     */
    int REPORT_ROOM_REPEAT = 2600002;

    /**
     * 跳槽厅跟公会解约了
     */
    int ACCUSED_ROOM_UN_SIGN = 2600003;

    /**
     * 跳槽厅波段号错误
     */
    int ACCUSED_ROOM_BAND_ILLEGAL = 2600004;

    int REPORT_TYPE_NO_EXIST = 2600005;

}