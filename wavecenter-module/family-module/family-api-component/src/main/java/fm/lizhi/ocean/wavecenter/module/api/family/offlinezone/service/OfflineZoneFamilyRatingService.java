
package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.ListLevelConfigBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.SimpleLevelBean;

/**
 * 线下专区-公会评级
 * <AUTHOR>
 */
public interface OfflineZoneFamilyRatingService {

    /**
     * 获取公会评级信息 - 带默认值
     */
    Result<SimpleLevelBean> getFamilyLevelWithDefault(int appId, Long familyId);


}

