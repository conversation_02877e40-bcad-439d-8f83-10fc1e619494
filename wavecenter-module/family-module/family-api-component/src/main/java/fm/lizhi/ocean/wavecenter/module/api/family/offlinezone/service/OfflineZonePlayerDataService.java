package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.OfflineZonePlayerDataBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request.RequestGetPlayerDataList;

import javax.validation.Valid;

/**
 * 线下专区-主播数据服务
 * <AUTHOR>
 */
public interface OfflineZonePlayerDataService {

    /**
     * 获取主播数据列表
     * 
     * @param request 请求参数
     * @return 分页主播数据列表
     */
    Result<PageBean<OfflineZonePlayerDataBean>> getPlayerDataList(@Valid RequestGetPlayerDataList request);

    /**
     * 获取主播数据列表失败
     */
    int GET_PLAYER_DATA_LIST_FAIL = 2490011;

    /**
     * 获取主播数据列表参数错误
     */
    int GET_PLAYER_DATA_LIST_PARAM_ERROR = 2490012;
}
