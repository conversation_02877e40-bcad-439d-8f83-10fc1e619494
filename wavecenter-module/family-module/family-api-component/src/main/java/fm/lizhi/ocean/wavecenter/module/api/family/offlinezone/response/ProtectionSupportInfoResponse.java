package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.response;

import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.PlayerInfoBean;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.FamilyInfoBean;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 跳槽保护-主播认证协议支撑信息响应
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ProtectionSupportInfoResponse {

    /**
     * 告示内容
     */
    private String notice;

    /**
     * 主播信息
     */
    private PlayerInfoBean playerInfo;

    /**
     * 公会信息
     */
    private FamilyInfoBean familyInfo;


}
