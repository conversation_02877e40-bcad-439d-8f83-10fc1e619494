package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 地图数据-厅
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class MapDataRoomBean {

    /**
     * id
     */
    private Long njId;
    /**
     * 昵称
     */
    private String njName;
    /**
     * 波段号
     */
    private String njBand;

    /**
     * 头像
     */
    private String photo;

    /**
     * 线下主播数
     */
    private Long offlinePlayerCnt;
}
