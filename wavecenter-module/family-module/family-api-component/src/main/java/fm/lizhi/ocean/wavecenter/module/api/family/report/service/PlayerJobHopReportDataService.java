package fm.lizhi.ocean.wavecenter.module.api.family.report.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.bean.PageBean;
import fm.lizhi.ocean.wavecenter.module.api.family.report.bean.ReportRoomViolationRecordBean;
import fm.lizhi.ocean.wavecenter.module.api.family.report.request.PageRoomViolationRecordRequest;
import fm.lizhi.ocean.wavecenter.module.api.family.report.request.SubmitReportApplyRequest;

import javax.validation.Valid;

/**
 * 玩家跳槽举报数据服务
 * <ul>
 *     <li>错误码规范参考<a href="https://lizhi2021.feishu.cn/wiki/Kulywk1KliQVGTktY56cny01nvg#JYrkdVrauo2bhbx0IeCcYSjsnMh">错误码规范</a></li>
 *     <li>状态码请维护在<a href="https://lizhi2021.feishu.cn/wiki/Ld0Rw5uvGiUimnkgez5cSVJ9n1c">创作者平台状态码维护文档</a></li>
 * </ul>
 */
public interface PlayerJobHopReportDataService {


    /**
     * 获取厅违规信息
     * report_room_violation_record
     *
     * @param request 请求参数
     * @return 响应结果
     */
    Result<PageBean<ReportRoomViolationRecordBean>> pageRoomViolationRecord(@Valid PageRoomViolationRecordRequest request);


    /**
     * 参数错误
     */
    int PAGE_ROOM_VIOLATION_RECORD_PARAM_ERR = 2610001;


}