package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request;

import fm.lizhi.ocean.wave.server.common.request.RequestAppIdAware;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 新增跳槽保护协议时效记录请求
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestAddAgreementValidity implements RequestAppIdAware {

    /**
     * 应用ID
     */
    @NotNull(message = "appId不能为空")
    private Integer appId;

    /**
     * 公会ID
     */
    @NotNull(message = "公会ID不能为空")
    private Long familyId;

    /**
     * 厅ID
     */
    @NotNull(message = "厅ID不能为空")
    private Long njId;

    /**
     * 主播ID
     */
    @NotNull(message = "主播ID不能为空")
    private Long playerId;

    /**
     * 签约时间
     */
    @NotNull(message = "签约时间不能为空")
    private Date beginSignTime;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}