package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request;

import fm.lizhi.ocean.wave.server.common.request.RequestAppIdAware;
import fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean.NeedResetPlayer;
import lombok.Data;

import java.util.List;

/**
 * 批量重置协议有效期请求
 * <AUTHOR>
 */
@Data
public class RequestBatchResetAgreementValidity implements RequestAppIdAware {


    private int appId;

    private List<NeedResetPlayer> playerList;

    private String operator;


    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
