
package fm.lizhi.ocean.wavecenter.module.api.family.report.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作状态枚举类
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ReportOperateStatusEnum {

    /**
     * 待执行
     */
    WAIT_OPERATE(0),

    /**
     * 成功
     */
    SUCCESS(1),

    /**
     * 失败
     */
    FAIL(2),

    ;
    private final Integer code;


    //根据code获取枚举
    public static ReportOperateStatusEnum getByCode(Integer code) {
        for (ReportOperateStatusEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}