package fm.lizhi.ocean.wavecenter.module.api.family.report.request;

import fm.lizhi.ocean.wave.server.common.request.RequestAppIdAware;
import fm.lizhi.ocean.wave.server.common.validator.AppEnumId;
import fm.lizhi.ocean.wavecenter.module.api.family.report.constant.OperateDurationTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 手动操作处理
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ManualRoomOperateRequest implements RequestAppIdAware {

    /**
     * 应用ID
     */
    @NotNull(message = "应用ID不能为空")
    @AppEnumId
    private Integer appId;


    @NotNull(message = "厅操作id不能为空")
    private Long reportRoomViolationOperateResultId;

    @NotNull(message = "操作时间不能为空")
    private OperateDurationTypeEnum operateDuration;

    @NotNull(message = "操作人ID不能为空")
    private String operator;
    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}