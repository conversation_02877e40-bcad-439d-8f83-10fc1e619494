package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request;

import fm.lizhi.ocean.wave.server.common.request.RequestAppIdAware;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class RequestDataMonitorFamilySummary implements RequestAppIdAware {


    @NotNull(message = "appId不能为空")
    private Integer appId;

    /**
     * 家族id
     */
    @NotNull(message = "家族ID不能为空")
    private Long familyId;


    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
