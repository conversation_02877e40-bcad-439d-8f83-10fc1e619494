package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.bean;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 跳槽保护协议与状态组合Bean
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class OfflineZoneProtectionWithStatusBean {
    
    /**
     * 主播ID
     */
    private Long playerId;
    
    /**
     * 保护状态值
     * 0: 未上传 - 跳槽保护表不存在数据
     * 1: 已上传，待主播确认 - player_agree = -1 && archived = false
     * 2: 已上传，主播逾期未确认 - player_agree = -1 && archived = true
     * 3: 主播确认 - player_agree = 1 && archived = true
     * 4: 主播拒绝 - player_agree = 0 && archived = true
     * 5: 上传逾期 - offline_zone_protection_agreement_validity.expired_time <= 当前时间
     * @see fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.enums.ProtectionStatusEnum
     */
    private Integer protectionStatus;

    /**
     * 是否受保护
     */
    private Boolean protection;
    
    /**
     * 保护协议详情（可能为null，当状态为未上传时）
     */
    private OfflineZoneProtectionBean protectionDetail;
}