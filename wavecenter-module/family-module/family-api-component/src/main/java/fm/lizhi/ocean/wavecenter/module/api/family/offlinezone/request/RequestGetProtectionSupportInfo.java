package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.request;

import fm.lizhi.ocean.wave.server.common.request.RequestAppIdAware;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RequestGetProtectionSupportInfo implements RequestAppIdAware {

    @NotNull(message = "appId不能为空")
    private int appId;

    @NotNull(message = "家族ID不能为空")
    private Long familyId;

    @NotNull(message = "njId不能为空")
    private Long njId;

    @NotNull(message = "主播不能为空")
    private Long playerId;

    @Override
    public Integer foundIdAppId() {
        return appId;
    }
}
