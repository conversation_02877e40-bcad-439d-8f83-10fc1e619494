package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants;

import lombok.Getter;

/**
 * 线下专区-主播类型枚举
 * <AUTHOR>
 */
@Getter
public enum OfflineZonePlayerCategoryEnums {
    /**
     * 线下主播
     */
    OFFLINE_PLAYER(0),

    /**
     * 线上主播
     */
    ONLINE_PLAYER(1)
    ;


    private final int category;

    OfflineZonePlayerCategoryEnums(int category) {
        this.category = category;
    }
}
