package fm.lizhi.ocean.wavecenter.module.api.family.offlinezone.constants;

import lombok.Getter;

/**
 * 主播跳槽保护协议同意状态枚举
 * <AUTHOR>
 */
@Getter
public enum ProtectionAgreeStatusEnums {

    /**
     * 未处理
     */
    NOT_PROCESSED(-1),

    /**
     * 同意
     */
    AGREED(1),

    /**
     * 拒绝
     */
    REJECTED(0);

    /**
     * 状态码
     */
    private final Integer code;

    ProtectionAgreeStatusEnums(Integer code) {
        this.code = code;
    }

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举值
     */
    public static ProtectionAgreeStatusEnums getByCode(Integer code) {
        if (code == null) {
            return NOT_PROCESSED;
        }

        for (ProtectionAgreeStatusEnums status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return NOT_PROCESSED;
    }


}
