package fm.lizhi.ocean.wavecenter.datastore.grow.capability.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class is automatically generated by Datastore MyBatis Generator, do not modify.
 *
 * 系统能力项
 *
 * @date 2025-06-06 09:22:47
 */
@Table(name = "`wavecenter_grow_capability`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WcGrowCapability {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 名称
     */
    @Column(name= "`name`")
    private String name;

    /**
     * 能力项code
     */
    @Column(name= "`capability_code`")
    private String capabilityCode;

    /**
     * 状态:0=停用 1=启用 2=下周
     */
    @Column(name= "`status`")
    private Integer status;

    /**
     * 业务 ID
     */
    @Column(name= "`app_id`")
    private Integer appId;

    /**
     * 服务部署环境, TEST/PRE/PRO
     */
    @Column(name= "`deploy_env`")
    private String deployEnv;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    /**
     * 创建人
     */
    @Column(name= "`create_user`")
    private String createUser;

    /**
     * 最近操作人
     */
    @Column(name= "`modify_user`")
    private String modifyUser;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", capabilityCode=").append(capabilityCode);
        sb.append(", status=").append(status);
        sb.append(", appId=").append(appId);
        sb.append(", deployEnv=").append(deployEnv);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append(", createUser=").append(createUser);
        sb.append(", modifyUser=").append(modifyUser);
        sb.append("]");
        return sb.toString();
    }
}