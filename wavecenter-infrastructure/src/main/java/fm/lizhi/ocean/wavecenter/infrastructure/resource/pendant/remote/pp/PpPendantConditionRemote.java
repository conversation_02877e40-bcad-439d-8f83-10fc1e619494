package fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.remote.pp;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.base.RpcResult;
import fm.lizhi.ocean.wavecenter.infrastructure.resource.pendant.remote.PendantConditionRemote;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.ConditionAddParamDTO;
import fm.lizhi.ocean.wavecenter.service.resource.pendant.dto.ConditionUpdateParamDTO;
import fm.lizhi.pp.content.assistant.api.ConditionService;
import fm.lizhi.pp.content.assistant.constants.DataConstants;
import fm.lizhi.pp.content.assistant.protocol.ConditionServiceProto.ConditionProbuf;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Collections;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class PpPendantConditionRemote implements PendantConditionRemote {

    @Autowired
    private ConditionService conditionService;

    private static final String NOT_LIMIT = "NOT_LIMIT";

    private static final int NOT_LIMIT_INT = -1;

    private static final String LOCATION_CN = "CN";

    private static final String PP_APPID = "PPYW";

    @Override
    public Result<Void> addCondition(ConditionAddParamDTO param) {
        ConditionProbuf conditionProbuf = ConditionProbuf.newBuilder()
                .setConfigId(param.getPendantId())
                .setNjUserGroupId(param.getNjGroupId())
                .addAllAppIdRelates(Collections.singletonList(PP_APPID))
                .addAllNjAppIdRelates(Collections.singletonList(PP_APPID))
                .setIntegralMinScore(NOT_LIMIT_INT)
                .setIntegralMaxScore(NOT_LIMIT_INT)
                .setIosBuilderStart(NOT_LIMIT_INT)
                .setIosBuilderEnd(NOT_LIMIT_INT)
                .setAndroidBuilderStart(NOT_LIMIT_INT)
                .setAndroidBuilderEnd(NOT_LIMIT_INT)
                .setWealthStart(NOT_LIMIT_INT)
                .setWealthEnd(NOT_LIMIT_INT)
                .setModels(NOT_LIMIT)
                .setOtherCondition(NOT_LIMIT)
                .setStatus(DataConstants.PendantStatus.ONLINE)
                .addAllLocations(Collections.singletonList(LOCATION_CN))
                .build();
        Result<Void> result = conditionService.addCondition(conditionProbuf);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(ADD_CONDITION_FAIL, "添加条件失败");
        }
        return RpcResult.success();
    }
    
    @Override
    public Result<Void> updateCondition(ConditionUpdateParamDTO param) {
        ConditionProbuf conditionProbuf = ConditionProbuf.newBuilder()
                .setConfigId(param.getPendantId())
                .setNjUserGroupId(param.getNjGroupId())
                .setId(param.getConditionId())
                .addAllAppIdRelates(Collections.singletonList(PP_APPID))
                .addAllNjAppIdRelates(Collections.singletonList(PP_APPID))
                .setIntegralMinScore(NOT_LIMIT_INT)
                .setIntegralMaxScore(NOT_LIMIT_INT)
                .setIosBuilderStart(NOT_LIMIT_INT)
                .setIosBuilderEnd(NOT_LIMIT_INT)
                .setAndroidBuilderStart(NOT_LIMIT_INT)
                .setAndroidBuilderEnd(NOT_LIMIT_INT)
                .setWealthStart(NOT_LIMIT_INT)
                .setWealthEnd(NOT_LIMIT_INT)
                .setModels(NOT_LIMIT)
                .setOtherCondition(NOT_LIMIT)
                .setStatus(DataConstants.PendantStatus.ONLINE)
                .addAllLocations(Collections.singletonList(LOCATION_CN))
                .build();
        Result<Void> result = conditionService.updateCondition(conditionProbuf);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(UPDATE_CONDITION_FAIL, "更新条件失败");
        }
        return RpcResult.success();
    }

    @Override
    public Result<Void> deleteCondition(long pendantId, long conditionId) {
        ConditionProbuf conditionProbuf = ConditionProbuf.newBuilder()
                .setConfigId(pendantId)
                .setId(conditionId)
                .addAllAppIdRelates(Collections.singletonList(PP_APPID))
                .addAllNjAppIdRelates(Collections.singletonList(PP_APPID))
                .setIntegralMinScore(NOT_LIMIT_INT)
                .setIntegralMaxScore(NOT_LIMIT_INT)
                .setIosBuilderStart(NOT_LIMIT_INT)
                .setIosBuilderEnd(NOT_LIMIT_INT)
                .setAndroidBuilderStart(NOT_LIMIT_INT)
                .setAndroidBuilderEnd(NOT_LIMIT_INT)
                .setWealthStart(NOT_LIMIT_INT)
                .setWealthEnd(NOT_LIMIT_INT)
                .setModels(NOT_LIMIT)
                .setOtherCondition(NOT_LIMIT)
                .addAllLocations(Collections.singletonList(LOCATION_CN))
                .setStatus(DataConstants.PendantStatus.OFFLINE)
                .build();
        Result<Void> result = conditionService.updateCondition(conditionProbuf);
        if (RpcResult.isFail(result)) {
            return RpcResult.fail(DELETE_CONDITION_FAIL, "删除条件失败");
        }
        return RpcResult.success();
    }
    
    @Override
    public BusinessEvnEnum getBusinessEvnEnum() {
        return BusinessEvnEnum.PP;
    }

    

}
