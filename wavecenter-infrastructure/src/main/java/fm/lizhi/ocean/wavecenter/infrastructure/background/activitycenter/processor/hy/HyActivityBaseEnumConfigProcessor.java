package fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.processor.hy;

import fm.lizhi.ocean.wave.server.common.constant.BusinessEvnEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.AutoConfigResourceEnum;
import fm.lizhi.ocean.wavecenter.api.activitycenter.constants.RewardTagEnum;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityAutoConfigResourceBean;
import fm.lizhi.ocean.wavecenter.api.background.activitycenter.bean.ActivityResourceEnumBean;
import fm.lizhi.ocean.wavecenter.infrastructure.background.activitycenter.processor.IActivityBaseEnumConfigProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 小陪伴(HEI_YE)业务的活动基础枚举配置处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class HyActivityBaseEnumConfigProcessor implements IActivityBaseEnumConfigProcessor {

    @Override
    public List<ActivityAutoConfigResourceBean> filterAutoConfigResourceList(List<ActivityAutoConfigResourceBean> resourceBeans) {
        // 小陪伴过滤掉节目单资源
        return resourceBeans.stream()
                .filter(bean -> !AutoConfigResourceEnum.PROGRAMME.getResourceCode().equals(bean.getResourceCode()))
                .collect(Collectors.toList());
    }

    @Override
    public List<ActivityResourceEnumBean> buildActivityRewardTagConfigBeanList() {
        // 小陪伴支持所有奖励标签，包括福袋
        return Arrays.stream(RewardTagEnum.values())
                .map(activityRewardTagEnum -> new ActivityResourceEnumBean()
                        .setName(activityRewardTagEnum.getName())
                        .setCode(activityRewardTagEnum.getCode())
                )
                .collect(Collectors.toList());
    }

    @Override
    public BusinessEvnEnum getBusinessEnv() {
        return BusinessEvnEnum.HEI_YE;
    }
}
